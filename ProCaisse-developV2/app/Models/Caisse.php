<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property SessionCaisse[] $sessionCaisses
 * @property string $CAI_IdCaisse
 * @property string $CAI_DesCaisse
 * @property string $CAI_Station
 * @property string $CAI_UserStation
 * @property string $CAI_User
 * @property boolean $CAI_export
 * @property string $CAI_DDm
 */
class Caisse extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'Caisse';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'CAI_IdCaisse';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    protected $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['CAI_DesCaisse', 'CAI_Station', 'CAI_UserStation', 'CAI_User', 'CAI_export', 'CAI_DDm'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function sessionCaisses()
    {
        return $this->hasMany('App\Models\SessionCaisse', 'SC_Caisse', 'CAI_IdCaisse');
    }
}
