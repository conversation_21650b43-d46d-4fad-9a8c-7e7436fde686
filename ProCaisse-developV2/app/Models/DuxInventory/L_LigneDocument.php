<?php

namespace App\Models\DuxInventory;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class L_LigneDocument extends Model
{
    protected $connection = 'onthefly';
    public $incrementing = false;
    protected $table = 'L_LigneDocument';
    public $timestamps = false;
    protected $guarded = [];

    public static function defaultFields()
    {
        return [
            'L_LigneDocument.id', 'L_LigneDocument.idDocument', 'L_LigneDocument.idArticle',
            'L_LigneDocument.codeArticle', 'L_LigneDocument.libelleArticle', 'L_LigneDocument.qte',
            'L_LigneDocument.idUniteVente', 'L_LigneDocument.libelleUnite', 'L_LigneDocument.puht',
            'L_LigneDocument.puttc', 'L_LigneDocument.mntht', 'L_LigneDocument.mntTva',
            'L_LigneDocument.assietteTva', 'L_LigneDocument.mntNetht', 'L_LigneDocument.tauxTva',
            'L_LigneDocument.mntBrutht', 'L_LigneDocument.mntttc', 'L_LigneDocument.tauxRemise',
            'L_LigneDocument.mntRemise','L_LigneDocument.dateLigne'
        ];
    }

    public function document()
    {
        return $this->belongsTo(M_Document::class, 'idDocument');
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            $model->{$model->getKeyName()} = BaseModel::getID();
            $model->j_ddm = Carbon::now()->format("Y-m-d H:i:s");
            $model->dateLigne = Carbon::now()->format("Y-m-d H:i:s");
           $model->fillAdditionalAttributes();
        });
    }

    protected function fillAdditionalAttributes()
    {
        $dataStation = P_Station::findOrFail($this->document->idStation);
        $DataEtatsDoc = E_EtatDocument::where('isDefault', '1')->firstOrFail();
        $Exercice = P_Exercice::where('active', '1')->firstOrFail();
        $DataArticle = P_Article::findOrFail($this->idArticle);
        $DataClassDoc = P_ClasseDocument::findOrFail($this->document->idClasseDocument);
        $DataTier = P_tier::findOrFail($this->document->idTier);
        $uniteVenteData = $this->setUniteVente($DataClassDoc);
        $taux = $this->setTauxCommercialAndApporteur($this->document);
        $tva = $this->setTva();
        $idCodeAbare = L_CodeABare::where('idarticle',$this->idArticle)->first();
        $qteStk = $this->setQteStk($DataArticle,$DataClassDoc,$this->document->idStation);
//        $abonnement = $this->setAbonnement();
        $daterep = isset($validatedDataLine['dateReparation']) ? $validatedDataLine['dateReparation'] : null;

        // Assign values to the model attributes
        $this->idUniteVente = $uniteVenteData['idunitevente'];
        $this->libelleUnite = $uniteVenteData['libelleUnite'];
        $this->coeffUnitVente = $uniteVenteData['coefunite'];
        $this->tauxCommercial = $taux['tauxCommercial'];
        $this->tauxApporteur = $taux['tauxApporteur'];
        $this->idCodeABare = !empty($idCodeAbare) ? $idCodeAbare->id : null;
        $this->idTva = $tva['idTva'];
        $this->tauxTva = $tva['tauxTva'];
//        $this->dateLigne = now();
//        $this->j_ddm = now();
//        $this->idDocument = $dataDocument->id;
//        $this->qteRestante = 0;
//        $this->margeB = 0;
//        $this->j_operation = 'Insertion';
//        $this->j_codeUser = $dataDocument->j_codeUser;
//        $this->j_export = '0';
        $this->ancienqteStock = $qteStk;
        $this->P_codeExercice = $Exercice->code;
        $this->P_codeStation = $dataStation->code;
//        $this->idStation = $dataDocument->idStation;
        $this->idEtatDocument = $DataEtatsDoc->id;

        $this->prixuVenteHt = $DataArticle->prixVentePubHt;
        $this->prixuVenteTTC = $DataArticle->prixVentePublicTTC;
        $this->soumisFodec = $DataArticle->soumisFodec;
        $this->soumisDC = $DataArticle->soumisDC;
        $this->soumisTva = $DataArticle->soumisTva;
        $this->ancien_prixAchat = $DataArticle->dernierPrixAchat;
        $this->PrixAchat = $DataArticle->dernierPrixAchat;
        $this->isStockable = $DataArticle->isStockable;
        $this->ancien_prixVente = $DataArticle->dernierPrixVente;

        $this->idTypeCalcul = $DataClassDoc->idTypeCalcul;
        $this->libelleTypeCalcul = $DataClassDoc->libelleTypeCalcul;
        $this->codeTypeCalcul = $DataClassDoc->codeTypeCalcul;

        $this->puhtAchat = $DataArticle->dernierPrixAchat;
        $this->cr = $DataArticle->coutRevientPonderer;
        $this->ancienCR = $DataArticle->coutRevientPonderer;
        $this->cmp = $DataArticle->cmp;
        $this->ancienCMP = $DataArticle->cmp;
        $this->art_stockable = $DataArticle->isStockable;

        $this->idTierLigne = $DataTier->id;
        $this->codeTier = $DataTier->code;
        $this->nomPrenomTier = $DataTier->nomPrenom;
        $this->adresseTier = $DataTier->adresse;

//        $this->tauxMajorationTva = 0;
//        $this->mntCharge = 0;
//        $this->prixenc = 0;
//        $this->MargeReel = 0;
//        $this->MntMargeReel = 0;
//        $this->qteRetour = 0;
//        $this->CC = 0;
//        $this->Dern_Qte_Ent = 0;
//        $this->abnDateDebut = $abonnement['abnDateDebut'];
//        $this->abnPeriodicite = $abonnement['abnPeriodicite'];
//        $this->abnUnitePeriode = $abonnement['abnUnitePeriode'];
//        $this->abnPeriodeJ = $abonnement['abnPeriodeJ'];
//        $this->abnDateFin = $abonnement['abnDateFin'];
//        $this->abnAlerteAvant = $abonnement['abnAlerteAvant'];
//        $this->abnRenouvellement = $abonnement['abnRenouvellement'];
//        $this->abnAbonementActif = $abonnement['abnAbonementActif'];
//        $this->abnDateResiliation = $abonnement['abnDateResiliation'];
        //$this->idStationDestination = $dataDocument->idStationDestination !== 'empty' ? $dataDocument->idStationDestination : null;
        $this->isregler_Ap = false;
        $this->QtTransforme = 0;
        $this->etatTransforme = '1';
        $this->dateReparation = $daterep;
        unset($this->document);

    }

    private function setUniteVente($DataClassDoc)
    {
        $idunitevente = $this->idUniteVente ?? '';
        $libelleUnite = $this->libelleUnite ?? '';
        $coefunite = 1;

        if ($this->idUniteVente) {
            $DataUnite = P_Article_Unite::findOrFail($idunitevente);
            if ($DataUnite) {
                $coefunite = $DataUnite->coefficien;
            }
        }
        else {
            $isAchat = $DataClassDoc->isAchat == '1';
            $isVente = $DataClassDoc->isVente == '1';
            if ($isAchat || $isVente) {
                $isdefaultField = $isAchat ? 'isdefault_Achat' : 'isdefault_Vente';
                $articleUnite = P_Article_Unite::
                join('P_Unite as p', 'p.id', '=', 'P_Article_Unite.idUniteVente')
                    ->where('P_Article_Unite.idArticle', '=', $this->idArticle)
                    ->where('P_Article_Unite.' . $isdefaultField, '=', '1')
                    ->select('P_Article_Unite.id', 'p.libelle', 'P_Article_Unite.coefficien')
                    ->first();
                if ($articleUnite) {
                    $idunitevente = $articleUnite->id;
                    $libelleUnite = $articleUnite->libelle;
                    $coefunite = $articleUnite->coefficien;
                }
            }
        }
        return compact( 'idunitevente', 'libelleUnite', 'coefunite');
    }

    private function setTauxCommercialAndApporteur($document)
    {
        $connectionName = $this->getConnectionName();
        $useCommission = P_Parametrage::select('useCommission')->first();;
        $tauxCommercial = 0;
        $tauxApporteur = 0;
        if ($useCommission->useCommission == '1') {
            $Commercial = DB::connection($connectionName)->select("select [dbo].[getCommissionComercial](? , ?,?) as tauxCommercial", [$document->idTier, $document->id_Apporteur, $this->idArticle]);
            $tauxCommercial = $Commercial[0]->tauxCommercial;
            $Apporteur = DB::connection($connectionName)->select("select [dbo].[getCommissionApporteur](? , ?,?) as tauxApporteur", [$document->idTier, $document->id_Apporteur, $this->idArticle]);
            $tauxApporteur = $Apporteur[0]->tauxApporteur;
        }
        return compact( 'tauxCommercial', 'tauxApporteur');
    }

    private function setTva()
    {
        $idTva = $this->idTva;
        $tauxTva = $this->tauxTva;
        if (!$idTva) {
            $article = P_Article::findOrFail($this->idArticle);
            if ($article) {
                $idTva = $article->idTva;
                $tauxTva = $article->tauxTva;
            }
        }
        return compact( 'idTva', 'tauxTva');
    }

    private function setQteStk($DataArticle,$DataClassDoc,$idStation)
    {
        $connectionName = $this->getConnectionName();
        $qteStk = 0;
        if ($DataArticle->isStockable && $DataClassDoc->decrementeStock === "1") {
            $DataL_Stock = DB::connection($connectionName)->select("set language French SELECT sum(qte) as qte FROM L_stock where  idStation='$idStation' and idArticle='$this->idArticle'");
            $qteStk = (float)$DataL_Stock->qte;
            if ($DataClassDoc->stk_Neg === "0") {
                if ($qteStk < $this->qte) {
                    $dataRes["idArticle"] = $this->idArticle;
                    $dataRes["codeArticle"] = $this->codeArticle;
                    $dataRes["libelleArticle"] = $this->libelleArticle;
                    DB::rollback();
                    throw new \Exception("Insufficient stock for Article: " . json_encode($dataRes));
                }
            }
        }
        return $qteStk;
    }

    private function setAbonnement($art)
    {
        $abnDateDebut = date("Y-m-d H:i:s");
        if ($art['abnDateDebut'] !== null) {
            $abnDateDebut = $art['abnDateDebut'];
        }
        $abnDateFin = date("Y-m-d H:i:s");
        if ($art['abnDateFin'] !== null) {
            $abnDateFin = $art['abnDateFin'];
        }
        $abnDateResiliation = date("Y-m-d H:i:s");
        if ($art['abnDateResiliation'] !== null) {
            $abnDateResiliation = $art['abnDateResiliation'];
        }

        $abnAlerteAvant = $art['abnAlerteAvant'];
        if ($art['abnAlerteAvant'] === null || $art['abnAlerteAvant'] === 'null') {
            $abnAlerteAvant = 0;
        }

        $abnAbonementActif = '0';
        if ($art['abnAbonementActif'] !== null) {
            $abnAbonementActif = $art['abnAbonementActif'];
        }

        $abnPeriodicite = 0;
        if ($art['abnPeriodicite'] !== null) {
            $abnPeriodicite = $art['abnPeriodicite'];
        }

        $abnPeriodeJ = 0;
        if ($art['abnPeriodeJ'] !== null) {
            $abnPeriodeJ = $art['abnPeriodeJ'];
        }

        $abnRenouvellement = date("Y-m-d H:i:s");
        if ($art['abnRenouvellement'] !== null) {
            $abnRenouvellement = $art['abnRenouvellement'];
        }

        $abnUnitePeriode = date("Y-m-d H:i:s");
        if ($art['abnUnitePeriode'] !== null) {
            $abnUnitePeriode = $art['abnUnitePeriode'];
        }
        return compact( 'abnDateDebut', 'abnDateFin','abnDateResiliation',
            'abnAlerteAvant','abnAbonementActif','abnPeriodicite','abnPeriodeJ','abnRenouvellement','abnUnitePeriode');
    }

}
