<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use App\Models\Client;
use App\Models\Ticket;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TicketWSMobile extends Controller
{

    public function getTicket(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $query = $connection->table('Ticket');

        if (($request->has('zone') && $request->input('zone') === true) || ($request->input('zone') === 'true' && $request->hasHeader('user'))) {
            $clients = array_column(Client::query()
                ->join('Zone', 'Client.Clt_Circuit', '=', 'Zone.CodeZone')
                ->join('ZoneUtilisateur', 'Zone.CodeZone', '=', 'ZoneUtilisateur.CodeZone')
                ->where('Zone.EtatZone', '=', true)
                ->where('ZoneUtilisateur.CodeUtilisateur', '=', $request->header('user'))
                ->select('Client.CLI_Code')
                ->get()->toArray(), 'CLI_Code');
            $query->whereIn('TIK_CodClt', $clients);
        }

        return response()->json($query->get());
    }

    public function getCAparFamille(Request $request)
    {

        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $date_from = AppHelper::setDateFormat($data["object"][0]);
        $date_to = AppHelper::setDateFormat($data["object"][1]);
        $req = $connection->table('View_ChiffreAffaire_Chat')
            ->groupby('FAM_Lib')
            ->select('FAM_Lib', $connection->raw('sum(LT_MtTTC) as CA'))
            ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
            ->orderby('CA', 'desc')
            ->get();


        return response()->json($req);
    }

    public function getCAparMarque(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"][0]);
        $date_to = AppHelper::setDateFormat($data["object"][1]);
        $connection = DatabaseConnection::setConnection($data);

        $result = $connection->table('View_ChiffreAffaire_Chat')
            ->groupby('MAR_Designation')
            ->select('MAR_Designation', $connection->raw('sum(LT_MtTTC) as CA'))
            ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
            ->orderby('CA', 'desc')
            ->get();
        return response()->json($result);
        /*$req = "Set Language French
              SELECT sum(LT_MtTTC) as CA,MAR_Designation FROM dbo.View_ChiffreAffaire_Chat
         WHERE TIK_DateHeureTicket between '" . ($data["object"][0]) . "'  AND '" . ($data["object"][1]) . "' GROUP BY MAR_Designation
         order by CA desc";

        return response()->json(collect($connection->select($req)));*/
    }

    public function getCAparFournisseurv1(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $req = "Set Language French
              SELECT sum(LT_mtTTC) as CA,FRS_Nomf,frs_codef FROM dbo.View_B9
         WHERE  (CAST(FLOOR(CAST(TIK_DateHeureTicket AS float)) AS datetime)between '" . ($data["object"][0]) . "'  AND '" . ($data["object"][1]) . "' )  GROUP BY FRS_Nomf,frs_codef
         order by CA desc";

        return response()->json(collect($connection->select($req)));
    }

    public function getCAparFournisseur(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->table('bon_entree')
            ->leftJoin('fournisseur', 'fournisseur.FRS_codef', '=', 'bon_entree.BON_ENT_CodeFrs')
            ->groupby('fournisseur.FRS_Nomf')
            ->select('fournisseur.FRS_Nomf', $connection->raw('sum(bon_entree.BON_ENT_MntTTC) as CA'))
            ->whereBetween('bon_entree.BON_ENT_Date', [$date_from, $date_to])
            ->orderby('CA', 'desc')
            ->get();
        return response()->json($result);

    }

    public function getCAparClient(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->table('View_B9')
            ->groupby('View_B9.CLI_Code', 'View_B9.CLI_NomPren')
            ->select('View_B9.CLI_Code', 'View_B9.CLI_NomPren', $connection->raw('sum(View_B9.LT_MtTTC) as CA'))
            ->whereBetween('View_B9.TIK_DateHeureTicket', [$date_from, $date_to])
            ->orderby('CA', 'desc')
            ->get();
        return response()->json($result);
    }

    public function getCAparArticle(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $date_from = AppHelper::setDateFormat($data["object"][0]);
        $date_to = AppHelper::setDateFormat($data["object"][1]);
        $result = $connection->table('View_ChiffreAffaire_Chat')
            ->groupby('ART_Designation')
            ->select('ART_Designation', $connection->raw('sum(LT_MtTTC) as CA'))
            ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
            ->orderby('CA', 'desc')
            ->get();
        return response()->json($result);
        /* $req = "Set Language French
               SELECT sum(LT_MtTTC) as CA,ART_Designation FROM dbo.View_ChiffreAffaire_Chat
          WHERE TIK_DateHeureTicket between '" . ($data["object"][0]) . "'  AND '" . ($data["object"][1]) . "' GROUP BY ART_Designation
          order by CA desc";

         return response()->json(collect($connection->select($req)));*/
    }


    public function getTicketsByCaisseId(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $exercice = $request->query('exercice');
        $archive = $request->query('archive');
        $tickets = null;
        if ($archive == 'true') {
            $tickets = $connection->table('Ticket')->where('TIK_IdSCaisse', '<>', $data["object"])
                ->where('TIK_Exerc', '=', $exercice)
                ->orderBy('TIK_DateHeureTicket', 'DESC')->get();
        }
        if ($archive == 'false') {

            $tickets = $connection->table('Ticket')->where('TIK_IdSCaisse', '=', $data["object"])
                ->orderBy('TIK_DateHeureTicket', 'DESC')->get();


        }

        return response()->json($tickets);
    }

    public function addTicketWithLignesTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data["connexion"] ?? null);

        if (!empty($data)) {

            $insertArticleResponse = $connection->table('Ticket')->insert($data["ticket"]);
            $ticket = $data["ticket"];
            if ($insertArticleResponse) {
                $stationArticleController = new StationArticleController();
                foreach ($data["ligneTicket"] as $ligneTicket) {
                    $ligneTicket["LT_MtHT"] = floatval($ligneTicket["LT_MtHT"]);
                    $ligneTicket["LT_MtTTC"] = floatval($ligneTicket["LT_MtTTC"]);
                    $ligneTicket["LT_PACHAT"] = floatval($ligneTicket["LT_PACHAT"]);
                    $ligneTicket["LT_PrixEncaisse"] = floatval($ligneTicket["LT_PrixEncaisse"]);
                    $ligneTicket["LT_Remise"] = floatval($ligneTicket["LT_Remise"]);
                    $ligneTicket["LT_PrixVente"] = floatval($ligneTicket["LT_PrixVente"]);
                    $connection->table('LigneTicket')->insert($ligneTicket);
                    $oldQty = $stationArticleController->getArticleCountInStation($ligneTicket["LT_CodArt"], $ticket["TIK_station"], $connection);
                    $stationArticleController->setArticleSockQantityInStation($ligneTicket["LT_CodArt"], $ticket["TIK_station"], $oldQty - $ligneTicket["LT_Qte"], $connection);
                }
                return response()->json(true);

            } else {
                return response()->json(false);

            }
        } else {

            return response()->json(false);

        }

    }

    public function addBatchTicketWithLignesTicketAndPayment(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        $result = [];

        if (!empty($items)) {
            foreach ($items["object"] as &$data) {
                $connection->beginTransaction();
                try {
                    $ticket = $data["ticket"];

                    $exist = $connection->table("ticket")
                        ->where('TIK_IdCarnet', $ticket["TIK_IdCarnet"])
                        ->where('TIK_NumTicket_M', $ticket["TIK_NumTicket_M"])
                        ->where('TIK_Exerc', $ticket['TIK_Exerc'])
                        ->where('TIK_Source', $ticket["TIK_Source"])
                        ->first();

                    if (!$exist) {
                        $ticket['TIK_Nbre_Pts_Gain'] = isset($ticket['TIK_NumTicket']) && !!$ticket['TIK_NumTicket']
                            ? $ticket['TIK_NumTicket']
                            : null;

                        if (!!$ticket['TIK_Timbre']) {
                            $timbre_value = $connection->table('timbre')
                                ->where('TIMB_Code', (int)$ticket['TIK_Timbre'])
                                ->pluck('TIMB_Value')->first();
                            $ticket['TIK_Timbre'] = (double)$timbre_value;
                        }

                        $ticket['TIK_DateHeureTicket'] = AppHelper::setDateFormat($ticket['TIK_DateHeureTicket']);
                        $ticket['TIK_Date_Mariage'] = AppHelper::setDateFormat($ticket['TIK_Date_Mariage']);

                        $existSession = $connection->table('SessionCaisse')
                            ->where('SC_IdSCaisse', $ticket['TIK_IdSCaisse'])
                            ->first();

                        if ($existSession) {
                            $count = $connection->table('Ticket')
                                ->where('TIK_IdSCaisse', $ticket['TIK_IdSCaisse'])
                                ->count();

                            if ($count <= 0) {
                                $connection->table('SessionCaisse')
                                    ->where('SC_IdSCaisse', $ticket['TIK_IdSCaisse'])
                                    ->update(["SC_DateHeureOuv" => $ticket['TIK_DateHeureTicket']]);
                            }
                        } else {
                            $soldeClient = Ticket::SoldeClient($connection, $ticket['TIK_CodClt']);
                            $result[] = $this->setResult($ticket['TIK_NumTicket'],
                                $ticket['TIK_IdCarnet'],
                                $ticket['TIK_Exerc'], $ticket['TIK_NumTicket_M'],
                                null, $ticket['TIK_CodClt'], $soldeClient['SoldeClient'], $soldeClient['Debit'], $soldeClient['Credit'],
                                null, "Session n'existe pas", Enum::Error_Montant);
                            Log::error("Session n'existe pas", $result);
                            throw new Exception("", Enum::Error_Session_Dont_exist);
                        }

                        $num_ticket = $connection->table('Ticket')
                            ->where("TIK_IdCarnet", $ticket['TIK_IdCarnet'])
                            ->max('TIK_NumTicket');

                        $ticket['TIK_NumTicket'] = $num_ticket <= 0 ? 1 : $num_ticket + 1;
                        $ticket['TIK_DDm'] = Carbon::now();

                        $nbligneMobile = $ticket['TIK_Nbr_Lin'];
                        unset($ticket['TIK_Nbr_Lin'], $ticket['Mnt_RevImp']);

                        $succesInsertticket = $connection->table('ticket')->insert($ticket);

                        if (!$succesInsertticket) {
                            $soldeClient = Ticket::SoldeClient($connection, $ticket['TIK_CodClt']);
                            $result[] = $this->setResult((int)$ticket['TIK_NumTicket'],
                                $ticket['TIK_IdCarnet'], $ticket['TIK_Exerc'],
                                $ticket['TIK_NumTicket_M'], null, $ticket['TIK_CodClt'],
                                $soldeClient['SoldeClient'], $soldeClient['Debit'], $soldeClient['Credit'],
                                null, "Erreur dans l'insertion de Ticket", Enum::Error_Insert_Ticket);
                            Log::error("Erreur dans l'insertion de Ticket", $result);
                            throw new Exception("", Enum::Error_Insert_Ticket);
                        }

                        $AddedTicket = $connection->table("ticket")
                            ->where('TIK_IdCarnet', $ticket["TIK_IdCarnet"])
                            ->where('TIK_NumTicket_M', $ticket["TIK_NumTicket_M"])
                            ->where('TIK_Exerc', $ticket['TIK_Exerc'])
                            ->where('TIK_Source', $ticket["TIK_Source"])
                            ->first();

                        $ligneTickets = $data["lignesTicket"];
                        $inserted_ligne = [];

                        foreach ($ligneTickets as $ligneTicket) {
                            $exist_ligne = $connection->table("LigneTicket")
                                ->where('LT_IdCarnet', $AddedTicket->TIK_IdCarnet)
                                ->where('LT_Exerc', $AddedTicket->TIK_Exerc)
                                ->where('LT_NumTicket', $AddedTicket->TIK_NumTicket)
                                ->where('LT_NumTicket_M', $ligneTicket["LT_NumTicket_M"])
                                ->first();

                            if (!$exist_ligne) {
                                $ligne = [
                                    "LT_NumTicket" => $AddedTicket->TIK_NumTicket,
                                    "LT_IdCarnet" => $AddedTicket->TIK_IdCarnet,
                                    "LT_Exerc" => $AddedTicket->TIK_Exerc,
                                    "LT_CodArt" => $ligneTicket["LT_CodArt"],
                                    "LT_PrixVente" => $ligneTicket["LT_PrixVente"],
                                    "LT_Remise" => $ligneTicket["LT_Remise"],
                                    "LT_TVA" => $ligneTicket["LT_TVA"],
                                    "LT_Commande" => $ligneTicket["LT_Commande"],
                                    "LT_MtHT" => $ligneTicket["LT_MtHT"],
                                    "LT_Unite" => $ligneTicket["LT_Unite"],
                                    "LT_Qte" => $ligneTicket["LT_Qte"],
                                    "LT_MtTTC" => $ligneTicket["LT_MtTTC"],
                                    "LT_Annuler" => $ligneTicket["LT_Annuler"],
                                    "LT_QtePiece" => 1,
                                    "LT_DDm" => Carbon::now(),
                                    "LT_PrixEncaisse" => $ligneTicket["LT_PrixEncaisse"],
                                    "LT_PACHAT" => $ligneTicket["LT_PACHAT"],
                                    "LT_Taux_Remise" => $ligneTicket["LT_Taux_Remise"],
                                    "LT_Tarif" => $ligneTicket["LT_Tarif"],
                                    "CodeBarFils" => $ligneTicket["CodeBarFils"],
                                    "LT_NumTicket_M" => $ligneTicket["LT_NumTicket_M"]
                                ];
                                $inserted_ligne[] = $ligne;
                            }
                        }

                        $succesInsertLigneTicket = $connection->table('LigneTicket')->insert($inserted_ligne);

                        if (count($inserted_ligne) < $nbligneMobile) {
                            throw new Exception("", Enum::Error_Insert_Ligne_Ticket);
                        }

                        // Block reglement
                        $testRegelemnt = $this->addRegelement(
                            strtolower($ticket['TIK_Etat']) === 'credit' ? null : $AddedTicket,
                            $data,
                            $connection
                        );

                        if (!$testRegelemnt) {
                            throw new Exception("", Enum::Error_Insert_ReglementCaisse);
                        }

                        // Success, commit transaction
                        $connection->commit();
                    }
                } catch (Exception $e) {
                    $connection->rollBack();
                    Log::error("Erreur dans l'ajout de ticket batch", ['error' => $e->getMessage()]);
                    continue; // move to next ticket
                }
            }
        }

        return response()->json($result);
    }

    public function updateTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        if (!empty($data)) {
            $response = $connection->table('Ticket')->where('TIK_NumTicket', $data["TIK_NumTicket"])->where('TIK_Exerc', $data["TIK_Exerc"])->where('TIK_IdCarnet', $data["TIK_IdCarnet"])->update($data);
            return response()->json($response == 1);

        } else {
            return response()->json(false);
        }

    }

    public function deleteTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        if (!empty($data)) {
            $data["TIK_Annuler"] = true;
            $response = $connection->table('Ticket')->where('TIK_NumTicket', $data["TIK_NumTicket"])->where('TIK_Exerc', $data["TIK_Exerc"])->where('TIK_IdCarnet', $data["TIK_IdCarnet"])->update($data);

            return response()->json(($response == 1) ? true : false);

        } else {
            return response()->json(false);
        }
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }

    public function CalculNombreTicketValidee(Request $request)
    {
        $data = $request->json()->all();
        $station = $request->input('station');
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $connection = DatabaseConnection::setConnection($data);

        if (!!$station) {
            $result = $connection->table('Ticket')
                ->select($connection->raw('count(TIK_MtTTC) as nbr_ticket_validee'))
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->where('TIK_Annuler', 0)
                ->where('TIK_station', '=', $station)
                ->first()->nbr_ticket_validee;

        } else {
            $result = $connection->table('Ticket')
                ->select($connection->raw('count(TIK_MtTTC) as nbr_ticket_validee'))
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->where('TIK_Annuler', 0)
                ->first()->nbr_ticket_validee;
        }
        return $result;

    }

    public function CalculNombreTicketAnnule(Request $request)
    {
        $data = $request->json()->all();
        $station = $request->input('station');
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $connection = DatabaseConnection::setConnection($data);
        if (!!$station) {
            $result = $connection->table('Ticket')
                ->select($connection->raw('count(TIK_MtTTC) as nbr_ticket_annulee'))
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->where('TIK_station', '=', $station)
                ->where('TIK_Annuler', 1)
                ->first()->nbr_ticket_annulee;
        } else {
            $result = $connection->table('Ticket')
                ->select($connection->raw('count(TIK_MtTTC) as nbr_ticket_annulee'))
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->where('TIK_Annuler', 1)
                ->first()->nbr_ticket_annulee;
        }
        return $result;

    }

    public function DisplayDetailTicketAnnule(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);

        $connection = DatabaseConnection::setConnection($data);
        $results = array();
        $tickets = $connection->table('ticket')
            ->leftJoin('Utilisateur', 'Utilisateur.Code_Ut', '=', 'Ticket.TIK_user')
            ->join('client', 'ticket.TIK_CodClt', '=', 'client.CLI_Code')
            ->select('TIK_NumTicket', 'TIK_Exerc', 'TIK_IdCarnet', 'Utilisateur.Nom'
                , 'Utilisateur.Prenom', 'TIK_MtTTC', 'TIK_Annuler', 'Ticket.TIK_DDm', 'client.CLI_NomPren')
            ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
            ->where('TIK_Annuler', 1)
            ->get();

        for ($index = 0; $index < sizeof($tickets); $index++) {
            $item = array();
            $item["ticket"] = $tickets[$index];
            $lignes = $connection->table('LigneTicket')
                ->leftJoin('article', 'article.ART_Code', '=', 'LigneTicket.LT_CodArt')
                ->select('LigneTicket.LT_CodArt', 'article.ART_Designation',
                    'LigneTicket.LT_PrixVente', 'LigneTicket.LT_Qte', 'LigneTicket.LT_MtTTC', 'LT_Remise')
                ->where('LigneTicket.LT_NumTicket', '=', $tickets[$index]->TIK_NumTicket)
                ->where('LigneTicket.LT_IdCarnet', '=', $tickets[$index]->TIK_IdCarnet)
                ->where('LigneTicket.LT_Exerc', '=', $tickets[$index]->TIK_Exerc)
                ->get();
            $item["ligneTicket"] = $lignes;

            $results[] = $item;

        }
        $grouped = (collect($results))->groupBy('ticket.Nom');
        return response()->json($grouped, 201);

    }

    public function DisplayDetailTicketValidee(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);

        $connection = DatabaseConnection::setConnection($data);
        $results = array();
        if ($request->input('station')) {
            $tickets = $connection->table('ticket')
                ->leftJoin('Utilisateur', 'Utilisateur.Code_Ut', '=', 'Ticket.TIK_user')
                ->join('client', 'ticket.TIK_CodClt', '=', 'client.CLI_Code')
                ->select('TIK_NumTicket', 'TIK_Exerc', 'TIK_IdCarnet', 'Utilisateur.Nom',
                    'Utilisateur.Prenom', 'TIK_MtTTC', 'TIK_Annuler', 'Ticket.TIK_DDm', 'client.CLI_NomPren')
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->where('Tik_Station', '=', $request->input('station'))
                ->where('TIK_Annuler', 0)
                ->get();
        } else {
            $tickets = $connection->table('ticket')
                ->leftJoin('Utilisateur', 'Utilisateur.Code_Ut', '=', 'Ticket.TIK_user')
                ->join('client', 'ticket.TIK_CodClt', '=', 'client.CLI_Code')
                ->select('TIK_NumTicket', 'TIK_Exerc', 'TIK_IdCarnet', 'Utilisateur.Nom',
                    'Utilisateur.Prenom', 'TIK_MtTTC', 'TIK_Annuler', 'Ticket.TIK_DDm', 'client.CLI_NomPren')
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->where('TIK_Annuler', 0)
                ->get();
        }


        for ($index = 0; $index < sizeof($tickets); $index++) {
            $item = array();
            $item["ticket"] = $tickets[$index];
            $lignes = $connection->table('LigneTicket')
                ->leftJoin('article', 'article.ART_Code', '=', 'LigneTicket.LT_CodArt')
                ->select('LigneTicket.LT_CodArt', 'article.ART_Designation', 'LigneTicket.LT_PrixVente', 'LigneTicket.LT_Qte', 'LigneTicket.LT_MtTTC', 'LT_Remise')
                ->where('LigneTicket.LT_NumTicket', '=', $tickets[$index]->TIK_NumTicket)
                ->where('LigneTicket.LT_IdCarnet', '=', $tickets[$index]->TIK_IdCarnet)
                ->where('LigneTicket.LT_Exerc', '=', $tickets[$index]->TIK_Exerc)
                ->get();
            $item["ligneTicket"] = $lignes;

            $results[] = $item;

        }
        $grouped = (collect($results))->groupBy('ticket.Nom');
        return response()->json($grouped, 201);


    }

    public function CalculNombreArticleSupprimee(Request $request)
    {
        $data = $request->json()->all();
        $station = $request->input('station');
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);

        $connection = DatabaseConnection::setConnection($data);
        if (!!$station) {
            $result = $connection->table('View_Historique_suppressArt')
                ->select($connection->raw('count(id) as nbr_article_supprimee'))
                ->where('STAT_Code', '=', $station)
                ->whereBetween('S_Date', [$date_from, $date_to])
                ->first()->nbr_article_supprimee;
        } else {
            $result = $connection->table('View_Historique_suppressArt')
                ->select($connection->raw('count(id) as nbr_article_supprimee'))
                ->whereBetween('S_Date', [$date_from, $date_to])
                ->first()->nbr_article_supprimee;
        }
        return $result;

    }

    public function DisplayNombreTicket(Request $request)
    {

        $nbr_ticket_validee = $this->CalculNombreTicketValidee($request);
        $nbr_ticket_annulee = $this->CalculNombreTicketAnnule($request);
        $nbr_ticket = $nbr_ticket_validee + $nbr_ticket_annulee;
        $taux_ticket_validee = $nbr_ticket == 0 ? 0 : ($nbr_ticket_validee / $nbr_ticket) * 100;
        $taux_ticket_annulee = $nbr_ticket == 0 ? 0 : ($nbr_ticket_annulee / $nbr_ticket) * 100;
        $nbr_article_supprimee = $this->CalculNombreArticleSupprimee($request);
        $nbr_tiroir = $this->CalculNombreTiroir($request);

        $result = [
            'nbr_ticket' => $nbr_ticket,
            'nbr_ticket_validee' => $nbr_ticket_validee,
            'nbr_ticket_annulee' => $nbr_ticket_annulee,
            'taux_ticket_validee' => $taux_ticket_validee,
            'taux_ticket_annulee' => $taux_ticket_annulee,
            'nbr_article_supprimee' => $nbr_article_supprimee,
            'nbr_tiroir' => $nbr_tiroir
        ];

        return response()->json($result, 201);

    }

    public function CalculNombreTicketValideeByCaissier(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $id_caissier = $data["object"]["id_caissier"];

        $connection = DatabaseConnection::setConnection($data);
        return $connection->table('Ticket')
            ->select($connection->raw('count(TIK_MtTTC) as nbr_ticket_validee'))
            ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
            ->where('TIK_Annuler', 0)
            ->where('TIK_user', "=", $id_caissier)
            ->first()->nbr_ticket_validee;

    }

    public function CalculNombreTicketAnnuleByCaissier(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $id_caissier = $data["object"]["id_caissier"];

        $connection = DatabaseConnection::setConnection($data);
        return $connection->table('Ticket')
            ->select($connection->raw('count(TIK_MtTTC) as nbr_ticket_annulee'))
            ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
            ->where('TIK_Annuler', 1)
            ->where('TIK_user', "=", $id_caissier)
            ->first()->nbr_ticket_annulee;

    }

    public function CalculNombreArticleSupprimeeByCaissier(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $id_caissier = $data["object"]["id_caissier"];

        $connection = DatabaseConnection::setConnection($data);
        return $connection->table('View_Historique_suppressArt')
            ->select($connection->raw('count(id) as nbr_article_supprimee'))
            ->whereBetween('S_Date', [$date_from, $date_to])
            ->where('S_User', "=", $id_caissier)
            ->first()->nbr_article_supprimee;

    }

    public function DisplayNombreTicketByCaissier(Request $request)
    {
        $nbr_ticket_validee_by_caissier = $this->CalculNombreTicketValideeByCaissier($request);
        $nbr_ticket_annulee_by_caissier = $this->CalculNombreTicketAnnuleByCaissier($request);
        $nbr_ticket_by_caissier = $nbr_ticket_validee_by_caissier + $nbr_ticket_annulee_by_caissier;
        $taux_ticket_validee_by_caissier = $nbr_ticket_by_caissier == 0 ? 0 : ($nbr_ticket_validee_by_caissier / $nbr_ticket_by_caissier) * 100;
        $taux_ticket_annulee_by_caissier = $nbr_ticket_by_caissier == 0 ? 0 : ($nbr_ticket_annulee_by_caissier / $nbr_ticket_by_caissier) * 100;
        $nbr_article_supprimee_by_caissier = $this->CalculNombreArticleSupprimeeByCaissier($request);

        $result = [
            'nbr_ticket' => $nbr_ticket_by_caissier,
            'nbr_ticket_validee' => $nbr_ticket_validee_by_caissier,
            'nbr_ticket_annulee' => $nbr_ticket_annulee_by_caissier,
            'taux_ticket_validee' => $taux_ticket_validee_by_caissier,
            'taux_ticket_annulee' => $taux_ticket_annulee_by_caissier,
            'nbr_article_supprimee' => $nbr_article_supprimee_by_caissier
        ];

        return response()->json($result, 201);

    }

    public function DisplayDetailArticleSupprimee(Request $request)
    {
        $data = $request->json()->all();
        $station = $request->input('station');
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $connection = DatabaseConnection::setConnection($data);
        if (!!$station) {
            $result = $connection->table('View_Historique_suppressArt')
                ->select('ART_Designation', 'S_Qte', 'S_PTotal', 'caissier', 'S_Date')
                ->where('Station', '=', $station)
                ->whereBetween('S_Date', [$date_from, $date_to])
                ->get();
        } else {
            $result = $connection->table('View_Historique_suppressArt')
                ->select('ART_Designation', 'S_Qte', 'S_PTotal', 'caissier', 'S_Date')
                ->whereBetween('S_Date', [$date_from, $date_to])
                ->get();
        }

        return response()->json($result, 201);
    }

    public function DisplayDetailArticleSupprimeeByCaissier(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $id_caissier = $data["object"]["id_caissier"];
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->table('View_Historique_suppressArt')
            ->select('ART_Designation', 'S_Qte', 'S_PTotal', 'caissier', 'S_Date')
            ->whereBetween('S_Date', [$date_from, $date_to])
            ->where('S_User', "=", $id_caissier)
            ->get();
        return response()->json($result, 201);
    }

    public function CalculNombreTiroir(Request $request)
    {
        $data = $request->json()->all();
        $station = $request->input('station');
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $connection = DatabaseConnection::setConnection($data);
        if (!!$station) {
            $result = $connection->table('Historique_Caisse')
                ->select($connection->raw('count(HIS_Cai_Num) as nbr_tiroir'))
                ->whereBetween('Date_Op', [$date_from, $date_to])
                ->where('Station', '=', $station)
                ->where('Info1', 'Ouverture tiroir')
                ->first()->nbr_tiroir;
        } else {
            $result = $connection->table('Historique_Caisse')
                ->select($connection->raw('count(HIS_Cai_Num) as nbr_tiroir'))
                ->whereBetween('Date_Op', [$date_from, $date_to])
                ->where('Info1', 'Ouverture tiroir')
                ->first()->nbr_tiroir;
        }
        return $result;
    }

    public function getMaxNumTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $TIK_IdCarnet = $request->input("id_carnet");

        $TIK_Exerc = $request->input("id_exerc");

        $result = $connection->table('Ticket')
            ->select()
            ->where("TIK_IdCarnet", "=", $TIK_IdCarnet)
            ->where("TIK_Exerc", "=", $TIK_Exerc)
            ->max('TIK_NumTicket');

        $num_ticket = (int)$result;

        return response()->json($num_ticket, 201);
    }


    public function addRegelement($ticket, $data, $connection): bool
    {

        $testReglement = true;

        if (isset($data["reglement"]) && sizeof($data['reglement']) != 0) {

            $reglement = $data["reglement"];
            //Controle du Ticket id 0
            $ticket_0 = $connection->table('Ticket')
                ->where('TIK_NumTicket', '=', 0)
                ->where('TIK_Exerc', '=', $reglement['REGC_Exercice'])
                ->where('TIK_IdCarnet', '=', $reglement['REGC_IdCarnet'])
                ->first();

            if (!$ticket_0) {
                $ticket_0_insert = $this->Object_Ticket_0($reglement['REGC_DateReg'], $reglement['REGC_Exercice'],
                    $reglement['REGC_IdCarnet'], $reglement['REGC_IdSCaisse'], $reglement['REGC_User']);
                $connection->table('Ticket')->insert($ticket_0_insert);
            }
            unset($reglement["rest"]);

            if (array_key_exists("made", $reglement)) {
                unset($reglement["made"]);
            }

            $reglement["REGC_Code"] = (new PrefixWSMobile)->getReglementPrefix($connection,
                "ReglementCaisse",
                $data["reglement"]['REGC_Exercice'], "REGC_IdSCaisse",
                $data["reglement"]['REGC_IdSCaisse']);


            $reglement['REGC_DateReg'] = AppHelper::setDateFormat($data["reglement"]['REGC_DateReg']);
            $reglement["REGC_NumTicket"] = !!$ticket ? $ticket->TIK_NumTicket : 0;

            $exist =
                sizeof(collect($connection->select("select * from ReglementCaisse where REGC_Code_M = '" .
                    $reglement["REGC_Code_M"] . "'")));

            if ($exist <= 0) {


                $succesInsertReglementCaisse = $connection->table('ReglementCaisse')->insert($reglement);
                // verifier si l'insertion du reglement est effectué si non lancer une exception
                if (!$succesInsertReglementCaisse) {
                    $testReglement = false;

                }

            } else {
                $testReglement = false;

            }

        }
        return $testReglement;

    }

    public function addCheques($data, $connection, $reglement): bool
    {
        $testCheques = true;
        if (isset($data["cheques"])) {
            $nbLigneCheque = count($data["cheques"]);
            $countLigneCheque = 0;
            foreach ($data["cheques"] as $key => &$cheque) {
                $cheque['EcheanceCheque'] = AppHelper::setDateFormat($cheque['EcheanceCheque']);
                $cheque['Reglement'] = $reglement->REGC_Code;

                $exist = $connection->table('ChequeCaisse')
                    ->where('NumCheque', '=', $cheque['NumCheque'])
                    ->where('Reglement_M', '=', $reglement->REGC_Code_M)
                    ->where('reglement_idsession', '=', $cheque['reglement_idsession'])
                    ->where('reglement_exercice', '=', $cheque['reglement_exercice'])->first();


                if (!$exist) {
                    $succesInsertCheque = $connection->table('ChequeCaisse')->insert($cheque);

                    if ($succesInsertCheque) {
                        $countLigneCheque++;
                    }
                }

            }
            if ($countLigneCheque < $nbLigneCheque) {
                $testCheques = false;
            }
        }

        return $testCheques;
    }

    public function addTraites($data, $connection, $reglement): bool
    {

        $testTraites = true;
        if (isset($data["traites"])) {
            $nbLigneTraite = count($data["traites"]);

            $countLigneTraite = 0;

            foreach ($data["traites"] as &$traite) {
                $traite["TRAIT_Num"] = $reglement->REGC_Code;
                $traite["TRAIT_Reglement"] = $reglement->REGC_Code;
                $traite['TRAIT_Echeance'] = AppHelper::setDateFormat($traite['TRAIT_Echeance']);

                $exist = $connection->table('TraiteCaisse')->where('TRAIT_Num_M', '=', $reglement->REGC_Code_M)
                    ->where('TRAIT_Ordre', '=', $traite['TRAIT_Ordre'])
                    ->where('TRAIT_Reglement', '=', $reglement->REGC_Code)
                    ->where('TRAIT_IdSession', '=', $traite['TRAIT_IdSession'])
                    ->where('TRAIT_Exercice', '=', $traite['TRAIT_Exercice'])->first();

                if (!$exist) {


                    $succesInsertTraiteCaisse = $connection->table('TraiteCaisse')->insert($traite);


                    if ($succesInsertTraiteCaisse) {
                        $countLigneTraite++;
                    }
                }
                //      $data["traites"][$key] = $traite;
            }

            if ($countLigneTraite < $nbLigneTraite) {
                $testTraites = false;
            }
        }

        return $testTraites;
    }


    public function CalculMontantTTC($items): float
    {
        $montant_traites = 0;
        $montant_cheques = 0;
        if (isset($items['reglement']) && count($items['reglement']) != 0) {
            $montant_reglement = (float)$items['reglement']['REGC_MntEspece'];

        }
        if (isset($items['traites'])) {
            foreach ($items['traites'] as $data) {
                $montant_traites += (float)$data['TRAIT_Montant'];
            }
        }
        if (isset($items['cheques'])) {

            foreach ($items['cheques'] as $data) {
                $montant_cheques += (float)$data['Montant'];
            }
        }
        return $montant_cheques + $montant_traites + $montant_reglement;
    }

    public function transert_bc_to_ticket($Ticket, $connection): ?string
    {
        $exist = $connection->table('devis')
            ->where('DEV_Num', '=', $Ticket->TIK_NumTicket_M)
            ->where('DEV_Exerc', '=', $Ticket->TIK_Exerc)
            ->first();
        if ($exist) {
            $observation = $Ticket->TIK_NumTicket . ";" . $Ticket->TIK_IdCarnet;
            $connection->table('devis')
                ->where('DEV_Num', '=', $exist->DEV_Num)
                ->where('DEV_Exerc', '=', $exist->DEV_Exerc)
                ->update(['DEV_Observation' => $observation]);

            return $observation;
        }
        return null;


    }

    public function verif_bc_exite($Ticket, $connection): ?string
    {
        $exist = $connection->table('devis')
            ->where('DEV_Num', '=', $Ticket->TIK_NumTicket_M)
            ->where('DEV_Exerc', '=', $Ticket->TIK_Exerc)
            ->first();
        if ($exist) {
            return $exist->DEV_Observation;
        }
        return null;
    }

    public function Object_Ticket_0($ddm, $TIK_Exerc, $TIK_IdCarnet, $TIK_IdSCaisse, $idUser): array
    {
        $ticket_0 = [

            "TIK_Annuler" => 0,
            "TIK_CodClt" => "000000001",
            "TIK_DDm" => $ddm,
            "TIK_DateHeureTicket" => $ddm,
            "TIK_Date_Mariage" => $ddm,
            "TIK_EnvWebServ" => 0,
            "TIK_Etat" => "Regler",
            "TIK_Exerc" => $TIK_Exerc,
            "TIK_export" => 0,
            "TIK_IdCarnet" => $TIK_IdCarnet,
            "TIK_IdSCaisse" => $TIK_IdSCaisse,
            "TIK_is_Contrat" => 0,
            "TIK_NomClient" => "Passager",
            "TIK_NumFact" => 0.0,
            "TIK_NumTicket" => 0,
            "TIK_Regler" => 0.0,
            "TIK_TauxRemise" => 0.0,
            "TIK_user" => $idUser,
            "TIK_Source" => "MOBILE",
            "TIK_Timbre" => 1.0
        ];
        return $ticket_0;
    }


}