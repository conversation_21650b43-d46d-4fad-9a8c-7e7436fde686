<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class UniteWSMobile extends Controller
{


    public function getUniteByCode(Request $request)
    {


        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('unite')->where('UNI_Code', $request->id)->first());

    }




}





