<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use App\Http\Controllers\FirebaseMessaging;
use App\Models\Client;
use App\Models\Devis;
use App\Models\LigneDevis;
use App\Models\Ticket;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

class BonCommandeWSMobile extends Controller
{

    public function getBonCommande(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $month = isset($data['object']['month']) ? (int) $data['object']['month'] : Carbon::now()->month;
        $year = isset($data['object']['year']) ? (int) $data['object']['year'] : Carbon::now()->year;
        $startOfMonth = Carbon::createFromDate($year, $month, 1)->startOfDay();
        $endOfMonth = Carbon::createFromDate($year, $month, 1)->endOfMonth()->endOfDay();

        $query = $connection->table('Devis')
            ->whereBetween('Devis.DEV_Date', [$startOfMonth, $endOfMonth]);


        if (($request->has('zone') && $request->input('zone') === true) || ($request->input('zone') === 'true' && $request->hasHeader('user'))) {
            $clients = array_column(Client::query()
                ->join('Zone', 'Client.Clt_Circuit', '=', 'Zone.CodeZone')
                ->join('ZoneUtilisateur', 'Zone.CodeZone', '=', 'ZoneUtilisateur.CodeZone')
                ->where('Zone.EtatZone', '=', true)
                ->where('ZoneUtilisateur.CodeUtilisateur', '=', $request->header('user'))
                ->select('Client.CLI_Code')
                ->get()->toArray(), 'CLI_Code');
            $query->whereIn('DEV_CodeClient', $clients);
        }

        return response()->json($query->get());
    }

    public function getBonCommandeWithPagination(Request $request)
    {
        $data = $request->json()->all();
        DatabaseConnection::setConnection($data);
   //     header('test3 : ' . $data['object']['limit']);
        $page = (int)$data['object']['page'] ?? 1;
        $limit = min((int) $data['object']['limit'] , 2000);
        $skip = ($page - 1) * $limit;

        $query = Devis::from('Devis')->with('lignesDevis');


        if (
            ($request->has('zone') && $request->input('zone') === true) ||
            ($request->input('zone') === 'true' && $request->hasHeader('user'))
        ) {
            $clients = array_column(
                Client::query()
                    ->join('Zone', 'Client.Clt_Circuit', '=', 'Zone.CodeZone')
                    ->join('ZoneUtilisateur', 'Zone.CodeZone', '=', 'ZoneUtilisateur.CodeZone')
                    ->where('Zone.EtatZone', true)
                    ->where('ZoneUtilisateur.CodeUtilisateur', $request->header('user'))
                    ->select('Client.CLI_Code')
                    ->get()
                    ->toArray(),
                'CLI_Code'
            );

            $query->whereIn('DEV_CodeClient', $clients);
        }


        $devisList = $query->skip($skip)->take($limit)->get();

        return response()->json([
            'data' => $devisList,
            'count' => $query->count(),
        ]);
    }


    public function getBonRetour(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Bon_Retour')->get());
    }

    public function addBatchBonCommande(Request $request)
    {
        $items = $request->json()->all();

        $result = array();

        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);

            foreach ($items["object"] as &$data) {
                $item = $data;
                $connection->beginTransaction();
                try {
                    $exist = $connection->table('devis')
                        ->where('DEV_Code_M', '=', $data['parent']['DEV_Code_M'])
                        ->where('DEV_Exerc', '=', $data['parent']['DEV_Exerc'])
                        ->where('DEV_User', '=', $data['parent']['DEV_User'])
                        ->where('DEV_StationOrigine', '=', $data['parent']['DEV_StationOrigine'])
                        ->first();
                    $soldeClient = Ticket::SoldeClient($connection, $data['parent']['DEV_CodeClient']);

                    if (!$exist) {
                        if (strtolower($data['parent']['DEV_info3']) === 'affectation' || strtolower($data['parent']['DEV_info3']) === 'sortie' || strtolower($data['parent']['DEV_info3']) === 'entree') {
                            $lignes_patrimones_apres_controles = array();
                            foreach ($data['children'] as $ligne) {
                                $client = new Client();
                                $verif_patrimoine = $this->verif_type_inventaire($data['parent']['DEV_info3'], $data['parent']['DEV_CodeClient'],
                                    $ligne['LG_DEV_NumSerie'], $connection, $client);

                                if ($verif_patrimoine === Enum::Error_article_pat_affecte) {
                                    $lignes_patrimones_apres_controles[] = $this->setResultLigneBC($ligne, Enum::Error_article_pat_affecte,
                                        "Article est dejà affecté dans l'emplacement : " . $client->Clt_ImoCode . "_" . $client->CLI_NomPren);
                                }
                                if ($verif_patrimoine === Enum::Error_num_serie_egale_code_barre_batiment) {
                                    $lignes_patrimones_apres_controles[] = $this->setResultLigneBC($ligne, Enum::Error_num_serie_egale_code_barre_batiment,
                                        "NumSerie  est dejà affecté à l'emplacement : " . $client->Clt_ImoCode . "_" . $client->CLI_NomPren);
                                }
                                if ($verif_patrimoine === Enum::Error_article_out) { ////
                                    $lignes_patrimones_apres_controles[] = $this->setResultLigneBC($ligne, Enum::Error_article_out, "Dernière type d'enregistrement est différent de (affectation/entree)");
                                }
                                if ($verif_patrimoine === Enum::Error_article_in) {
                                    $lignes_patrimones_apres_controles[] = $this->setResultLigneBC($ligne, Enum::Error_article_in, "Dernière type d'enregistrement est différent de sortie");
                                }


                            }

                            if (count($lignes_patrimones_apres_controles) !== 0) {
                                $result[] = $this->setResultBC($data["parent"]['DEV_Num'], $data['parent']['DEV_Exerc'],
                                    $data['parent']['DEV_StationOrigine'], $data['parent']['DEV_User'], $data['parent']['DEV_Code_M'], $data['parent']['DEV_info3'],
                                    "Patrimoine rejected", Enum::Error_patrimoine, $lignes_patrimones_apres_controles
                                );

                                // throw new \Exception("", Enum::Error_patrimoine);
                            }

                        }

                        $item["parent"]['DEV_Num'] = (new PrefixWSMobile)->getCommandePrefix(
                            $connection,
                            "devis",
                            $item["parent"]['DEV_Exerc'],
                            $item["parent"]['DEV_StationOrigine'],
                            $item["parent"]["DEV_User"],
                            $item["parent"]["DEV_info3"]
                        );
                        $data["parent"]['DEV_Num'] = $item["parent"]['DEV_Num'];
                        $item["parent"]['DEV_Date'] = AppHelper::setDateFormat($data["parent"]['DEV_Date']);
                        $item["parent"]['DEV_DDm'] = AppHelper::setDateFormat($data["parent"]['DEV_DDm']);
                        $item["parent"]["DEV_MntTTC"] = (float)$data["parent"]["DEV_MntTTC"];
                        $item["parent"]["DEV_MntTva"] = (float)$data["parent"]["DEV_MntTva"];
                        $item["parent"]["DEV_MntDC"] = (float)$data["parent"]["DEV_MntDC"];
                        $item["parent"]["DEV_MntFodec"] = (float)$data["parent"]["DEV_MntFodec"];
                        $item["parent"]["DEV_MntNetHt"] = (float)$data["parent"]["DEV_MntNetHt"];
                        $item["parent"]["DEV_Mntht"] = (float)$data["parent"]["DEV_Mntht"];

                        if (strtolower($data['parent']['DEV_info3']) === 'sortie') {
                            if (Schema::connection("onthefly")->hasColumn('Devis', 'DEV_Code_SF')) {
                                $item["parent"]["DEV_Code_SF"] = $data["parent"]["DEV_Code_SF"] ?? null;
                            }
                            if (Schema::connection("onthefly")->hasColumn('Devis', 'DEV_Code_SO')) {
                                $item["parent"]["DEV_Code_SO"] = $data["parent"]["DEV_Code_SO"] ?? null;
                            }
                            if (Schema::connection("onthefly")->hasColumn('Devis', 'DEV_TyMvtCode')) {
                                $item["parent"]["DEV_TyMvtCode"] = $data["parent"]["DEV_TyMvtCode"] ?? null;
                            }


                        }
                        if (strtolower($data['parent']['DEV_info3']) === 'sortie' && strtolower($data['parent']['DEV_info3']) === 'entree') {
                            $item["parent"]["DEV_EtatBon"] = "1";
                        }

                        $data["parent"] = $item["parent"];

                        $sucssesInsertBC = $connection->table('devis')->insert($data["parent"]);
                        if (!$sucssesInsertBC) {

                            $result[] = $this->setResultBC($data["parent"]['DEV_Num'], $data['parent']['DEV_Exerc'],
                                $data['parent']['DEV_StationOrigine'], $data['parent']['DEV_Station'], $data['parent']['DEV_User'], $data['parent']['DEV_Code_M'], $data['parent']['DEV_info3'],
                                "Error dans l'insertion du Bon Commande", Enum::Error_Insert_BC, []
                            );
                            //  throw new \Exception("", Enum::Error_Insert_BC);
                        } else {
                            $lignesBC = $data['children'];
                            $nbreLigneBc = count($lignesBC);
                            $testLigneBC = (new LigneBonCommandeWSMobile())->addBatchLigneBonCommande($lignesBC, $connection, $item["parent"]['DEV_Num'], $nbreLigneBc, $data['parent']['DEV_info3'], $data["parent"]["DEV_Etat"], $item["parent"]["DEV_User"]);
                            if (!$testLigneBC) {

                                $result[] = $this->setResultBC($data["parent"]['DEV_Num'], $data['parent']['DEV_Exerc'],
                                    $data['parent']['DEV_StationOrigine'], $data['parent']['DEV_Station'], $data['parent']['DEV_User'], $data['parent']['DEV_Code_M'], $data['parent']['DEV_info3'],
                                    "Error dans l'insertion des lignes Bon Commandes ", Enum::Error_Insert_LigneBC, []
                                );

                                //throw new \Exception("", Enum::Error_Insert_LigneBC);
                            } else {

                                $result[] = $this->setResultBC($data["parent"]['DEV_Num'], $data['parent']['DEV_Exerc'],
                                    $data['parent']['DEV_StationOrigine'], $data['parent']['DEV_Station'], $data['parent']['DEV_User'], $data['parent']['DEV_Code_M'], $data['parent']['DEV_info3'],
                                    "INSERTED", Enum::Success_Code, []
                                );
                            }

                        }


                    } else {

                        $result[] = $this->setResultBC($exist->DEV_Num, $exist->DEV_Exerc
                            , $exist->DEV_StationOrigine, $data['parent']['DEV_Station'], $exist->DEV_User, $exist->DEV_Code_M, $exist->DEV_info3,
                            "Bon Commande Existe Deja", Enum::Error_BC_Exite, []);
                    }


                    $connection->commit();
                    if (($data['parent']['DEV_info3']) === 'sortie') {
                        if (Schema::connection("onthefly")->hasColumn('Utilisateur', 'token')) {
                            $this->sendNotification($connection, $item["parent"]["DEV_User"], $item["parent"]['DEV_Num'],
                                $item["parent"]['DEV_CodeClient']);
                        }
                    }

                } catch (\Exception $e) {
                    $connection->rollBack();
                    return $e->getMessage();

                }

            }
        }

        return response()->json($result);
    }


    public function addBatchBonLivraisonMobile(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);

            $result = null;
            foreach ($items["object"] as $data) {
                if ($data["BON_Trans_Vehicule"] == "NULL" || $data["BON_Trans_Vehicule"] == null) {
                    $data["BON_Trans_Vehicule"] = null;
                }

                if ($data["BON_Trans_obs"] == "NULL" || $data["BON_Trans_obs"] == null) {
                    $data["BON_Trans_obs"] = null;
                }

                if ($data["Num_Manuelle"] == "NULL" || $data["Num_Manuelle"] == null) {
                    $data["Num_Manuelle"] = null;
                }

                if ($data["Observation"] == "NULL" || $data["Observation"] == null) {
                    $data["Observation"] = null;
                }

                if ($data["BON_Trans_Mnt_HT"] == "NULL" || $data["BON_Trans_Mnt_HT"] == null) {
                    $data["BON_Trans_Mnt_HT"] = "NULL";
                }

                if ($data["BON_Trans_Mnt_TTC"] == "NULL" || $data["BON_Trans_Mnt_TTC"] == null) {
                    $data["BON_Trans_Mnt_TTC"] = "NULL";
                }

                if ($data["BON_Trans_Etat_Saisie"] == "NULL" || $data["BON_Trans_Etat_Saisie"] == null) {
                    $data["BON_Trans_Etat_Saisie"] = null;
                }

                $data['DDm'] = AppHelper::setDateFormat($data['DDm']);
                $data['BON_Trans_DDm'] = AppHelper::setDateFormat($data['BON_Trans_DDm']);
                if (array_key_exists("BON_ENT_SYNC", $data)) {
                    unset($data["BON_ENT_SYNC"]);
                }
                $c = $connection->insert("Set Language French  SET DATEFORMAT 'ymd' 
                insert into [bon_transfert] ([BON_Trans_DDm], [BON_Trans_Date], [BON_Trans_Etat], [BON_Trans_Etat_Saisie],
                [BON_Trans_Exerc], [BON_Trans_export], [BON_Trans_Mnt_HT], [BON_Trans_Mnt_TTC], [BON_Trans_Num], [BON_Trans_obs],
                 [BON_Trans_Stat], [BON_Trans_StatDest], [BON_Trans_StatSource], [BON_Trans_Transporteur], [BON_Trans_User], [BON_Trans_Vehicule],
                  [DDm], [Declaree], [Exportation], [Num_Manuelle], [Observation])

                 values (
                     '" . $data['BON_Trans_DDm'] . "',
                  '" . $data["BON_Trans_Date"] . "',
                   '" . $data["BON_Trans_Etat"] . "',
                    '" . $data["BON_Trans_Etat_Saisie"] . "',
                 '" . $data["BON_Trans_Exerc"] . "',
                  '" . $data["BON_Trans_export"] . "',
                   " . $data["BON_Trans_Mnt_HT"] . ",
                    " . $data["BON_Trans_Mnt_TTC"] . ",
                     '" . $data["BON_Trans_Num"] . "',
                     '" . $data["BON_Trans_obs"] . "',
                 '" . $data["BON_Trans_Stat"] . "',
                  '" . $data["BON_Trans_StatDest"] . "',
                   '" . $data["BON_Trans_StatSource"] . "',
                   '" . $data["BON_Trans_Transporteur"] . "',
                    '" . $data["BON_Trans_User"] . "',
                     '" . $data["BON_Trans_Vehicule"] . "',
                 '" . $data["DDm"] . "',
                  '" . $data["Declaree"] . "',
                   '" . $data["Exportation"] . "',
                    '" . $data["Num_Manuelle"] . "',
                    '" . $data["Observation"] . "')");
                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }


    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data)->setlocale('fr');
    }

    /**
     * @param $lignesBC
     * @param $data
     * @return mixed
     */
    public function chanLineNumWithParentNum($lignesBC, $data)
    {
        for ($i = 0, $iMax = count($lignesBC); $i < $iMax; $i++) {
            $lignesBC[$i]["LG_DEV_NumBon"] = $data;
        }
        return $lignesBC;
    }

    public function controle_inventaire(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        $object = $items['object'];
        $client = new Client();
        $verif_patrimoine = $this->verif_type_inventaire($object['DEV_info3'], $object['DEV_CodeClient'], $object['LG_DEV_NumSerie'], $connection, $client);
        if ($verif_patrimoine === Enum::Error_article_pat_affecte) {
            return $this->setControlePatrimoine("Article est deja affecte", Enum::Error_article_pat_affecte);
        } else if ($verif_patrimoine === Enum::Error_article_out) {
            return $this->setControlePatrimoine("dernière type enregistrement different de (affectation/entree)", Enum::Error_article_pat_affecte);

        } else if ($verif_patrimoine === Enum::Error_article_in) {

        } else if ($verif_patrimoine === Enum::Error_inventaire_code_barre_batiment_) {
            return $this->setControlePatrimoine("Article est affecte a autre batiment " . $client->Clt_ImoCode . "_" . $client->CLI_NomPren, Enum::Error_article_in);
        } else if ($verif_patrimoine === Enum::Error_num_serie_egale_code_barre_batiment) {

            return $this->setControlePatrimoine("NumSerie  est dejà affecté à l'emplacement", Enum::Error_num_serie_egale_code_barre_batiment);
        } else {
            return $this->setControlePatrimoine("Ok", Enum::Success_Code);
        }
    }

    public function verif_type_inventaire($DEV_info3, $DEV_CodeClient, $LG_DEV_NumSerie, $connexion, $client)
    {
        if ($DEV_info3 == 'affectation') {
            $code_bar_article_affecte = $connexion->table('ligne_devis')
                ->join('devis', 'ligne_devis.LG_DEV_NumBon', '=', 'devis.DEV_Num')
                ->where('devis.DEV_info3', '!=', 'inventaire')
                ->pluck('ligne_devis.LG_DEV_NumSerie')
                ->toArray();

            if (in_array($LG_DEV_NumSerie, $code_bar_article_affecte, true)) {
                $newClient = $connexion->table('client')
                    ->join('Devis', 'Devis.DEV_CodeClient', '=', 'client.CLI_Code')
                    ->join('ligne_devis', 'Devis.DEV_Num', '=', 'ligne_devis.LG_DEV_NumBon')
                    ->where('ligne_devis.LG_DEV_NumSerie', '=', $LG_DEV_NumSerie)
                    ->select('client.*')
                    ->first();

                $client['CLI_Code'] = $newClient->CLI_Code;
                $client['CLI_NomPren'] = $newClient->CLI_NomPren;
                $client['Clt_ImoCode'] = $newClient->Clt_ImoCode;

                return Enum::Error_article_pat_affecte;
            }

            $verifCodeBarClient = $connexion->table('client')
                ->where('Clt_ImoCB', '=', $LG_DEV_NumSerie)
                ->first();
            if (!empty($verifCodeBarClient)) {
                $client['CLI_Code'] = $verifCodeBarClient->CLI_Code;
                $client['CLI_NomPren'] = $verifCodeBarClient->CLI_NomPren;
                $client['Clt_ImoCode'] = $verifCodeBarClient->Clt_ImoCode;

                return Enum::Error_num_serie_egale_code_barre_batiment;
            }

        } else if ($DEV_info3 == 'sortie') {
            $last_patrimoine = $connexion->table('devis')
                ->join('ligne_devis', 'DEV_Num', '=', 'LG_DEV_NumBon')
                ->where('DEV_CodeClient', '=', $DEV_CodeClient)
                ->where('LG_DEV_NumSerie', '=', $LG_DEV_NumSerie)
                ->orderBy('DEV_DDm', 'desc')
                ->pluck('DEV_info3')->first();

            if ($last_patrimoine != 'affectation' && $last_patrimoine != 'entree' && $last_patrimoine != 'inventaire') {
                return Enum::Error_article_out;
            }

        } else if ($DEV_info3 == 'entree') {
            $last_patrimoine = $connexion->table('devis')
                ->join('ligne_devis', 'DEV_Num', '=', 'LG_DEV_NumBon')
                ->where('LG_DEV_NumSerie', '=', $LG_DEV_NumSerie)
                ->orderBy('DEV_DDm', 'desc')
                ->pluck('DEV_info3')->first();

            if ($last_patrimoine !== 'sortie') {
                return Enum::Error_article_in;
            }

        } else if ($DEV_info3 == 'inventaire') {
            $last_patrimoine = $connexion->table('devis')
                ->join('ligne_devis', 'DEV_Num', '=', 'LG_DEV_NumBon')
                ->where('LG_DEV_NumSerie', '=', $LG_DEV_NumSerie)
                ->orderBy('DEV_DDm', 'desc')
                ->pluck('DEV_CodeClient')
                ->first();
            $verifCodeBarClient = $connexion->table('client')
                ->where('CLI_Code', '=', $last_patrimoine)
                ->first();
            if ($last_patrimoine !== $DEV_CodeClient && !empty($last_patrimoine)) {
                $client['CLI_Code'] = $verifCodeBarClient->CLI_Code;
                $client['CLI_NomPren'] = $verifCodeBarClient->CLI_NomPren;
                return Enum::Error_inventaire_code_barre_batiment_;
            }

        } else {
            return Enum::Success_Code;
        }

    }

    public function getAllTypeMouvement(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('TypeMvtDepOut')->get());
    }

//
//   public function getAllDeplacememntOutByUser(Request $request)
//    {
//        $data = $request->json()->all();
//        $connection = DatabaseConnection::setConnection($data);
//        $devis = array();
//        if (!!Request::createFromGlobals()->hasHeader('user')) {
//            $idUser = $request->header('user');
//            $devis = Devis::join('UserSite', 'UserSite.CodeSitF', '=', 'Devis.DEV_Code_SF')
//                ->where('UserSite.codeUtilisateur', '=', $idUser)
//                ->with('lignesDevis')
//                ->get();
//        }
//
//        return response()->json($devis);
//    }
//
    public function getAllDeplacememntOutByUser(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $devis = array();

        if ($request->hasHeader('user')) {
            $idUser = $request->header('user');


            $devis = Devis::leftJoin('UserSite', 'UserSite.CodeSitF', '=', 'Devis.DEV_Code_SF')
                ->where('UserSite.codeUtilisateur', '=', $idUser)
                ->select("DEV_EtatBon", 'Devis.DEV_Num', 'Devis.DEV_Exerc', 'Devis.DEV_Date', 'Devis.DEV_CodeClient', 'Devis.DEV_StationOrigine', 'Devis.DEV_Etat', 'Devis.DEV_Station', 'Devis.DEV_User', 'Devis.DEV_Mntht', 'Devis.DEV_MntNetHt', 'Devis.DEV_MntTva', 'Devis.DEV_MntTTC', 'Devis.DEV_TauxRemise', 'Devis.DEV_Remise', 'Devis.DEV_Regler', 'Devis.DEV_export', 'Devis.DEV_DDm', 'Devis.DEV_Code_SF') // Sélectionner uniquement les colonnes nécessaires
                ->groupBy('DEV_EtatBon', 'Devis.DEV_Num', 'Devis.DEV_Exerc', 'Devis.DEV_Date', 'Devis.DEV_CodeClient', 'Devis.DEV_StationOrigine', 'Devis.DEV_Etat', 'Devis.DEV_Station', 'Devis.DEV_User', 'Devis.DEV_Mntht', 'Devis.DEV_MntNetHt', 'Devis.DEV_MntTva', 'Devis.DEV_MntTTC', 'Devis.DEV_TauxRemise', 'Devis.DEV_Remise', 'Devis.DEV_Regler', 'Devis.DEV_export', 'Devis.DEV_DDm', 'Devis.DEV_Code_SF') // Utiliser groupBy pour les colonnes sélectionnées
                ->with('lignesDevis')
                ->get();
        }

        return response()->json($devis);
    }

    public function sendNotification($connection, $userCode, $idDevis, $DEV_CodeClient)
    {

        $tokens = $connection->table('UserSite')
            ->join('Utilisateur', 'Utilisateur.Code_Ut', '=', 'UserSite.codeUtilisateur')
            ->join('Devis', 'Devis.DEV_Code_SF', '=', 'UserSite.CodeSitF')
            ->select('Utilisateur.token')
            ->where('Devis.DEV_Num', $idDevis)
            ->where('Utilisateur.token', '!=', '')
            ->pluck('token')
            ->toArray();
        $client = $connection->table('client')
            ->where('CLI_Code', $DEV_CodeClient)
            ->select('CLI_NomPren')
            ->first();
        $user = $connection->table('Utilisateur')
            ->where('Code_Ut', $userCode)
            ->select('Nom', 'Prenom')
            ->first();

        $username = $user->Nom . " " . $user->Prenom;


        $title = "Vous avez un déplacement out N° " . $idDevis;
        $message = "Vous avez un déplacement out N° " . $idDevis . " de la part du batiment '" . $client->CLI_NomPren
            . "' effectué par '" . $username . "'";


        $notification = app()->make(FirebaseMessaging::class)->callAction('sendNotification', [$tokens, $title, $message]);


    }

}
