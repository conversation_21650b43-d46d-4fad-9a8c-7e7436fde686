<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use Exception;

class DepenseWSMobile extends Controller
{


    public function getDepenseByDay(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('View_RecapVente')->whereBetween('Datecomp', array(($data["object"][0]), $data["object"][1]))->first());

    }

    public function getTypeDepence(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $typeDepense = $connection->table('DEPENCE')->get();
        return response()->json($typeDepense);

    }

    public function addBatchDepense(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        $result = array();
        foreach ($items['object'] as $depense) {
            $connection->beginTransaction();
            try {
                $depense['DEP_DDm'] = AppHelper::setDateFormat($depense['DEP_DDm']);
                $depense['DDm'] = AppHelper::setDateFormat($depense['DDm']);

                $query = $connection->table("depence_caisse")
                    ->where('DEP_Code', '=', $depense["DEP_Code"])
                    ->where('DEP_SC_IdSCession', '=', $depense["DEP_SC_IdSCession"])
                    ->where('DEP_SC_Caisse', '=', $depense["DEP_SC_Caisse"])
                    ->where('DEP_User', '=', $depense["DEP_User"])
                    ->where('DEP_Montant', '=', $depense["DEP_Montant"])
                    ->where('DDm', '=', $depense["DDm"])
                    ->where('DEP_Code_M', '=', $depense["DEP_Code_M"]);

                $exist = $query->first();
                if (!$exist) {
                    $succesInsertDepense = $connection->table('depence_caisse')->insert($depense);
                    if (!$succesInsertDepense) {
                        $result[] = [
                            "DEP_Code" => $depense["DEP_Code"],
                            "message" => "Erreur dans l'insertion Depense_caisse" . $depense["DEP_Code"],

                            "code" => Enum::Error_Insert_Depense_caisse
                        ];
                        throw new Exception("", Error_Insert_Depense_caisse);

                    }

                } else {
                    $succesUpdateDepense = $connection->table('depence_caisse')
                        ->where("DEP_Code", $depense["DEP_Code"])
                        ->where("DEP_SC_IdSCession", $depense["DEP_SC_IdSCession"])
                        ->where("DEP_SC_Caisse", $depense["DEP_SC_Caisse"])
                        ->where("DEP_User", $depense["DEP_User"])
                        ->where("DEP_Montant", $depense["DEP_Montant"])
                        ->where("DDm", $depense["DDm"])
                        ->where("DEP_Code_M", $depense["DEP_Code_M"])
                        ->update([
                            "DEP_descrip" => $depense["DEP_descrip"] ?? null,
                            "DEP_export" => $depense["DEP_export"] ?? null,
                            "DEP_DDm" => $depense["DEP_DDm"] ?? null,
                            "export" => $depense["export"] ?? null,
                        ]);
                    if (!$succesUpdateDepense) {
                        $result[] = [
                            "DEP_Code" => $depense["DEP_Code"],
                            "message" => "Erreur dans la modification Depense_caisse" . $depense["DEP_Code"],
                            "code" => Enum::Error_Update_Depense_caisse
                        ];
                        throw new Exception("", Error_Update_Depense_caisse);
                    }
                }

                $connection->commit();
                $result[] = [
                    "DEP_Code" => $depense["DEP_Code"],
                    "DEP_SC_IdSCession" => $depense["DEP_SC_IdSCession"],
                    "DEP_SC_Caisse" => $depense["DEP_SC_Caisse"],
                    "DEP_User" => $depense["DEP_User"],
                    "DEP_Montant" => $depense["DEP_Montant"],
                    "DDm" => $depense["DDm"],
                    "DEP_Code_M" => $depense["DEP_Code_M"],
                ];
            } catch
            (\Exception $e) {
                $connection->rollBack();
            }
        }
        return response()->json($result);
    }

    public function getAllDepense(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $query = array();
        if (!!Request::createFromGlobals()->hasHeader('user')) {
            $user = $request->Header('user');
            $query = $connection->table('depence_caisse ')
                ->where("DEP_User", $user)
                ->orderby('DDm', 'desc')
                ->get();
        }
        return response()->json($query);
    }

    public function deleteDepense(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $depense = $data["object"];
        $depense['DDm'] = AppHelper::setDateFormat($depense['DDm']);
        $query = $connection->table('depence_caisse ')
            ->where("DEP_Code", $depense["DEP_Code"])
            ->where("DEP_SC_IdSCession", $depense["DEP_SC_IdSCession"])
            ->where("DEP_SC_Caisse", $depense["DEP_SC_Caisse"])
            ->where("DEP_User", $depense["DEP_User"])
            ->where("DEP_Montant", $depense["DEP_Montant"])
            ->where("DEP_Code_M", $depense["DEP_Code_M"])
            //     ->where("DDm","=", $depense["DDm"])
            ->delete();
        return response()->json(($query == 1) ? true : false);
    }

    public function addBatchTypeDepense(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        $result = array();
        foreach ($items['object'] as $depense) {
            $connection->beginTransaction();
            try {
                $depense['DEP_DDm'] = AppHelper::setDateFormat($depense['DEP_DDm']);

                $query = $connection->table("DEPENCE")
                    ->where('DEP_Code', '=', $depense["DEP_Code"])
                    ->where('DEP_Code_M', '=', $depense["DEP_Code_M"]);

                $exist = $query->first();
                if (!$exist) {
                    $prefixController = new PrefixWSMobile();
                    $depense["DEP_Code"] = $prefixController->getDepensePrefix($connection,
                        "DEPENCE",
                        $depense['DEP_station']);

                    $succesInsertDepense = $connection->table('DEPENCE')->insert($depense);

                    if (!$succesInsertDepense) {
                        $result[] = [
                            "DEP_Code" => $depense["DEP_Code"],
                            "message" => "Erreur dans l'insertion Depence" . $depense["DEP_Code"],
                            "code" => Enum::Error_Insert_Depense
                        ];
                        throw new Exception("", Error_Insert_Depense);
                    }

                } else {
                    $succesUpdateDepense = $connection->table('DEPENCE')
                        ->where("DEP_Code", $depense["DEP_Code"])
                        ->where("DEP_Code_M", $depense["DEP_Code_M"])
                        ->update([
                            "DEP_Lib" => $depense["DEP_Lib"] ?? null,
                            "DEP_IsTactile" => $depense["DEP_IsTactile"] ?? null,
                            "DEP_user" => $depense["DEP_user"] ?? null,
                            "DEP_station" => $depense["DEP_station"] ?? null,
                            "DEP_export" => $depense["DEP_export"] ?? null,
                            "DEP_DDm" => $depense["DEP_DDm"] ?? null,
                        ]);

                    if (!$succesUpdateDepense) {
                        $result[] = [
                            "DEP_Code" => $depense["DEP_Code"],
                            "message" => "Erreur dans l'insertion DEPENCE" . $depense["DEP_Code"],
                            "code" => Enum::Error_Update_Depense
                        ];
                        throw new Exception("", Error_Update_Depense);
                    }
                }
                $connection->commit();
                $result[] = [
                    "DEP_Code" => $depense["DEP_Code"],
                    "DEP_Code_M" => $depense["DEP_Code_M"],
                ];
            } catch
            (\Exception $e) {
                $connection->rollBack();
            }
        }
        return response()->json($result);
    }

    public function getDepenseByIdCaisse(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $item=$data['object'];
            $query = $connection->table('depence_caisse ');
            if(!empty($item['DDm'])){
                $query=$query->where('DDm', $item['DDm']);
            }

              $query=$query->where("DEP_SC_Caisse", $item['DEP_SC_Caisse'])
                  ->get();

        return response()->json($query);
    }
}





