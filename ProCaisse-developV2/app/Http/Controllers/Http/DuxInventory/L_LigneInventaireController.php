<?php

namespace App\Http\Controllers\Http\DuxInventory;

use App\Helpers\DatabaseConnection;
use App\Models\DuxInventory\BaseModel;
use App\Models\DuxInventory\L_LigneInventaire;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;

class L_LigneInventaireController extends Controller
{
    public function getLigneInventaires($idInventaire)
    {
        try {
            $lignesInventaire = L_LigneInventaire::where('idInventaire', $idInventaire)->get();
            return response()->success($lignesInventaire);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }


    public function addLigneInventaires(Request $request, $linge)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        try {
            $linge['id'] = (new BaseModel())->getID();
            $ligneInventaire = L_LigneInventaire::create($linge);
            $idStation = $request['object']['idStation'];
            $connection->statement("EXEC [updateCodes] 'L_LigneInventaire',$idStation;");
            return $ligneInventaire;
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
