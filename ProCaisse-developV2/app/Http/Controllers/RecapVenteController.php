<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Svg\Tag\Group;

class RecapVenteController extends Controller
{
    public function getRecapVente(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $item = $data['object'];

        $result = $connection->table('View_Journal_Reglement_001_3')
            ->selectRaw('SUM(Mnt_Espece) AS Mnt_Espece, SUM(Mnt_Carte_Bancaire) AS Mnt_Carte_Bancaire, SUM(Mnt_Cheque) AS Mnt_Cheque, SUM(Mnt_Traite) AS Mnt_Traite,
                         SUM(Mnt_Espece) + SUM(Mnt_Carte_Bancaire) + SUM(Mnt_Cheque) + SUM(Mnt_Traite) + SUM(ISNULL(MntCarte_prepayee, 0)) + SUM(ISNULL(Mnt_PointMerci, 0))

                     + SUM(ISNULL(MntBonAchat, 0)) + SUM(ISNULL(Mnt_Bonus, 0)) AS TotalRecette, SUM(ISNULL(Mnt_Bonus, 0)) AS Mnt_Bonus, 
                     SUM(DISTINCT SC_FondCaisse) AS Fond_Caisse, SUM(DISTINCT SC_TotDepense) AS Dep_Caisse, SUM(ISNULL(MntCarte_prepayee, 0)) AS MntCarte_prepayee,
                     SUM(ISNULL(Mnt_PointMerci, 0)) AS Mnt_PointMerci, SUM(ISNULL(MntBonAchat, 0)) AS MntBonAchat')
            ->where("REGC_IdSCaisse", $item['REGC_IdSCaisse'])
            ->groupBy('REGC_IdSCaisse')
            ->groupBy('NomPrenUtil')
            ->first();

        // $res=$ppp=floatval(Mnt_Espece)+floatval(Mnt_Carte_Bancaire)+floatval(Mnt_Cheque)+floatval(Mnt_Traite)+floatval(total)+floatval(Mnt_Bonus)+floatval()+floatval()+floatval()+floatval()+floatval()
        $totalCaisse = floatval($result->TotalRecette) + floatval($result->Fond_Caisse) - floatval($result->Dep_Caisse);

        $resultArray = (array)$result;
        $array = [

            "TotalCaisse" => (string)$totalCaisse
        ];
        $resultat_final = array_merge($resultArray, $array);


        return response()->json($resultat_final);

    }
}

