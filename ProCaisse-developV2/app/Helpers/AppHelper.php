<?php


namespace App\Helpers;

use Carbon\Carbon;

class AppHelper
{

    public static function getNumber($n)
    {
        $n_splited = explode(".", $n);
        if (count($n_splited) > 1) {
            if (strpos($n_splited[1], "E-")) {
                $n_splited[1] = str_replace("E-", "", $n_splited[1]);
            }
            if (strpos($n_splited[1], "e-")) {
                $n_splited[1] = str_replace("e-", "", $n_splited[1]);
            }
            return $n_splited[0] . '.' . substr($n_splited[1], 0, 6);
        }
        if (strpos($n, "E-")) {
            $n = str_replace("E-", "", $n);
        }
        if (strpos($n, "e-")) {
            $n = str_replace("e-", "", $n);
        }
        return $n;
    }

    public static function setDateFormat($date = null, $format = "Y-m-d H:i:s"): string
    {
        try {

            $format = env("FORCED_DATE_FORMAT", false) ? env("DATE_FORMAT", $format) : $format;
            if (!is_null($date)) {
                return Carbon::createFromTimeString($date ?? new \DateTime())->format($format);

            } else {
                return Carbon::now()->format($format);
            }
        } catch (\Exception $e) {
            return Carbon::now()->format($format);
        }
    }

    public static function getDateFormat($connection)
    {
        $dates = ["MM/dd/yyyy HH:mm:ss" => "10/23/2021", "yyyy-MM-dd HH:mm:ss" => "2021-10-23", "dd-MM-yyyy HH:mm:ss" => "23-10-2021",];

        foreach ($dates as $key => $value) {
            if ((new AppHelper)->checkDateValidity($connection, $value)) {
                return $key;
            }
        }

        if (env("FORCED_DATE_FORMAT", false)) {
            switch (env("DATE_FORMAT", "Y-m-d H:i:s")) {
                case "d-m-Y H:i:s":
                    return "dd-MM-yyyy HH:mm:ss";
                case "m/d/Y H:i:s":
                    return "MM/dd/yyyy HH:mm:ss";
                case "Y-m-d H:i:":
                default:
                    return "yyyy-MM-dd HH:mm:ss";
            }
        }

        return "yyyy-MM-dd HH:mm:ss";

    }

    private function checkDateValidity($connection, $value)
    {
        try {
            $connection->table('Utilisateur')->select('*')->where("DDm", "=", $value)->get();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

}