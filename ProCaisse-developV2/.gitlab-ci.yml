stages:
  - docker

build-php:
  stage: docker
  image: docker:latest
  only:
    - tags
  tags:
    - docker
  script:
    - docker login -u ya<PERSON><PERSON><PERSON>@asm-dev.com -p 12031995 $CI_REGISTRY
    - docker build -t $CI_REGISTRY_IMAGE/php:$CI_COMMIT_REF_NAME -f Dockerfile-php .
    - docker push $CI_REGISTRY_IMAGE/php:$CI_COMMIT_REF_NAME

build-nginx:
  stage: docker
  image: docker:latest
  only:
    - tags
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_REF_NAME -f Dockerfile-back-end .
    - docker push $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_REF_NAME


