version: '3.5'

networks:
  frontend:
    driver: ${NETWORKS_DRIVER}
  backend:
    driver: ${NETWORKS_DRIVER}
volumes:
  redis:
    driver: ${VOLUMES_DRIVER}Z
  caddy:
    driver: ${VOLUMES_DRIVER}

services:
### Workspace Utilities ##################################
    workspace:
      build:
        context: ./workspace
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - SHELL_OH_MY_ZSH=${SHELL_OH_MY_ZSH}
          - SHELL_OH_MY_ZSH_AUTOSUGESTIONS=${SHELL_OH_MY_ZSH_AUTOSUGESTIONS}
          - SHELL_OH_MY_ZSH_ALIASES=${SHELL_OH_MY_ZSH_ALIASES}
          - BASE_IMAGE_TAG_PREFIX=${WORKSPACE_BASE_IMAGE_TAG_PREFIX}
          - LARADOCK_PHP_VERSION=${PHP_VERSION}
          - LARADOCK_PHALCON_VERSION=${PHALCON_VERSION}
          - INSTALL_SUBVERSION=${WORKSPACE_INSTALL_SUBVERSION}
          - INSTALL_BZ2=${WORKSPACE_INSTALL_BZ2}
          - INSTALL_GMP=${WORKSPACE_INSTALL_GMP}
          - INSTALL_GNUPG=${WORKSPACE_INSTALL_GNUPG}
          - INSTALL_XDEBUG=${WORKSPACE_INSTALL_XDEBUG}
          - INSTALL_PCOV=${WORKSPACE_INSTALL_PCOV}
          - INSTALL_PHPDBG=${WORKSPACE_INSTALL_PHPDBG}
          - INSTALL_SSH2=${WORKSPACE_INSTALL_SSH2}
          - INSTALL_SOAP=${WORKSPACE_INSTALL_SOAP}
          - INSTALL_XSL=${WORKSPACE_INSTALL_XSL}
          - INSTALL_LDAP=${WORKSPACE_INSTALL_LDAP}
          - INSTALL_SMB=${WORKSPACE_INSTALL_SMB}
          - INSTALL_IMAP=${WORKSPACE_INSTALL_IMAP}
          - INSTALL_MONGO=${WORKSPACE_INSTALL_MONGO}
          - INSTALL_AMQP=${WORKSPACE_INSTALL_AMQP}
          - INSTALL_CASSANDRA=${WORKSPACE_INSTALL_CASSANDRA}
          - INSTALL_GEARMAN=${WORKSPACE_INSTALL_GEARMAN}
          - INSTALL_PHPREDIS=${WORKSPACE_INSTALL_PHPREDIS}
          - INSTALL_MSSQL=${WORKSPACE_INSTALL_MSSQL}
          - NVM_NODEJS_ORG_MIRROR=${WORKSPACE_NVM_NODEJS_ORG_MIRROR}
          - INSTALL_NODE=${WORKSPACE_INSTALL_NODE}
          - NPM_REGISTRY=${WORKSPACE_NPM_REGISTRY}
          - NPM_FETCH_RETRIES=${WORKSPACE_NPM_FETCH_RETRIES}
          - NPM_FETCH_RETRY_FACTOR=${WORKSPACE_NPM_FETCH_RETRY_FACTOR}
          - NPM_FETCH_RETRY_MINTIMEOUT=${WORKSPACE_NPM_FETCH_RETRY_MINTIMEOUT}
          - NPM_FETCH_RETRY_MAXTIMEOUT=${WORKSPACE_NPM_FETCH_RETRY_MAXTIMEOUT}
          - INSTALL_PNPM=${WORKSPACE_INSTALL_PNPM}
          - INSTALL_YARN=${WORKSPACE_INSTALL_YARN}
          - INSTALL_NPM_GULP=${WORKSPACE_INSTALL_NPM_GULP}
          - INSTALL_NPM_BOWER=${WORKSPACE_INSTALL_NPM_BOWER}
          - INSTALL_NPM_VUE_CLI=${WORKSPACE_INSTALL_NPM_VUE_CLI}
          - INSTALL_NPM_ANGULAR_CLI=${WORKSPACE_INSTALL_NPM_ANGULAR_CLI}
          - INSTALL_DRUSH=${WORKSPACE_INSTALL_DRUSH}
          - INSTALL_WP_CLI=${WORKSPACE_INSTALL_WP_CLI}
          - INSTALL_DRUPAL_CONSOLE=${WORKSPACE_INSTALL_DRUPAL_CONSOLE}
          - INSTALL_AEROSPIKE=${WORKSPACE_INSTALL_AEROSPIKE}
          - INSTALL_OCI8=${WORKSPACE_INSTALL_OCI8}
          - INSTALL_V8JS=${WORKSPACE_INSTALL_V8JS}
          - COMPOSER_GLOBAL_INSTALL=${WORKSPACE_COMPOSER_GLOBAL_INSTALL}
          - COMPOSER_VERSION=${WORKSPACE_COMPOSER_VERSION}
          - COMPOSER_AUTH=${WORKSPACE_COMPOSER_AUTH}
          - COMPOSER_REPO_PACKAGIST=${WORKSPACE_COMPOSER_REPO_PACKAGIST}
          - INSTALL_WORKSPACE_SSH=${WORKSPACE_INSTALL_WORKSPACE_SSH}
          - INSTALL_LARAVEL_ENVOY=${WORKSPACE_INSTALL_LARAVEL_ENVOY}
          - INSTALL_LARAVEL_INSTALLER=${WORKSPACE_INSTALL_LARAVEL_INSTALLER}
          - INSTALL_DEPLOYER=${WORKSPACE_INSTALL_DEPLOYER}
          - INSTALL_PRESTISSIMO=${WORKSPACE_INSTALL_PRESTISSIMO}
          - INSTALL_LINUXBREW=${WORKSPACE_INSTALL_LINUXBREW}
          - INSTALL_MC=${WORKSPACE_INSTALL_MC}
          - INSTALL_SYMFONY=${WORKSPACE_INSTALL_SYMFONY}
          - INSTALL_PYTHON=${WORKSPACE_INSTALL_PYTHON}
          - INSTALL_PYTHON3=${WORKSPACE_INSTALL_PYTHON3}
          - INSTALL_IMAGE_OPTIMIZERS=${WORKSPACE_INSTALL_IMAGE_OPTIMIZERS}
          - INSTALL_IMAGEMAGICK=${WORKSPACE_INSTALL_IMAGEMAGICK}
          - INSTALL_TERRAFORM=${WORKSPACE_INSTALL_TERRAFORM}
          - INSTALL_DUSK_DEPS=${WORKSPACE_INSTALL_DUSK_DEPS}
          - INSTALL_PG_CLIENT=${WORKSPACE_INSTALL_PG_CLIENT}
          - INSTALL_PHALCON=${WORKSPACE_INSTALL_PHALCON}
          - INSTALL_SWOOLE=${WORKSPACE_INSTALL_SWOOLE}
          - INSTALL_TAINT=${WORKSPACE_INSTALL_TAINT}
          - INSTALL_LIBPNG=${WORKSPACE_INSTALL_LIBPNG}
          - INSTALL_GRAPHVIZ=${WORKSPACE_INSTALL_GRAPHVIZ}
          - INSTALL_IONCUBE=${WORKSPACE_INSTALL_IONCUBE}
          - INSTALL_MYSQL_CLIENT=${WORKSPACE_INSTALL_MYSQL_CLIENT}
          - INSTALL_PING=${WORKSPACE_INSTALL_PING}
          - INSTALL_SSHPASS=${WORKSPACE_INSTALL_SSHPASS}
          - INSTALL_INOTIFY=${WORKSPACE_INSTALL_INOTIFY}
          - INSTALL_FSWATCH=${WORKSPACE_INSTALL_FSWATCH}
          - INSTALL_AST=${WORKSPACE_INSTALL_AST}
          - INSTALL_YAML=${WORKSPACE_INSTALL_YAML}
          - INSTALL_RDKAFKA=${WORKSPACE_INSTALL_RDKAFKA}
          - INSTALL_MAILPARSE=${WORKSPACE_INSTALL_MAILPARSE}
          - INSTALL_GIT_PROMPT=${WORKSPACE_INSTALL_GIT_PROMPT}
          - INSTALL_XMLRPC=${WORKSPACE_INSTALL_XMLRPC}
          - PUID=${WORKSPACE_PUID}
          - PGID=${WORKSPACE_PGID}
          - CHROME_DRIVER_VERSION=${WORKSPACE_CHROME_DRIVER_VERSION}
          - NODE_VERSION=${WORKSPACE_NODE_VERSION}
          - YARN_VERSION=${WORKSPACE_YARN_VERSION}
          - DRUSH_VERSION=${WORKSPACE_DRUSH_VERSION}
          - AST_VERSION=${WORKSPACE_AST_VERSION}
          - IMAGEMAGICK_VERSION=${WORKSPACE_IMAGEMAGICK_VERSION}
          - TZ=${WORKSPACE_TIMEZONE}
          - INSTALL_POWERLINE=${WORKSPACE_INSTALL_POWERLINE}
          - INSTALL_SUPERVISOR=${WORKSPACE_INSTALL_SUPERVISOR}
          - INSTALL_FFMPEG=${WORKSPACE_INSTALL_FFMPEG}
          - INSTALL_AUDIOWAVEFORM=${WORKSPACE_INSTALL_AUDIOWAVEFORM}
          - INSTALL_WKHTMLTOPDF=${WORKSPACE_INSTALL_WKHTMLTOPDF}
          - INSTALL_GNU_PARALLEL=${WORKSPACE_INSTALL_GNU_PARALLEL}
          - INSTALL_LNAV=${WORKSPACE_INSTALL_LNAV}
          - INSTALL_PROTOC=${WORKSPACE_INSTALL_PROTOC}
          - INSTALL_PHPDECIMAL=${WORKSPACE_INSTALL_PHPDECIMAL}
          - INSTALL_ZOOKEEPER=${WORKSPACE_INSTALL_ZOOKEEPER}
          - INSTALL_SSDB=${WORKSPACE_INSTALL_SSDB}
          - INSTALL_TRADER=${WORKSPACE_INSTALL_TRADER}
          - PROTOC_VERSION=${WORKSPACE_PROTOC_VERSION}
          - INSTALL_DOCKER_CLIENT=${WORKSPACE_INSTALL_DOCKER_CLIENT}
          - INSTALL_MEMCACHED=${WORKSPACE_INSTALL_MEMCACHED}
          - http_proxy
          - https_proxy
          - no_proxy
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - ./php-worker/supervisord.d:/etc/supervisord.d
      extra_hosts:
        - "dockerhost:${DOCKER_HOST_IP}"
      ports:
        - "${WORKSPACE_SSH_PORT}:22"
        - "${WORKSPACE_BROWSERSYNC_HOST_PORT}:3000"
        - "${WORKSPACE_BROWSERSYNC_UI_HOST_PORT}:3001"
        - "${WORKSPACE_VUE_CLI_SERVE_HOST_PORT}:8080"
        - "${WORKSPACE_VUE_CLI_UI_HOST_PORT}:8000"
        - "${WORKSPACE_ANGULAR_CLI_SERVE_HOST_PORT}:4200"
      tty: true
      networks:
        - frontend
        - backend

### PHP-FPM ##############################################
    php-fpm:
      build:
        context: ./php-fpm
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - BASE_IMAGE_TAG_PREFIX=${PHP_FPM_BASE_IMAGE_TAG_PREFIX}
          - LARADOCK_PHP_VERSION=${PHP_VERSION}
          - LARADOCK_PHALCON_VERSION=${PHALCON_VERSION}
          - INSTALL_BZ2=${PHP_FPM_INSTALL_BZ2}
          - INSTALL_ENCHANT=${PHP_FPM_INSTALL_ENCHANT}
          - INSTALL_GMP=${PHP_FPM_INSTALL_GMP}
          - INSTALL_GNUPG=${PHP_FPM_INSTALL_GNUPG}
          - INSTALL_XDEBUG=${PHP_FPM_INSTALL_XDEBUG}
          - INSTALL_PCOV=${PHP_FPM_INSTALL_PCOV}
          - INSTALL_PHPDBG=${PHP_FPM_INSTALL_PHPDBG}
          - INSTALL_SSH2=${PHP_FPM_INSTALL_SSH2}
          - INSTALL_SOAP=${PHP_FPM_INSTALL_SOAP}
          - INSTALL_XSL=${PHP_FPM_INSTALL_XSL}
          - INSTALL_SMB=${PHP_FPM_INSTALL_SMB}
          - INSTALL_IMAP=${PHP_FPM_INSTALL_IMAP}
          - INSTALL_MONGO=${PHP_FPM_INSTALL_MONGO}
          - INSTALL_AMQP=${PHP_FPM_INSTALL_AMQP}
          - INSTALL_CASSANDRA=${PHP_FPM_INSTALL_CASSANDRA}
          - INSTALL_GEARMAN=${PHP_FPM_INSTALL_GEARMAN}
          - INSTALL_MSSQL=${PHP_FPM_INSTALL_MSSQL}
          - INSTALL_BCMATH=${PHP_FPM_INSTALL_BCMATH}
          - INSTALL_PHPREDIS=${PHP_FPM_INSTALL_PHPREDIS}
          - INSTALL_MEMCACHED=${PHP_FPM_INSTALL_MEMCACHED}
          - INSTALL_OPCACHE=${PHP_FPM_INSTALL_OPCACHE}
          - INSTALL_EXIF=${PHP_FPM_INSTALL_EXIF}
          - INSTALL_AEROSPIKE=${PHP_FPM_INSTALL_AEROSPIKE}
          - INSTALL_OCI8=${PHP_FPM_INSTALL_OCI8}
          - INSTALL_MYSQLI=${PHP_FPM_INSTALL_MYSQLI}
          - INSTALL_PGSQL=${PHP_FPM_INSTALL_PGSQL}
          - INSTALL_PG_CLIENT=${PHP_FPM_INSTALL_PG_CLIENT}
          - INSTALL_POSTGIS=${PHP_FPM_INSTALL_POSTGIS}
          - INSTALL_INTL=${PHP_FPM_INSTALL_INTL}
          - INSTALL_GHOSTSCRIPT=${PHP_FPM_INSTALL_GHOSTSCRIPT}
          - INSTALL_LDAP=${PHP_FPM_INSTALL_LDAP}
          - INSTALL_PHALCON=${PHP_FPM_INSTALL_PHALCON}
          - INSTALL_SWOOLE=${PHP_FPM_INSTALL_SWOOLE}
          - INSTALL_TAINT=${PHP_FPM_INSTALL_TAINT}
          - INSTALL_IMAGE_OPTIMIZERS=${PHP_FPM_INSTALL_IMAGE_OPTIMIZERS}
          - INSTALL_IMAGEMAGICK=${PHP_FPM_INSTALL_IMAGEMAGICK}
          - INSTALL_CALENDAR=${PHP_FPM_INSTALL_CALENDAR}
          - INSTALL_FAKETIME=${PHP_FPM_INSTALL_FAKETIME}
          - INSTALL_IONCUBE=${PHP_FPM_INSTALL_IONCUBE}
          - INSTALL_APCU=${PHP_FPM_INSTALL_APCU}
          - INSTALL_CACHETOOL=${PHP_FPM_INSTALL_CACHETOOL}
          - INSTALL_YAML=${PHP_FPM_INSTALL_YAML}
          - INSTALL_RDKAFKA=${PHP_FPM_INSTALL_RDKAFKA}
          - INSTALL_GETTEXT=${PHP_FPM_INSTALL_GETTEXT}
          - INSTALL_ADDITIONAL_LOCALES=${PHP_FPM_INSTALL_ADDITIONAL_LOCALES}
          - INSTALL_MYSQL_CLIENT=${PHP_FPM_INSTALL_MYSQL_CLIENT}
          - INSTALL_PING=${PHP_FPM_INSTALL_PING}
          - INSTALL_SSHPASS=${PHP_FPM_INSTALL_SSHPASS}
          - INSTALL_MAILPARSE=${PHP_FPM_INSTALL_MAILPARSE}
          - INSTALL_PCNTL=${PHP_FPM_INSTALL_PCNTL}
          - ADDITIONAL_LOCALES=${PHP_FPM_ADDITIONAL_LOCALES}
          - INSTALL_FFMPEG=${PHP_FPM_FFMPEG}
          - INSTALL_AUDIOWAVEFORM=${PHP_FPM_AUDIOWAVEFORM}
          - INSTALL_WKHTMLTOPDF=${PHP_FPM_INSTALL_WKHTMLTOPDF}
          - INSTALL_XHPROF=${PHP_FPM_INSTALL_XHPROF}
          - INSTALL_XMLRPC=${PHP_FPM_INSTALL_XMLRPC}
          - INSTALL_PHPDECIMAL=${PHP_FPM_INSTALL_PHPDECIMAL}
          - INSTALL_ZOOKEEPER=${PHP_FPM_INSTALL_ZOOKEEPER}
          - INSTALL_SSDB=${PHP_FPM_INSTALL_SSDB}
          - INSTALL_TRADER=${PHP_FPM_INSTALL_TRADER}
          - DOWNGRADE_OPENSSL_TLS_AND_SECLEVEL=${PHP_DOWNGRADE_OPENSSL_TLS_AND_SECLEVEL}
          - PUID=${PHP_FPM_PUID}
          - PGID=${PHP_FPM_PGID}
          - IMAGEMAGICK_VERSION=${PHP_FPM_IMAGEMAGICK_VERSION}
          - LOCALE=${PHP_FPM_DEFAULT_LOCALE}
          - PHP_FPM_NEW_RELIC=${PHP_FPM_NEW_RELIC}
          - PHP_FPM_NEW_RELIC_KEY=${PHP_FPM_NEW_RELIC_KEY}
          - PHP_FPM_NEW_RELIC_APP_NAME=${PHP_FPM_NEW_RELIC_APP_NAME}
          - INSTALL_DOCKER_CLIENT=${PHP_FPM_INSTALL_DOCKER_CLIENT}
          - http_proxy
          - https_proxy
          - no_proxy
      volumes:
        - ./php-fpm/php${PHP_VERSION}.ini:/usr/local/etc/php/php.ini
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      ports:
        - "${PHP_FPM_XDEBUG_PORT}:9003"
      expose:
        - "9000"
      environment:
        - FAKETIME=${PHP_FPM_FAKETIME}
      depends_on:
        - workspace
      networks:
        - backend

### PHP Worker ############################################
    php-worker:
      build:
        context: ./php-worker
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - LARADOCK_PHP_VERSION=${PHP_VERSION}
          - PHALCON_VERSION=${PHALCON_VERSION}
          - INSTALL_BZ2=${PHP_WORKER_INSTALL_BZ2}
          - INSTALL_GD=${PHP_WORKER_INSTALL_GD}
          - INSTALL_IMAGEMAGICK=${PHP_WORKER_INSTALL_IMAGEMAGICK}
          - INSTALL_GMP=${PHP_WORKER_INSTALL_GMP}
          - INSTALL_GNUPG=${PHP_WORKER_INSTALL_GNUPG}
          - INSTALL_LDAP=${PHP_WORKER_INSTALL_LDAP}
          - INSTALL_PGSQL=${PHP_WORKER_INSTALL_PGSQL}
          - INSTALL_MONGO=${PHP_WORKER_INSTALL_MONGO}
          - INSTALL_BCMATH=${PHP_WORKER_INSTALL_BCMATH}
          - INSTALL_MEMCACHED=${PHP_WORKER_INSTALL_MEMCACHED}
          - INSTALL_OCI8=${PHP_WORKER_INSTALL_OCI8}
          - INSTALL_MSSQL=${PHP_WORKER_INSTALL_MSSQL}
          - INSTALL_PHALCON=${PHP_WORKER_INSTALL_PHALCON}
          - INSTALL_SOAP=${PHP_WORKER_INSTALL_SOAP}
          - INSTALL_ZIP_ARCHIVE=${PHP_WORKER_INSTALL_ZIP_ARCHIVE}
          - INSTALL_MYSQL_CLIENT=${PHP_WORKER_INSTALL_MYSQL_CLIENT}
          - INSTALL_AMQP=${PHP_WORKER_INSTALL_AMQP}
          - INSTALL_CASSANDRA=${PHP_WORKER_INSTALL_CASSANDRA}
          - INSTALL_GEARMAN=${PHP_WORKER_INSTALL_GEARMAN}
          - INSTALL_GHOSTSCRIPT=${PHP_WORKER_INSTALL_GHOSTSCRIPT}
          - INSTALL_SWOOLE=${PHP_WORKER_INSTALL_SWOOLE}
          - INSTALL_TAINT=${PHP_WORKER_INSTALL_TAINT}
          - INSTALL_FFMPEG=${PHP_WORKER_INSTALL_FFMPEG}
          - INSTALL_AUDIOWAVEFORM=${PHP_WORKER_INSTALL_AUDIOWAVEFORM}
          - INSTALL_REDIS=${PHP_WORKER_INSTALL_REDIS}
          - INSTALL_IMAP=${PHP_WORKER_INSTALL_IMAP}
          - INSTALL_XMLRPC=${PHP_WORKER_INSTALL_XMLRPC}
          - INSTALL_SSDB=${PHP_WORKER_INSTALL_SSDB}
          - PUID=${PHP_WORKER_PUID}
          - PGID=${PHP_WORKER_PGID}
          - IMAGEMAGICK_VERSION=${PHP_WORKER_IMAGEMAGICK_VERSION}
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - ./php-worker/supervisord.d:/etc/supervisord.d
      depends_on:
        - workspace
      extra_hosts:
        - "dockerhost:${DOCKER_HOST_IP}"
      networks:
        - backend

### Laravel Horizon ############################################
    laravel-horizon:
      build:
        context: ./laravel-horizon
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - LARADOCK_PHP_VERSION=${PHP_VERSION}
          - INSTALL_BZ2=${LARAVEL_HORIZON_INSTALL_BZ2}
          - INSTALL_GD=${LARAVEL_HORIZON_INSTALL_GD}
          - INSTALL_GMP=${LARAVEL_HORIZON_INSTALL_GMP}
          - INSTALL_GNUPG=${LARAVEL_HORIZON_INSTALL_GNUPG}
          - INSTALL_LDAP=${LARAVEL_HORIZON_INSTALL_LDAP}
          - INSTALL_IMAGEMAGICK=${LARAVEL_HORIZON_INSTALL_IMAGEMAGICK}
          - INSTALL_PGSQL=${PHP_FPM_INSTALL_PGSQL}
          - INSTALL_ZIP_ARCHIVE=${LARAVEL_HORIZON_INSTALL_ZIP_ARCHIVE}
          - INSTALL_BCMATH=${PHP_FPM_INSTALL_BCMATH}
          - INSTALL_MEMCACHED=${PHP_FPM_INSTALL_MEMCACHED}
          - INSTALL_SOCKETS=${LARAVEL_HORIZON_INSTALL_SOCKETS}
          - INSTALL_YAML=${LARAVEL_HORIZON_INSTALL_YAML}
          - INSTALL_CASSANDRA=${LARAVEL_HORIZON_INSTALL_CASSANDRA}
          - INSTALL_PHPREDIS=${LARAVEL_HORIZON_INSTALL_PHPREDIS}
          - INSTALL_MONGO=${LARAVEL_HORIZON_INSTALL_MONGO}
          - INSTALL_FFMPEG=${LARAVEL_HORIZON_INSTALL_FFMPEG}
          - INSTALL_AUDIOWAVEFORM=${LARAVEL_HORIZON_INSTALL_AUDIOWAVEFORM}
          - PUID=${LARAVEL_HORIZON_PUID}
          - PGID=${LARAVEL_HORIZON_PGID}
          - IMAGEMAGICK_VERSION=${LARAVEL_HORIZON_IMAGEMAGICK_VERSION}
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}
        - ./laravel-horizon/supervisord.d:/etc/supervisord.d
      depends_on:
        - workspace
      extra_hosts:
        - "dockerhost:${DOCKER_HOST_IP}"
      networks:
        - backend

  ### Redis ################################################
    redis:
      build: ./redis
      volumes:
        - ${DATA_PATH_HOST}/redis:/data
      ports:
        - "${REDIS_PORT}:6379"
      networks:
        - backend

### Weaver (Athena PDF) #################################################
    weaver:
      image: arachnysdocker/athenapdf-service
      ports:
        - "9521:8080"
      env_file:
        - ./weaver/conf/sample.env
      networks:
        - backend
      depends_on:
        - workspace

### Laravel Echo Server #######################################
    laravel-echo-server:
      build:
        context: ./laravel-echo-server
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
      volumes:
        - ./laravel-echo-server/laravel-echo-server.json:/app/laravel-echo-server.json:ro
      ports:
        - "${LARAVEL_ECHO_SERVER_PORT}:6001"
      links:
        - redis
      networks:
        - frontend
        - backend
        
### Caddy Server #########################################
    caddy:
      build: ./caddy
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - ${CADDY_CONFIG_PATH}:/etc/caddy
        - ${CADDY_HOST_LOG_PATH}:/var/log/caddy
        - ${DATA_PATH_HOST}:/root/.caddy
      ports:
        - "${CADDY_HOST_HTTP_PORT}:80"
        - "${CADDY_HOST_HTTPS_PORT}:443"
      depends_on:
        - php-fpm
      networks:
        - frontend
        - backend



### NGINX Server #########################################
#    nginx:
#      build:
#        context: ./nginx
#        args:
#          - CHANGE_SOURCE=${CHANGE_SOURCE}
#          - PHP_UPSTREAM_CONTAINER=${NGINX_PHP_UPSTREAM_CONTAINER}
#          - PHP_UPSTREAM_PORT=${NGINX_PHP_UPSTREAM_PORT}
#          - http_proxy
#          - https_proxy
#          - no_proxy
#      volumes:
#        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
#        - ${NGINX_HOST_LOG_PATH}:/var/log/nginx
#        - ${NGINX_SITES_PATH}:/etc/nginx/sites-available
#        - ${NGINX_SSL_PATH}:/etc/nginx/ssl
 #     ports:
 #       - "${NGINX_HOST_HTTP_PORT}:80"
 #       - "${NGINX_HOST_HTTPS_PORT}:443"
 #     depends_on:
 #       - php-fpm
 #     networks:
 #       - frontend
 #       - backend
