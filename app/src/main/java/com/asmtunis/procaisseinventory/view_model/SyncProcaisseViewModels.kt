package com.asmtunis.procaisseinventory.view_model

import androidx.compose.runtime.Stable
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model.SyncBonCommandeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model.SyncBonRetourViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.view_model.SyncClientViewModel
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model.SyncDistributionNumViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.SyncInvBatimentViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.SyncInvPatrimoineViewModel
import com.asmtunis.procaisseinventory.pro_caisse.reglement.view_model.SyncReglementViewModel
import com.asmtunis.procaisseinventory.pro_caisse.tournee.view_model.SyncTourneeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.SyncVcViewModel
import kotlinx.coroutines.FlowPreview

@OptIn(FlowPreview::class)
data class SyncProcaisseViewModels(
    val syncVcViewModel: SyncVcViewModel,
    val syncDistNumViewModel: SyncDistributionNumViewModel,
    val syncReglementViewModel: SyncReglementViewModel,
    val syncBonRetourViewModel: SyncBonRetourViewModel,
    val syncInvPatrimoineViewModel: SyncInvPatrimoineViewModel,
    val syncBonCommandeViewModel: SyncBonCommandeViewModel,
    val syncBonLivraisonVM: SyncBonLivraisonViewModel,
    val syncClientViewModel: SyncClientViewModel,
    val syncTourneeViewModel: SyncTourneeViewModel,
    val syncInvBatimentViewModel: SyncInvBatimentViewModel,
) {
    val isLoading = syncClientViewModel.clientsState.loading ||
            syncDistNumViewModel.responseDeleteBatchVisiteDnState.loading ||
            syncDistNumViewModel.responseAddBatchVisiteDnState.loading ||
            syncVcViewModel.imageState.loading ||
            syncVcViewModel.promoState.loading ||
            syncVcViewModel.prixState.loading ||
            syncVcViewModel.autreState.loading ||
            syncVcViewModel.newProductState.loading ||
            syncVcViewModel.newProductDeletedState.loading ||
            syncVcViewModel.autreDeletedState.loading ||
            syncVcViewModel.prixDeletedState.loading ||
            syncVcViewModel.promoDeletedState.loading ||
            syncReglementViewModel.reglementCaisseUpdateState.loading ||
            syncInvPatrimoineViewModel.responseAddAffectationState.loading ||
            syncInvPatrimoineViewModel.responseAddDepOutState.loading ||
            syncInvPatrimoineViewModel.responseAddDepInState.loading ||
            syncInvPatrimoineViewModel.responseAddInventaireState.loading ||

            syncInvPatrimoineViewModel.responseAddAffectationBatimentState.loading ||
            syncInvPatrimoineViewModel.responseAddDepOutBatimentState.loading ||
            syncInvPatrimoineViewModel.responseAddDepInBatimentState.loading ||
            syncInvPatrimoineViewModel.responseAddInventaireBatimentState.loading ||

            syncInvPatrimoineViewModel.responseImageAddAffectationBatimentState.loading ||
            syncInvPatrimoineViewModel.responseImageDepOutBatimentState.loading ||
            syncInvPatrimoineViewModel.responseImageDepInBatimentState.loading ||
            syncInvPatrimoineViewModel.responseImageInventaireBatimentState.loading ||


            syncInvBatimentViewModel.responseAffectCodeBareBatimentState.loading ||

            syncBonCommandeViewModel.responseAddBonCommandeState.loading ||
            syncBonRetourViewModel.responseAddBonRetourState.loading ||
            syncBonLivraisonVM.responseAddBatchTicketWithLignesState.loading ||
            syncTourneeViewModel.batchUpdateLigneOrdreMissionState.loading
}