package com.asmtunis.procaisseinventory.view_model

import android.annotation.SuppressLint
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ClientArticlePrix
import com.asmtunis.procaisseinventory.articles.data.priceperstation.remote.state.PricePerStationState
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE_AUTHORIZATION_TYPE_MENU
import com.asmtunis.procaisseinventory.core.authorizations.AuthorizationFunction.addToAuthList
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.BC
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.BL
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.VEILLE_CONCURENTIELLE
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.ktor.insertNetworkError
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.AUTO_FACTURE_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.MAX_NUM_TICKET
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.serialization.json.JsonElement
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import com.asmtunis.procaisseinventory.data.etablisement.domaine.Etablisement
import com.asmtunis.procaisseinventory.data.facture.domaine.Facture
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.statistiques.domaine.Statistics
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.asmtunis.procaisseinventory.data.ville.domaine.Ville
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.dto.BonCommandeWithLignes
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.dto.PaginationResponseBonCommandeWithLignes
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.dto.toBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.dto.extractLignesFromBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMissionWithLines
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.Vendeur
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.simapps.ui_kit.utils.getCurrentDate
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.cancel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.cancellable
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonIgnoreUnknownKeys
import java.io.IOException
import javax.inject.Inject
import kotlin.math.ceil



@HiltViewModel
class GetProCaisseDataViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {




    var authorizationList = ArrayList<Authorization>()

    var grantedAuthorizationList = ArrayList<String>()
        private set


    var clientsState: RemoteResponseState<List<Client>> by mutableStateOf(RemoteResponseState())
        private set

    var immobilisationState: RemoteResponseState<List<Immobilisation>> by mutableStateOf(RemoteResponseState())
        private set


    var typeMouvementState: RemoteResponseState<List<TypeMouvement>> by mutableStateOf(RemoteResponseState())
        private set

    var batimentByUserState: RemoteResponseState<List<BatimentByUser>> by mutableStateOf(RemoteResponseState())
        private set


    var deplacementOutByUserResponseState: RemoteResponseState<List<DeplacementOutByUser>> by mutableStateOf(RemoteResponseState())
        private set
    var inventairePieceJointState: RemoteResponseState<List<ImagePieceJoint>> by mutableStateOf(RemoteResponseState())
        private set

    var etablissementState: RemoteResponseState<Etablisement> by mutableStateOf(RemoteResponseState())
        private set

    var villeState: RemoteResponseState<List<Ville>> by mutableStateOf(RemoteResponseState())
        private set

    var banqueState: RemoteResponseState<List<Banque>> by mutableStateOf(RemoteResponseState())
        private set

    var carteRestoState: RemoteResponseState<List<CarteResto>> by mutableStateOf(RemoteResponseState())
        private set

    var ticketRestoState: RemoteResponseState<List<List<TraiteCaisse>>> by mutableStateOf(RemoteResponseState())
        private set



    var chequeCaisseState: RemoteResponseState<List<List<ChequeCaisse>>> by mutableStateOf(RemoteResponseState())
        private set




    var reglementCaisseState: RemoteResponseState<List<ReglementCaisse>> by mutableStateOf(RemoteResponseState())
        private set

    var bonLivraisonState: RemoteResponseState<List<Ticket>> by mutableStateOf(RemoteResponseState())
        private set
    var ligneTicketCaisseState: RemoteResponseState<List<List<LigneTicket>>> by mutableStateOf(RemoteResponseState())
        private set

    var factureState: RemoteResponseState<List<Facture>> by mutableStateOf(RemoteResponseState())
        private set

    var bonCommandeState: RemoteResponseState<List<BonCommande>> by mutableStateOf(RemoteResponseState())
        private set
    var ligneBonCommandeState: RemoteResponseState<List<LigneBonCommande>> by mutableStateOf(RemoteResponseState())
        private set


    var bonRetourState: RemoteResponseState<List<BonRetour>> by mutableStateOf(RemoteResponseState())
        private set
    var ligneBonRetourState: RemoteResponseState<List<LigneBonRetour>> by mutableStateOf(RemoteResponseState())
        private set


    var maxNumTicketState: RemoteResponseState<Int> by mutableStateOf(RemoteResponseState())
        private set

    var pricePerStationState by mutableStateOf(PricePerStationState())
        private set

    var sessionCaisseState: RemoteResponseState<List<SessionCaisse>> by mutableStateOf(RemoteResponseState())
        private set

    var sessionCaisseByUserState: RemoteResponseState<SessionCaisse> by mutableStateOf(RemoteResponseState())
        private set




    var superficieState: RemoteResponseState<List<SuperficieDn>> by mutableStateOf(RemoteResponseState())
        private set

    var typePointVenteState: RemoteResponseState<List<TypePointVenteDn>> by mutableStateOf(RemoteResponseState())
        private set

    var typeServiceState: RemoteResponseState<List<TypeServicesDn>> by mutableStateOf(RemoteResponseState())
        private set

    var familleDnState: RemoteResponseState<List<FamilleDn>> by mutableStateOf(RemoteResponseState())
        private set

    var visitesState: RemoteResponseState<List<VisitesDn>> by mutableStateOf(RemoteResponseState())
        private set

    var lignesVisitesState: RemoteResponseState<List<LigneVisitesDn>> by mutableStateOf(RemoteResponseState())
        private set

    var listConcurrentState: RemoteResponseState<List<ConcurrentVC>> by mutableStateOf(RemoteResponseState())
        private set

    var statisticsState: RemoteResponseState<Statistics> by mutableStateOf(RemoteResponseState())
        private set



    var clientArticlePrixState: RemoteResponseState<List<ClientArticlePrix>> by mutableStateOf(RemoteResponseState())
        private set

    /**
     * vc begin
     */
    var newProductState: RemoteResponseState<List<NewProductVC>> by mutableStateOf(RemoteResponseState())
        private set

    var promoState: RemoteResponseState<List<PromoVC>> by mutableStateOf(RemoteResponseState())
        private set


    var prixState: RemoteResponseState<List<PrixVC>> by mutableStateOf(RemoteResponseState())
        private set


    var autreState: RemoteResponseState<List<AutreVC>> by mutableStateOf(RemoteResponseState())
        private set


    var typeCommunicationState: RemoteResponseState<List<TypeCommunicationVC>> by mutableStateOf(RemoteResponseState())
        private set

    var imageState: RemoteResponseState<List<ImagePieceJoint>> by mutableStateOf(RemoteResponseState())
        private set

    /**
     * vc end
     */


    var etatOrdreMissionState: RemoteResponseState<List<EtatOrdreMission>> by mutableStateOf(RemoteResponseState())
        private set

    var ordreMissionWithLinesState: RemoteResponseState<List<OrdreMissionWithLines>> by mutableStateOf(RemoteResponseState())
        private set

    private val _baseConfig = mutableStateOf(BaseConfig())
    val baseConfig: State<BaseConfig> = _baseConfig



    init {
        viewModelScope.launch {
            proCaisseLocalDb.authorization.getAll().collect {
                if(it==null) return@collect
                setProcaisseAuthList(autorisationUser = it)
            }
        }

        // Ensure user ID is properly synchronized
        syncUserIdFromDatabase()
    }

    /**
     * Synchronize user ID from local database to ensure consistency
     */
    private fun syncUserIdFromDatabase() {
        viewModelScope.launch {
            proCaisseLocalDb.utilisateur.getUser().collect { utilisateur ->
                if (utilisateur != null && utilisateur.codeUt.isNotEmpty()) {
                    com.asmtunis.procaisseinventory.core.Globals.USER_ID = utilisateur.codeUt
                }
            }
        }
    }
    fun getBanques(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getAsyncBanque = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )
                    proCaisseRemote.banque.getBanques(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncBanque.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.banque.deleteAll()

                            proCaisseLocalDb.banque.upsertAll(result.data!!)

                            banqueState = RemoteResponseState(data = result.data, loading = false, error = null)
                            
                        }

                        is DataResult.Loading -> {
                            
                            banqueState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_BANQUES,
                                errorMessage = result.message
                            )
                            
                            banqueState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }


    fun getVilles(
        baseConfig: BaseConfig,
        sCIdSCaisse: String
    ) {
        try {
            viewModelScope.launch {
                val getAsyncVille = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sCIdSCaisse)
                    )
                    proCaisseRemote.ville.getVilles(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncVille.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.ville.deleteAll()
                             if(result.data!=null)
                            proCaisseLocalDb.ville.upsertAll(result.data)

                            villeState = RemoteResponseState(data = result.data, loading = false, error = null)
                            
                        }

                        is DataResult.Loading -> {
                            
                            villeState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_VILLE,
                                errorMessage = result.message
                            )
                            
                            villeState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getStatistics(baseConfig: BaseConfig, sCIdSCaisse: String) {
        try {
            viewModelScope.launch {
                val getAsyncStatistics = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sCIdSCaisse)
                    )
                    proCaisseRemote.statistics.getStatistics(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncStatistics.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.statistiques.deleteAll()

                            proCaisseLocalDb.statistiques.upsertAll(result.data!!)


                            statisticsState = RemoteResponseState(data = result.data, loading = false, error = null)
                            
                        }

                        is DataResult.Loading -> {
                            statisticsState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_STATISTICS,
                                errorMessage = result.message
                            )
                            
                            statisticsState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getCarteResto(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getAsyncCarteResto = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )
                    proCaisseRemote.carteResto.getCartesResto(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncCarteResto.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.carteResto.deleteAll()

                            proCaisseLocalDb.carteResto.upsertAll(result.data!!)

                            carteRestoState =
                                RemoteResponseState(data = result.data, loading = false, error = null)
                            
                        }

                        is DataResult.Loading -> {
                            
                            carteRestoState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_CARTES_RESTO,
                                errorMessage = result.message
                            )

                            
                            carteRestoState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }



    fun getChequeCaisse(
        baseConfig: BaseConfig,
        reglementsCaisse: List<ReglementCaisse>
    ) {
        try {
            viewModelScope.launch {
                val getAsyncChequeCaisse = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(reglementsCaisse)
                    )
                    proCaisseRemote.chequeCaisse.getChequeCaisseByReglements(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncChequeCaisse.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.chequeCaisse.deleteAll()

                            proCaisseLocalDb.chequeCaisse.upsertAll(result.data!!.first())

                            chequeCaisseState =
                                RemoteResponseState(data = result.data, loading = false, error = null)
                            
                        }

                        is DataResult.Loading -> {
                            
                            chequeCaisseState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_CHEQUE_BY_REGLEMENTS,
                                errorMessage = result.message
                            )
                            chequeCaisseState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getTraiteCaisse(
        baseConfig: BaseConfig,
        reglementsCaisse: List<ReglementCaisse>
    ) {
        try {
            viewModelScope.launch {
                val getAsyncTraiteCaisse = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(reglementsCaisse)
                    )
                    proCaisseRemote.ticketResto.getTraiteCaisseByReglements(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncTraiteCaisse.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.ticketResto.deleteAll()

                            proCaisseLocalDb.ticketResto.upsertAll(result.data!!.first())

                            ticketRestoState =
                                RemoteResponseState(data = result.data, loading = false, error = null)
                            
                        }

                        is DataResult.Loading -> {
                            
                            ticketRestoState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_TRAITE_CAISSE_BY_REGLEMENTS,
                                errorMessage = result.message
                            )

                            ticketRestoState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getReglementCaiss(
        baseConfig: BaseConfig,
        sCIdSCaisse: String,
        exercice: String
    ) {

        try {
            viewModelScope.launch {
                val getReglementCaiss = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sCIdSCaisse)
                    )
                    proCaisseRemote.reglementCaisse.getReglementCaisseBySession(
                        baseConfig = Json.encodeToString(baseConfigObj),
                        exercice = exercice,
                        archive = false
                    )
                    /*  getReglementCaisse(baseConfig = baseConfig,
                          utilisateur = utilisateur,
                          exercice = exercice)*/
                }
                getReglementCaiss.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.reglementCaisse.deleteAll()
                            proCaisseLocalDb.reglementCaisse.upsertAll(result.data!!)

                            if(result.data.isNotEmpty()){
                                getChequeCaisse(
                                    baseConfig = baseConfig,
                                    reglementsCaisse = result.data
                                )
                                getTraiteCaisse(
                                    baseConfig = baseConfig,
                                    reglementsCaisse = result.data
                                )
                            }


                            //  }
                            reglementCaisseState =
                                RemoteResponseState(
                                    data = result.data,
                                    loading = false,
                                    error = null
                                )
                            
                        }

                        is DataResult.Loading -> {
                            
                            reglementCaisseState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_REGLEMENT_CAISSE_BY_SESSION,
                                errorMessage = result.message
                            )
                            reglementCaisseState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    /**
     * BON LIVRAISON - Load all tickets for station from all sessions
     */
    fun getAllTicketsForStation(
        baseConfig: BaseConfig,
        utilisateur: Utilisateur,
        exerciceCode: String
    ) {
        try {
            viewModelScope.launch {
                // First get all sessions (not just user's sessions)
                val getSessionCaisses = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )
                    proCaisseRemote.sessionCaisse.getSessionCaisses(
                        Json.encodeToString(baseConfigObj)
                    )
                }

                getSessionCaisses.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            // Filter sessions for current station
                            val stationSessions = result.data!!.filter { session ->
                                session.sCStation == utilisateur.Station
                            }


                            if (stationSessions.isNotEmpty()) {
                                // Clear existing tickets to avoid duplicates
                                // proCaisseLocalDb.bonLivraison.deleteAll()

                                // Load tickets from each session
                                stationSessions.forEach { session ->
                                    getTickets(
                                        baseConfig = baseConfig,
                                        sCIdSCaisse = session.sCIdSCaisse,
                                        ddm = getCurrentDate(),
                                        exercice = exerciceCode,
                                        archive = false,
                                        zone = true
                                    )
                                }
                            } else {
                            }
                        }
                        is DataResult.Loading -> {
                            bonLivraisonState = RemoteResponseState(data = null, loading = true, error = null)
                        }
                        is DataResult.Error -> {
                            bonLivraisonState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        } catch (e: IOException) {
        }
    }

    /**
     * REGLEMENT - Load all reglement for station from all sessions
     */
    fun getAllReglementForStation(
        baseConfig: BaseConfig,
        utilisateur: Utilisateur,
        exerciceCode: String
    ) {
        try {
            viewModelScope.launch {
                // First get all sessions (not just user's sessions)
                val getSessionCaisses = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )
                    proCaisseRemote.sessionCaisse.getSessionCaisses(
                        Json.encodeToString(baseConfigObj)
                    )
                }

                getSessionCaisses.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            // Filter sessions for current station
                            val stationSessions = result.data!!.filter { session ->
                                session.sCStation == utilisateur.Station
                            }


                            if (stationSessions.isNotEmpty()) {
                                // Load reglement from each session
                                stationSessions.forEach { session ->
                                    getReglementCaiss(
                                        baseConfig = baseConfig,
                                        sCIdSCaisse = session.sCIdSCaisse,
                                        exercice = exerciceCode
                                    )
                                }
                            } else {
                            }
                        }
                        is DataResult.Loading -> {
                            reglementCaisseState = RemoteResponseState(data = null, loading = true, error = null)
                        }
                        is DataResult.Error -> {
                            reglementCaisseState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        } catch (e: IOException) {
        }
    }

    /**
     * BON LIVRAISON
     */
    fun getTickets(
        baseConfig: BaseConfig,
        sCIdSCaisse: String,
        ddm: String,
        exercice: String,
        archive: Boolean,
        zone: Boolean
    ) {


        try {
            viewModelScope.launch {
                val getAsyncTicket = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sCIdSCaisse)
                    )
                    proCaisseRemote.ticket.getTicketsByCaisseId(
                        baseConfig = Json.encodeToString(baseConfigObj),
                        ddm = ddm,
                        exercice = exercice,
                        archive = archive,
                        zone = zone
                    )

                }
                getAsyncTicket.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                           // proCaisseLocalDb.bonLivraison.deleteAll()

                            val finalList = ArrayList<Ticket>()
                            for (i in result.data!!.indices) {
                                if (stringToDouble(result.data[i].tIKMtTTC) > 0) {
                                    var ticket = result.data[i]
                                    if (result.data[i].tIKNumTicketM == "") {
                                        ticket = result.data[i].copy(tIKNumTicketM = result.data[i].tIKNumTicket)
                                    }
                                    finalList.add(ticket)
                                }
                            }


                            if (finalList.isNotEmpty()) {
                                proCaisseLocalDb.bonLivraison.upsertAll(finalList)
                                getLigneTickets(baseConfig = baseConfig, tickets = result.data)
                            }

                            bonLivraisonState = RemoteResponseState(data = result.data, loading = false, error = null)
                            
                        }

                        is DataResult.Loading -> {
                            
                            bonLivraisonState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_TICKETS_BY_CAISSE_ID,
                                errorMessage = result.message
                            )
                            bonLivraisonState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }



    /**
     * FACTURE
     */
    fun getFatures(
        baseConfig: BaseConfig
    ) {


        try {
            viewModelScope.launch {
                val getAsyncFacture = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )
                    proCaisseRemote.facture.getFacture(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )
                    /*  getReglementCaisse(baseConfig = baseConfig,
                          utilisateur = utilisateur,
                          exercice = exercice)*/
                }
                getAsyncFacture.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            proCaisseLocalDb.facture.deleteAll()

                            if(!result.data.isNullOrEmpty())
                            proCaisseLocalDb.facture.upsertAll(result.data)

                            factureState = RemoteResponseState(data = result.data, loading = false, error = null)
                            
                        }

                        is DataResult.Loading -> {
                            
                            factureState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_FACTURE,
                                errorMessage = result.message
                            )
                            
                            factureState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getLigneTickets(
        baseConfig: BaseConfig,
        tickets: List<Ticket>
    ) {


        try {
            viewModelScope.launch {
                val getAsyncLigneTicket = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(tickets)
                    )
                    proCaisseRemote.ligneTicket.getLigneTicketByTickets(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncLigneTicket.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.ligneBonLivraison.deleteAll()

                            val finalList = ArrayList<LigneTicket>()
                            if (result.data!![0].isNotEmpty()) {
                                for (list in result.data) {
                                    finalList.addAll(list)
                                }
                            }
                            proCaisseLocalDb.ligneBonLivraison.upsertAll(finalList)

                            ligneTicketCaisseState =
                                RemoteResponseState(
                                    data = result.data,
                                    loading = false,
                                    error = null
                                )
                            
                        }

                        is DataResult.Loading -> {
                            
                            ligneTicketCaisseState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_LIGNE_TICKET_BY_TICKETS,
                                errorMessage = result.message
                            )
                            ligneTicketCaisseState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getMaxNumTicket(
        baseConfig: BaseConfig,
        sCIdSCaisse: String,
        exercice: String,
        carnet: String
    ) {


        try {
            viewModelScope.launch {
                val getAsyncNumTicket = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sCIdSCaisse)
                    )
                    proCaisseRemote.ticket.getMaxNumTicket(
                        baseConfig = Json.encodeToString(baseConfigObj),

                        idExerc = exercice,
                        idCarnet = carnet
                    )

                }
                getAsyncNumTicket.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            viewModelScope.launch {
                                proCaisseLocalDb.dataStore.putInt(MAX_NUM_TICKET, result.data!!)
                            }


                            maxNumTicketState =
                                RemoteResponseState(data = result.data, loading = false, error = null)
                            
                        }

                        is DataResult.Loading -> {
                            
                            maxNumTicketState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_MAX_NUM_TICKET,
                                errorMessage = result.message
                            )
                            maxNumTicketState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }



    /**
     * Check available memory and return true if memory is low
     */
    private fun isMemoryLow(): Boolean {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val availableMemory = maxMemory - usedMemory

        // Consider memory low if less than 50MB available
        return availableMemory < 50 * 1024 * 1024
    }

    /**
     * Helper function to fetch a page with retry mechanism and memory management
     */
    private suspend fun fetchPageWithRetry(
        page: Int,
        baseConfig: BaseConfig,
        proCaisseRemote: ProCaisseRemote,
        pageSize: Int = 50, // Reduced default page size for memory efficiency
        maxRetries: Int = 3
    ): Pair<Int, Flow<DataResult<JsonElement?>>?> {
        val retryDelays = listOf(1000L, 2000L, 4000L) 

        repeat(maxRetries) { attempt ->
            try {
                // Force garbage collection before each attempt
                if (attempt > 0) {
                    System.gc()
                    kotlinx.coroutines.delay(500) // Longer delay for memory recovery
                }

                val pageObj = mapOf(
                    "page" to page,
                    "limit" to pageSize
                )
                val pageConfigObj = GenericObject(
                    baseConfig,
                    Json.encodeToJsonElement(pageObj)
                )

                val result = proCaisseRemote.bonCommande.getBonCommandesPaginationWithLignes(
                    baseConfig = Json.encodeToString(pageConfigObj),
                    page = page.toString(),
                    limit = pageSize.toString()
                )

                return Pair(page, result)

            } catch (e: OutOfMemoryError) {
                // Handle OutOfMemoryError specifically
                System.gc() // Force garbage collection
                kotlinx.coroutines.delay(1000) // Wait for memory recovery

                if (attempt < maxRetries - 1) {
                    // Continue to next iteration of repeat loop
                } else {
                    return Pair(page, null) // Give up on this page
                }
            } catch (e: Exception) {
                val errorMessage = e.message ?: "Unknown error"
                val errorType = e::class.simpleName ?: "Exception"


                val isRetryableError = when {
                    errorMessage.contains("authentication", ignoreCase = true) -> false
                    errorMessage.contains("permission", ignoreCase = true) -> false
                    errorMessage.contains("unauthorized", ignoreCase = true) -> false
                    errorMessage.contains("forbidden", ignoreCase = true) -> false
                    errorMessage.contains("401", ignoreCase = true) -> false
                    errorMessage.contains("403", ignoreCase = true) -> false
                    // Add more specific API error patterns
                    errorMessage.contains("invalid page", ignoreCase = true) -> false
                    errorMessage.contains("page not found", ignoreCase = true) -> false
                    else -> true // Retry on network errors, timeouts, and other API errors
                }

                if (!isRetryableError) {
                    return Pair(page, null)
                }

                if (attempt < maxRetries - 1) {
                    val delay = retryDelays[attempt]
                    kotlinx.coroutines.delay(delay)
                } else {
                }
            }
        }

        return Pair(page, null)
    }

    /**
     * BON COMMANDE
     *
     */

    fun getBonCommande(
        baseConfig: BaseConfig,
        sCIdSCaisse: String,
        clientList: List<Client>
    ) {
        try {
            viewModelScope.launch {
                bonCommandeState = RemoteResponseState(data = null, loading = true, error = null)

                // Reduced page size to prevent OutOfMemoryError with large nested data
                var pageSize = 50 // Start with smaller page size for memory efficiency
                val firstPageAsync = async {
                    val paginationObj = mapOf(
                        "page" to 1,
                        "limit" to pageSize
                    )
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(paginationObj)
                    )
                    proCaisseRemote.bonCommande.getBonCommandesPaginationWithLignes(
                        baseConfig = Json.encodeToString(baseConfigObj),
                        page = "1",
                        limit = pageSize.toString()
                    )
                }

                firstPageAsync.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            val jsonResponse = result.data
                            if (jsonResponse != null) {
                                // Configure Json with ignoreUnknownKeys to handle API response variations
                                val jsonConfig = Json {
                                    ignoreUnknownKeys = true
                                    explicitNulls = false
                                    isLenient = true
                                    coerceInputValues = true
                                    // Memory optimization settings
                                    allowStructuredMapKeys = false
                                    useArrayPolymorphism = false
                                }

                                // Parse the JSON response to extract pagination info and data with lignes
                                // Use try-catch to handle potential OutOfMemoryError during deserialization
                                val paginationResponseWithLignes = try {
                                    jsonConfig.decodeFromJsonElement<PaginationResponseBonCommandeWithLignes>(jsonResponse)
                                } catch (e: OutOfMemoryError) {
                                    // If OOM occurs, reduce page size and retry
                                    pageSize = minOf(pageSize / 2, 25)
                                    bonCommandeState = RemoteResponseState(
                                        data = null,
                                        loading = false,
                                        error = "Memory error: Reducing page size to $pageSize and retrying. Please try again."
                                    )
                                    return@onEach
                                } catch (e: Exception) {
                                    bonCommandeState = RemoteResponseState(
                                        data = null,
                                        loading = false,
                                        error = "JSON parsing error: ${e.message}"
                                    )
                                    return@onEach
                                }

                                val totalCount = paginationResponseWithLignes.totalAsString.toIntOrNull() ?: 0
                                val limit = pageSize
                                val totalPages = if (totalCount > 0) kotlin.math.ceil(totalCount.toDouble() / limit).toInt() else 0


                                proCaisseLocalDb.bonCommande.deleteAll()
                                proCaisseLocalDb.ligneBonCommande.deleteAll()

                                if (totalCount == 0) {
                                    bonCommandeState = RemoteResponseState(data = emptyList(), loading = false, error = null)
                                    return@onEach
                                }

                                // Collect all data from first page and extract lignes
                                val allBonCommandes = mutableListOf<BonCommande>()
                                val allLignes = mutableListOf<LigneBonCommande>()
                                var successfulPages = 1 // First page is already successful
                                var failedPages = 0
                                var retriedPages = 0
                                val failedPageNumbers = mutableListOf<Int>()

                                paginationResponseWithLignes.data?.let { bonCommandesWithLignes ->
                                    // Process in smaller chunks to reduce memory pressure
                                    bonCommandesWithLignes.chunked(25).forEach { batch ->
                                        batch.forEach { bonCommandeWithLignes ->
                                            // Convert to BonCommande entity
                                            allBonCommandes.add(bonCommandeWithLignes.toBonCommande())
                                            // Extract ligne data
                                            val extractedLignes = bonCommandeWithLignes.extractLignesFromBonCommande()
                                            allLignes.addAll(extractedLignes)
                                        }
                                        // Force garbage collection hint after each batch
                                        System.gc()
                                        kotlinx.coroutines.delay(20) // Allow memory recovery
                                    }
                                }

                                // If there are more pages and we have data, fetch them
                                if (totalPages > 1 && totalCount > limit) {
                                    val pageRange = (2..totalPages).toList()

                                    // Process pages sequentially to avoid memory issues
                                    val pageResults = mutableListOf<Pair<Int, Flow<DataResult<JsonElement?>>?>>()

                                    for (page in pageRange) {
                                        // Check memory before processing each page
                                        if (isMemoryLow()) {
                                            System.gc()
                                            kotlinx.coroutines.delay(1000) // Wait for memory recovery

                                            // If still low memory, stop processing more pages
                                            if (isMemoryLow()) {
                                                break
                                            }
                                        }

                                        // Force garbage collection before processing each page
                                        System.gc()

                                        val pageResult = fetchPageWithRetry(
                                            page = page,
                                            baseConfig = baseConfig,
                                            proCaisseRemote = proCaisseRemote,
                                            pageSize = pageSize
                                        )
                                        pageResults.add(pageResult)

                                        // Small delay between pages to allow memory cleanup
                                        kotlinx.coroutines.delay(200)
                                    }

                                    pageResults.forEach { (pageNumber, pageFlow) ->

                                        // Check available memory before processing each page
                                        val runtime = Runtime.getRuntime()
                                        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
                                        val maxMemory = runtime.maxMemory()
                                        val availableMemory = maxMemory - usedMemory

                                        if (availableMemory < 50 * 1024 * 1024) { // Less than 50MB available
                                            System.gc()
                                            kotlinx.coroutines.delay(100) // Allow GC to complete
                                        }

                                        if (pageFlow != null) {
                                            try {
                                                var pageProcessed = false
                                                var resultCount = 0
                                                pageFlow.collect { pageResult: DataResult<JsonElement?> ->
                                                    resultCount++
                                                    when (pageResult) {
                                                        is DataResult.Success<JsonElement?> -> {
                                                            if (pageResult.data != null) {
                                                                try {
                                                                    // Use same Json config for consistency with memory optimizations
                                                                    val jsonConfig = Json {
                                                                        ignoreUnknownKeys = true
                                                                        explicitNulls = false
                                                                        isLenient = true
                                                                        coerceInputValues = true
                                                                        // Memory optimization settings
                                                                        allowStructuredMapKeys = false
                                                                        useArrayPolymorphism = false
                                                                    }

                                                                    val pageResponseWithLignes = try {
                                                                        jsonConfig.decodeFromJsonElement<PaginationResponseBonCommandeWithLignes>(pageResult.data)
                                                                    } catch (e: OutOfMemoryError) {
                                                                        // Skip this page if OOM occurs
                                                                        failedPages++
                                                                        failedPageNumbers.add(pageNumber)
                                                                        return@collect
                                                                    } catch (e: Exception) {
                                                                        // Skip this page if parsing fails
                                                                        failedPages++
                                                                        failedPageNumbers.add(pageNumber)
                                                                        return@collect
                                                                    }

                                                                    val pageRecordCount = pageResponseWithLignes.data?.size ?: 0

                                                                    // Process records in smaller batches to reduce memory pressure
                                                                    pageResponseWithLignes.data?.chunked(25)?.forEach { batch ->
                                                                        batch.forEach { bonCommandeWithLignes ->
                                                                            // Convert to BonCommande entity
                                                                            allBonCommandes.add(bonCommandeWithLignes.toBonCommande())
                                                                            // Extract ligne data
                                                                            val extractedLignes = bonCommandeWithLignes.extractLignesFromBonCommande()
                                                                            allLignes.addAll(extractedLignes)
                                                                        }
                                                                        // Force garbage collection hint after each batch
                                                                        System.gc()
                                                                        kotlinx.coroutines.delay(20) // Longer pause for memory recovery
                                                                    }

                                                                    if (!pageProcessed) {
                                                                        successfulPages++
                                                                        pageProcessed = true
                                                                    }

                                                                    // Clear the page data reference to help GC
                                                                    pageResult.data?.let {
                                                                        // Data is processed, allow GC to clean up
                                                                        System.gc()
                                                                    }

                                                                } catch (jsonException: Exception) {
                                                                    if (jsonException is OutOfMemoryError) {
                                                                        System.gc() // Force cleanup
                                                                    }
                                                                    if (!pageProcessed) {
                                                                        failedPages++
                                                                        failedPageNumbers.add(pageNumber)
                                                                        pageProcessed = true
                                                                    }
                                                                }
                                                            } else {
                                                                if (!pageProcessed) {
                                                                    failedPages++
                                                                    failedPageNumbers.add(pageNumber)
                                                                    pageProcessed = true
                                                                }
                                                            }
                                                        }
                                                        is DataResult.Error<JsonElement?> -> {
                                                            val errorMessage = pageResult.message ?: "Unknown error"

                                                            if (errorMessage.contains("OOM") || errorMessage.contains("OutOfMemoryError")) {
                                                                System.gc() // Force cleanup
                                                            }

                                                            if (!pageProcessed) {
                                                                failedPages++
                                                                failedPageNumbers.add(pageNumber)
                                                                pageProcessed = true
                                                            }
                                                        }
                                                        is DataResult.Loading<JsonElement?> -> {
                                                        }
                                                    }
                                                }
                                            } catch (e: Exception) {
                                                if (e is OutOfMemoryError) {
                                                    System.gc()
                                                }
                                                failedPages++
                                                failedPageNumbers.add(pageNumber)
                                            }
                                        } else {
                                            failedPages++
                                            failedPageNumbers.add(pageNumber)
                                            retriedPages++
                                        }

                                        // Force garbage collection after each page to free memory
                                        System.gc()
                                    }

                                    if (failedPageNumbers.isNotEmpty()) {
                                    }
                                } else {
                                }

                                // Validate final count
                                val actualCount = allBonCommandes.size
                                val missingRecords = totalCount - actualCount
                                val expectedRecordsFromFailedPages = failedPages * pageSize // Using actual page size


                                if (missingRecords > 0) {
                                    if (failedPages > 0) {
                                    } else {
                                    }
                                }

                                // Process all collected BonCommande data
                                val finalBonCommandes = allBonCommandes.map {
                                    it.copy(dEVClientName = clientList.firstOrNull { client ->
                                        client.cLICode == it.dEVCodeClient
                                    }?.cLINomPren ?: it.dEVCodeClient)
                                }

                                // Save both BonCommande and LigneBonCommande data
                                try {
                                    proCaisseLocalDb.bonCommande.upsertAll(finalBonCommandes)
                                    if (allLignes.isNotEmpty()) {
                                        proCaisseLocalDb.ligneBonCommande.upsertAll(allLignes)
                                    }

                                    // Final memory cleanup
                                    System.gc()

                                    val successMessage = if (failedPages > 0) {
                                        "Loaded ${finalBonCommandes.size} records. $failedPages pages failed due to memory constraints."
                                    } else null

                                    bonCommandeState = RemoteResponseState(
                                        data = allBonCommandes,
                                        loading = false,
                                        error = successMessage
                                    )
                                } catch (e: OutOfMemoryError) {
                                    System.gc()
                                    bonCommandeState = RemoteResponseState(
                                        data = null,
                                        loading = false,
                                        error = "Memory error during data save. Try reducing data size or restarting the app."
                                    )
                                }
                            }
                        }

                        is DataResult.Loading -> {
                            bonCommandeState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_COMMANDE,
                                errorMessage = result.message
                            )

                            bonCommandeState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        } catch (e: OutOfMemoryError) {
            // Handle OutOfMemoryError at the top level
            System.gc()
            bonCommandeState = RemoteResponseState(
                data = null,
                loading = false,
                error = "Memory error: Not enough memory to load BonCommande data. Try restarting the app or reducing data size."
            )
        } catch (e: IOException) {
            bonCommandeState = RemoteResponseState(
                data = null,
                loading = false,
                error = "Network error: ${e.message}"
            )
        } catch (e: Exception) {
            bonCommandeState = RemoteResponseState(
                data = null,
                loading = false,
                error = "Unexpected error: ${e.message}"
            )
        }
    }

     fun getLigneBonCommande(
         baseConfig: BaseConfig,
         sCIdSCaisse: String
    ) {


        try {
            viewModelScope.launch {
                val getAsyncLigneBonCommande = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sCIdSCaisse)
                    )
                    proCaisseRemote.ligneBonCommande.getLigneBonCommande(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncLigneBonCommande.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.ligneBonCommande.deleteAll()
                            proCaisseLocalDb.ligneBonCommande.upsertAll(result.data!!)


                            ligneBonCommandeState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )

                        }

                        is DataResult.Loading -> {

                            ligneBonCommandeState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {



                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_LIGNE_COMMANDE,
                                errorMessage = result.message
                            )
                            ligneBonCommandeState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    /**
     * BON RETOUR
     *
     */

    fun getBonRetour(
        baseConfig: BaseConfig,
        sCIdSCaisse: String,
        clientList: List<Client>
    ) {
        try {
            viewModelScope.launch {
                val getAsyncBonRetour = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sCIdSCaisse)
                    )
                    proCaisseRemote.bonRetour.getBonRetours(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncBonRetour.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.bonRetour.deleteAll()

                            val finalList = ArrayList<BonRetour>()
                            for (i in result.data!!.indices) {
                                val bonRetour = result.data[i]

                                val clt = clientList.firstOrNull { it.cLICode == result.data[i].bORCodefrs }

                                bonRetour.bORNomfrs = clt?.cLINomPren ?: ""


                                finalList.add(bonRetour)
                            }
                         proCaisseLocalDb.bonRetour.upsertAll(finalList)

                            bonRetourState = RemoteResponseState(data = result.data, loading = false, error = null)

                        }

                        is DataResult.Loading -> {

                            bonRetourState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {


                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_BON_RETOUR,
                                errorMessage = result.message
                            )
                            bonRetourState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }


    fun getLigneBonRetour(
        baseConfig: BaseConfig,
        sCIdSCaisse: String
    ) {
        try {
            viewModelScope.launch {
                val getAsyncLigneBonRetour = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sCIdSCaisse)
                    )
                    proCaisseRemote.ligneBonRetour.getLigneBonRetours(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncLigneBonRetour.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.ligneBonRetour.deleteAll()
                            proCaisseLocalDb.ligneBonRetour.upsertAll(result.data!!)

                            ligneBonRetourState =
                                RemoteResponseState(data = result.data, loading = false, error = null)

                        }

                        is DataResult.Loading -> {

                            ligneBonRetourState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {



                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_LIGNE_BON_RETOUR,
                                errorMessage = result.message
                            )
                            ligneBonRetourState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }


    fun getEtablissement(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {
                val getEtablissement = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.etablissement.getEtablisements(Json.encodeToString(baseConfigObj))
                }

                getEtablissement.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            proCaisseLocalDb.etablisement.deleteAll()
                            proCaisseLocalDb.etablisement.upsertAll(result.data!!)

                            etablissementState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            etablissementState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ETABLISSEMENTS,
                                errorMessage = result.message
                            )
                            etablissementState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)


            }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }


    fun getPricePerStation(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {

                val getPricePerStation = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.pricePerStation.getPricesByStation(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getPricePerStation.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.pricePerStation.deleteAll()
                            proCaisseLocalDb.pricePerStation.upsertAll(result.data!!)

                            pricePerStationState = PricePerStationState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            pricePerStationState =
                                PricePerStationState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {


                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_PRICES_BY_STATION,
                                errorMessage = result.message
                            )
                            pricePerStationState = PricePerStationState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }
        } catch (e: IOException) {

        } /*catch (e: AnotherException) {

    }*/
    }

    fun getSessionCaisses(
        baseConfig: BaseConfig,
        utilisateur: Utilisateur,
        fromUpdate: Boolean = false,
        clientList: List<Client>,
        exerciceCode: String
    ) {
        try {
            viewModelScope.launch {
                val getSessionCaisses = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.sessionCaisse.getSessionCaisses(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getSessionCaisses.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            proCaisseLocalDb.sessionCaisse.upsertAll(result.data!!)

                            sessionCaisseState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )



                            if (fromUpdate) return@onEach

                            val sCIdSCaisse = result.data.firstOrNull{it.sCCodeUtilisateur.toString() == utilisateur.codeUt}?.sCIdSCaisse?:""
                            val sCIdCarnet = result.data.firstOrNull{it.sCCodeUtilisateur.toString() == utilisateur.codeUt}?.sCIdCarnet?:""

                            getVilles(
                                baseConfig = baseConfig,
                                sCIdSCaisse = sCIdSCaisse,
                            )


                            if(grantedAuthorizationList.contains(AuthorizationValuesProCaisse.BR)){
                                getBonRetour(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    clientList = clientList
                                )

                                getLigneBonRetour(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse
                                )
                            }


                            if (grantedAuthorizationList.contains(BC) ||
                                grantedAuthorizationList.contains(AuthorizationValuesProCaisse.PATRIMOINE)
                            ) {


                                getBonCommande(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    clientList = clientList
                                )



                                getLigneBonCommande(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse
                                )
                            }



                            getStatistics(baseConfig = baseConfig, sCIdSCaisse = sCIdSCaisse)


                            if (grantedAuthorizationList.contains(BL) && exerciceCode.isNotEmpty()) {
                                getReglementCaiss(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    exercice = exerciceCode
                                )

                                getTickets(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    ddm = getCurrentDate(),
                                    exercice = exerciceCode,
                                    archive = false,
                                    zone = true
                                )

                                getFatures(baseConfig = baseConfig)

                                getMaxNumTicket(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    exercice = exerciceCode,
                                    carnet = sCIdCarnet
                                )
                            }

                        }

                        is DataResult.Loading -> {
                            sessionCaisseState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_SESSION_CAISSES,
                                errorMessage = result.message
                            )

                            sessionCaisseState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }


        } catch (e: IOException) {

        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/
    }


     fun getSessionCaissesByUser(
         baseConfig: BaseConfig,
         utilisateur: Utilisateur,
         fromUpdate: Boolean = false,
         clientList: List<Client>,
         exerciceCode: String
    ) {
        try {
            viewModelScope.launch {
                val getSessionCaisses = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(utilisateur.codeUt)

                    )

                    proCaisseRemote.sessionCaisse.getSessionCaisseByUser(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getSessionCaisses.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            proCaisseLocalDb.sessionCaisse.upsert(result.data!!)
                            //  }
                            sessionCaisseByUserState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )

                            val sCIdSCaisse = result.data.sCIdSCaisse

                          if (fromUpdate) return@onEach

                            getVilles(
                                baseConfig = baseConfig,
                                sCIdSCaisse = sCIdSCaisse,
                            )


                            if(grantedAuthorizationList.contains(AuthorizationValuesProCaisse.BR)){
                                getBonRetour(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    clientList = clientList
                                )

                                getLigneBonRetour(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse
                                )
                            }


                            if (grantedAuthorizationList.contains(BC) ||
                                grantedAuthorizationList.contains(AuthorizationValuesProCaisse.PATRIMOINE)
                            ) {


                                getBonCommande(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    clientList = clientList
                                )


                                getLigneBonCommande(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse
                                )
                            }



                            getStatistics(baseConfig = baseConfig, sCIdSCaisse = sCIdSCaisse)


                            if (grantedAuthorizationList.contains(BL) && exerciceCode.isNotEmpty()) {
                                getReglementCaiss(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    exercice = exerciceCode
                                )

                                getTickets(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    ddm = getCurrentDate(),
                                    exercice = exerciceCode,
                                    archive = false,
                                    zone = true
                                )

                                getFatures(baseConfig = baseConfig)

                                getMaxNumTicket(
                                    baseConfig = baseConfig,
                                    sCIdSCaisse = sCIdSCaisse,
                                    exercice = exerciceCode,
                                    carnet = result.data.sCIdCarnet!!
                                )
                            }

                        }

                        is DataResult.Loading -> {
                            sessionCaisseByUserState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {


                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_SESSION_CAISSE_BY_USER,
                                errorMessage = result.message
                            )

                            sessionCaisseByUserState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/
    }

    fun getFamilleDn(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {
                val getFamilleDn = async {


                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.distributionNumerique.getAllFamille(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }
                getFamilleDn.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.familleDn.deleteAll()
                            proCaisseLocalDb.familleDn.upsertAll(result.data!!)
                            //  }
                            familleDnState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            familleDnState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ALL_FAMILLE,
                                errorMessage = result.message
                            )

                            familleDnState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/
    }


    fun getSuperficieDn(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {
                val getSuperficieDn = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.distributionNumerique.getAllSuperficie(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }
                getSuperficieDn.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.superficieDn.deleteAll()
                            proCaisseLocalDb.superficieDn.upsertAll(result.data!!)
                            //  }
                            superficieState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            superficieState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ALL_SUPERFICIE,
                                errorMessage = result.message
                            )

                            superficieState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/
    }

    fun getTypePointVenteDn(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {
                val getTypePointVenteDn = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.distributionNumerique.getAllTypePVente(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getTypePointVenteDn.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.typePointVenteDn.deleteAll()
                            proCaisseLocalDb.typePointVenteDn.upsertAll(result.data!!)

                            typePointVenteState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            typePointVenteState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ALL_TYPE_P_VENTE,
                                errorMessage = result.message
                            )

                            typePointVenteState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/
    }

    fun getTypeServicesDn(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {
                val getTypeServicesDn = async {


                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.distributionNumerique.getAllTypeService(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getTypeServicesDn.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.typeServicesDn.deleteAll()
                            proCaisseLocalDb.typeServicesDn.upsertAll(result.data!!)

                            typeServiceState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            typeServiceState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ALL_TYPE_SERVICE,
                                errorMessage = result.message
                            )

                            typeServiceState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/
    }

    fun getVisitesDn(
        baseConfig: BaseConfig,
        clientList: List<Client>
    ) {
        try {
            viewModelScope.launch {
                val getVisitesDn = async {


                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.distributionNumerique.getAllVisiteByUser(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getVisitesDn.await().onEach { result ->
                    visitesState = when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.visitesDn.deleteVisiteAll()
                            val finalList = result.data!!.map { it.copy(vIS_NomClient = clientList.firstOrNull { client-> client.cLICode == it.vIS_CodeClient }?.cLINomPren?: it.vIS_CodeClient) }


                            proCaisseLocalDb.visitesDn.upsertVisiteAll(finalList)

                            RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ALL_VISITE,
                                errorMessage = result.message
                            )

                            RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/
    }

    fun getLignesVisitesDn(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getLignesVisitesDn = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.distributionNumerique.getAllLigneVisite(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getLignesVisitesDn.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.visitesDn.deleteLigneVisiteAll()
                            proCaisseLocalDb.visitesDn.upsertLigneVisiteAll(result.data!!)


                            lignesVisitesState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            lignesVisitesState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ALL_LIGNE_VISITE,
                                errorMessage = result.message
                            )

                            lignesVisitesState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getVCImage(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getVCImage = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.veilleConcurentiel.getVCImage(Json.encodeToString(baseConfigObj))
                }

                getVCImage.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.imageVC.deleteAll()
                            proCaisseLocalDb.imageVC.upsertAll(result.data!!)

                            imageState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            imageState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_VC_IMAGE,
                                errorMessage = result.message
                            )
                            imageState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getVCTypeCommunication(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getVCTypeCommunication = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.veilleConcurentiel.getVCTypeCommunication(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getVCTypeCommunication.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.typeCommunicationVC.deleteAll()
                            proCaisseLocalDb.typeCommunicationVC.upsertAll(result.data!!)

                            typeCommunicationState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            typeCommunicationState = RemoteResponseState(
                                data = null,
                                loading = true,
                                error = null
                            )
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_VC_TYPE_COMMUNICATION,
                                errorMessage = result.message
                            )
                            typeCommunicationState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getVCListeConcurrent(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getVCListeConcurrent = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.veilleConcurentiel.getVCListeConcurrent(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getVCListeConcurrent.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.listConcurrentVC.deleteAll()
                            proCaisseLocalDb.listConcurrentVC.upsertAll(result.data!!)

                            listConcurrentState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            listConcurrentState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_VC_LISTE_CONCURRENT,
                                errorMessage = result.message
                            )
                            listConcurrentState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getVCNewProduct(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getVCNewProduct = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.veilleConcurentiel.getVCNewProduct(
                        Json.encodeToString(
                            baseConfigObj
                        )
                    )
                }

                getVCNewProduct.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.newProductVC.deleteAll()
                            proCaisseLocalDb.newProductVC.upsertAll(result.data!!)

                            newProductState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            newProductState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_VC_NEW_PRODUCT,
                                errorMessage = result.message
                            )

                            newProductState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getVCAutre(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getVCAutre = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.veilleConcurentiel.getVCAutre(Json.encodeToString(baseConfigObj))
                }

                getVCAutre.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.autreVC.deleteAll()
                            proCaisseLocalDb.autreVC.upsertAll(result.data!!)

                            autreState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            autreState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {


                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_VC_AUTRE,
                                errorMessage = result.message
                            )

                            autreState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getVCPrix(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getVCPrix = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.veilleConcurentiel.getVCPrix(Json.encodeToString(baseConfigObj))
                }

                getVCPrix.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.prixVC.deleteAll()
                            proCaisseLocalDb.prixVC.upsertAll(result.data!!)

                            prixState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            prixState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_VC_PRIX,
                                errorMessage = result.message
                            )
                            prixState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getVCPromo(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getVCPromo = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.veilleConcurentiel.getVCPromo(Json.encodeToString(baseConfigObj))
                }

                getVCPromo.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.promoVC.deleteAll()
                            proCaisseLocalDb.promoVC.upsertAll(result.data!!)

                            promoState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            promoState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_VC_PROMO,
                                errorMessage = result.message
                            )
                            promoState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getTypeMouvement(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getImmobilisations = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.immobilisation.getAllTypeMouvement(Json.encodeToString(baseConfigObj))
                }
                getImmobilisations.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.typeMouvement.deleteAll()
                            proCaisseLocalDb.typeMouvement.insertAll(result.data!!)

                            typeMouvementState = RemoteResponseState(data = result.data, loading = false, error = null)

                        }

                        is DataResult.Loading -> {
                            typeMouvementState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ALL_TYPE_MOUVEMENT,
                                errorMessage = result.message
                            )
                            typeMouvementState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getImmobilisation(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getImmobilisations = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.immobilisation.getImmobilisation(Json.encodeToString(baseConfigObj))
                }
                getImmobilisations.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.immobilisation.deleteAll()
                            proCaisseLocalDb.immobilisation.insertAll(result.data!!)

                            getDeplacementOutByUser(baseConfig = baseConfig, listImmobilisation = result.data!!)
                            getBatimentsByUser(baseConfig = baseConfig)

                            immobilisationState = RemoteResponseState(data = result.data, loading = false, error = null)



                        }

                        is DataResult.Loading -> {
                            immobilisationState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_IMMOBILISATION,
                                errorMessage = result.message
                            )
                            immobilisationState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }


    fun getBatimentsByUser(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getImmobilisations = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.immobilisation.getBatimentByUser(Json.encodeToString(baseConfigObj))
                }
                getImmobilisations.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                          //  proCaisseLocalDb.batimentByUser.deleteAll()
                          //  proCaisseLocalDb.batimentByUser.insertAll(result.data!!)

                            for(batiment in result.data!!) {
                                proCaisseLocalDb.immobilisation.setIsBatimentUser(batiment.cLICode)
                            }



                            batimentByUserState = RemoteResponseState(data = result.data, loading = false, error = null)

                        }

                        is DataResult.Loading -> {
                            batimentByUserState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_BATIMENT_BY_USER,
                                errorMessage = result.message
                            )
                            batimentByUserState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getDeplacementOutByUser(
        baseConfig: BaseConfig,
       listImmobilisation: List<Immobilisation>
    ) {
        try {
            viewModelScope.launch {
                val getImmobilisations = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.provideDeplacementOutByUserApi.getAllDeplacememntOutByUser(Json.encodeToString(baseConfigObj))
                }
                getImmobilisations.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            // Ne supprimer que les données synchronisées pour préserver les données locales non sync
                            proCaisseLocalDb.deplacementOutByUser.deleteSynced()

                            val finalList = ArrayList<DeplacementOutByUser>()
                            for (i in result.data!!.indices) {

                                val cliNom =   listImmobilisation.firstOrNull { it.cLICode == result.data[i].dEVCodeClient }?.cLINomPren?:""

                                val bonCommande = result.data[i].copy(dEVClient = cliNom)


                                finalList.add(bonCommande)
                            }

                            if (finalList.isNotEmpty()) proCaisseLocalDb.deplacementOutByUser.upsertAll(finalList)

                            val lignes = result.data.flatMap { it.lignesDevis }
                            proCaisseLocalDb.ligneBonCommande.upsertAll(lignes)

                            // Ajouter les BonCommande locaux non synchronisés comme DeplacementOutByUser
                            addLocalBonCommandeAsDeplacementOut(listImmobilisation)



                            deplacementOutByUserResponseState = RemoteResponseState(data = result.data, loading = false, error = null)

                        }

                        is DataResult.Loading -> {
                            deplacementOutByUserResponseState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ALL_DEPLACEMENT_OUT_BY_USER,
                                errorMessage = result.message
                            )

                            deplacementOutByUserResponseState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }


    fun getPiecesJointInventaire(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getPiecesJointsInventaire = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.inventairePatrimoine.getPiecesJointInventaire(Json.encodeToString(baseConfigObj))
                }
                getPiecesJointsInventaire.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.inventairePieceJoint.deleteAll()
                            proCaisseLocalDb.inventairePieceJoint.insertAll(result.data!!)

                            inventairePieceJointState = RemoteResponseState(data = result.data, loading = false, error = null)


                        }

                        is DataResult.Loading -> {
                            inventairePieceJointState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_IMAGES,
                                errorMessage = result.message
                            )
                            inventairePieceJointState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

     fun getClient(
         baseConfig: BaseConfig,
         utilisateur: Utilisateur,
         fromUpdate : Boolean = false,
         exerciceCode: String
    ) {
        try {
            viewModelScope.launch {
                val getClients = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.clients.getClients(
                        Json.encodeToString(baseConfigObj),
                        utilisateur.CltEquivalent
                    )
                }
                getClients.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            //   viewModelScope.launch(dispatcherIO) {

                            val clientList = result.data?: emptyList()
                            proCaisseLocalDb.clients.deleteAll()
                            proCaisseLocalDb.clients.upsertAll(clientList)
                            //  }
                            clientsState = RemoteResponseState(data = clientList, loading = false, error = null)


                            if(fromUpdate) return@onEach

                            //TODO See neccesaire auth to call getReglementCaiss
                            if (grantedAuthorizationList.contains(BL) || grantedAuthorizationList.contains(BC) ||
                                grantedAuthorizationList.contains(AuthorizationValuesProCaisse.PATRIMOINE))

                          /* getSessionCaissesByUser(
                                baseConfig = baseConfig,
                                utilisateur = utilisateur
                            )*/
                            getSessionCaisses(
                                baseConfig = baseConfig,
                               // fromUpdate = fromUpdate,
                                utilisateur = utilisateur,
                            clientList = clientList,
                            exerciceCode = exerciceCode
                            )

                            if (grantedAuthorizationList.contains(VEILLE_CONCURENTIELLE)) {
                                getVisitesDn(
                                    baseConfig = baseConfig,
                                    clientList = clientList
                                )

                                getLignesVisitesDn(baseConfig = baseConfig)
                            }
                        }

                        is DataResult.Loading -> {

                            clientsState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_CLIENTS,
                                errorMessage = result.message
                            )

                            clientsState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    fun getEtatOrdreMission(baseConfig: BaseConfig) {
        try {
            viewModelScope.launch {
                val getEtatOrdMission = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.ordreMission.getEtatOrdreMission(Json.encodeToString(baseConfigObj))
                }

                getEtatOrdMission.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.etatOrdreMission.deleteAll()
                            proCaisseLocalDb.etatOrdreMission.upsertAll(result.data!!)

                            etatOrdreMissionState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            etatOrdreMissionState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ETAT_ORDRE_MISSION,
                                errorMessage = result.message
                            )
                            etatOrdreMissionState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }

    @SuppressLint("SuspiciousIndentation")
    fun getOrdreWithLines(
        baseConfig: BaseConfig,
        utilisateur: Utilisateur,
        date: String? = null
    ) {
        try {
            viewModelScope.launch {


                val vendeur= Vendeur(
                    ORD_vendeur = utilisateur.codeUt,
                    ORD_Date = date?: getCurrentDate(),
                )
                val getOrdMission = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(vendeur)

                    )

                    proCaisseRemote.ordreMission.getOrdreMissionWithLines(Json.encodeToString(baseConfigObj))
                }

                getOrdMission.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.ordreMission.deleteAll()
                            proCaisseLocalDb.ligneOrdreMission.deleteAll()

                            for (ordreMissionWithLine in result.data!!) {
                                proCaisseLocalDb.ordreMission.upsert(ordreMissionWithLine.ordreMission)

                                if(ordreMissionWithLine.ligneOrdreMission!=null)
                                proCaisseLocalDb.ligneOrdreMission.upsertAll(ordreMissionWithLine.ligneOrdreMission!!)
                            }


                            ordreMissionWithLinesState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            ordreMissionWithLinesState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ORDRE_MISSION_WITH_LINES,
                                errorMessage = result.message
                            )
                            ordreMissionWithLinesState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/

    }


    suspend fun getProcaisseData(baseConfig: BaseConfig, utilisateur: Utilisateur, exerciceCode: String) = coroutineScope {
        try {
            // Ensure proCaisseGrantedAuthorizationList is populated
            if (grantedAuthorizationList.isEmpty()) {
                setProcaisseAuthList(autorisationUser = utilisateur.autorisationUser)
            }

            // List of general tasks
            val generalTasks = listOf(
                async { setClientNames() },
                async { getPricePerStation(baseConfig) },
                async { getEtablissement(baseConfig) },
                async { getBanques(baseConfig) },
                async { getCarteResto(baseConfig) }
            )

            // Tasks for VEILLE_CONCURENTIELLE authorization
            val veilleConcurrentielleTasks = if (grantedAuthorizationList.contains(VEILLE_CONCURENTIELLE)) {
                listOf(
                    async { getFamilleDn(baseConfig) },
                    async { getSuperficieDn(baseConfig) },
                    async { getTypePointVenteDn(baseConfig) },
                    async { getTypeServicesDn(baseConfig) },

                    // VC tasks
                    async { getVCImage(baseConfig) },
                    async { getVCTypeCommunication(baseConfig) },
                    async { getVCListeConcurrent(baseConfig) },
                    async { getVCNewProduct(baseConfig) },
                    async { getVCAutre(baseConfig) },
                    async { getVCPrix(baseConfig) },
                    async { getVCPromo(baseConfig) }
                )
            } else emptyList()


            val tourneeTasks = if (grantedAuthorizationList.contains(AuthorizationValuesProCaisse.TOURNE)) {
                listOf(
                    async { getEtatOrdreMission(baseConfig) },
                    async { getOrdreWithLines(baseConfig, utilisateur) }
                )
            } else emptyList()

            // Tasks for PATRIMOINE authorization
            val patrimoineTasks = if (grantedAuthorizationList.contains(AuthorizationValuesProCaisse.PATRIMOINE)) {
                listOf(
                    async { getImmobilisation(baseConfig) },
                    async { getTypeMouvement(baseConfig) },
                    async { getPiecesJointInventaire(baseConfig) }
                )
            } else emptyList()

            // Collect and process exercise data from local DB
            val localDbTask = async {
                getClient (
                baseConfig = baseConfig,
                utilisateur = utilisateur,
                exerciceCode = exerciceCode
            )
            }


            // Wait for all tasks to complete
            (generalTasks + veilleConcurrentielleTasks + patrimoineTasks + localDbTask + tourneeTasks).awaitAll()
        } catch (e: Exception) {
        }
    }


    private suspend fun setProcaisseAuthList(autorisationUser: List<Authorization>) {

        authorizationList.clear()
        grantedAuthorizationList.clear()
        authorizationList.addAll(addToAuthList(result = autorisationUser))
        grantedAuthorizationList.addAll(addToAuthList(result = autorisationUser).map { authorisation -> authorisation.AutoCodeAu })

            proCaisseLocalDb.dataStore.putBoolean(
                AUTO_FACTURE_AUTHORISATION,
                authorizationList.any { it.AutoCodeAu == AuthorizationValuesProCaisse.AUTO_FACTURE}
            )



    }


 //  private var exerciceList: List<Exercice> by mutableStateOf(emptyList())
       // private set


//ToDO IMPLEMENT A BETTER SOLUTION TO PUT CLIENT NAMES (BR, BC, ...)

       var bonCommandeList: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
     private set
    private fun setClientNames() {
        viewModelScope.launch(dispatcherIO) {


            proCaisseLocalDb.bonCommande.getAll().cancellable().collect { listBonCommande ->
                bonCommandeList = emptyMap()
                if (listBonCommande == null) return@collect
                bonCommandeList = listBonCommande
                proCaisseLocalDb.immobilisation.getAll().cancellable().collectLatest { listImmobilisation->
                    if (listImmobilisation == null) return@collectLatest

                    for (immobilisation in listImmobilisation) {
                        proCaisseLocalDb.bonCommande.updateNameClient(
                            codeclient = immobilisation.cLICode,
                            clientName = immobilisation.cLINomPren
                        )
                    }
                  //  cancel()
                }

                proCaisseLocalDb.clients.getAll().cancellable().collectLatest {listClient->
                    if (false) return@collectLatest

                    for (client in listClient) {
                        proCaisseLocalDb.bonCommande.updateNameClient(
                            codeclient = client.cLICode,
                            clientName = client.cLINomPren
                        )
                    }
                   // cancel()
                }





                cancel()
            }



        }
    }

    /**
     * Convertit les BonCommande locaux non synchronisés en DeplacementOutByUser
     * pour qu'ils soient visibles dans les écrans de Deplacement In
     */
    private suspend fun addLocalBonCommandeAsDeplacementOut(listImmobilisation: List<Immobilisation>) {
        try {
            // Récupérer les BonCommande locaux non synchronisés de type Deplacement Out
            val localBonCommandes = proCaisseLocalDb.invePatrimoine.getNotSyncedByType("SORTIE")

            val localDeplacementOutList = ArrayList<DeplacementOutByUser>()

            localBonCommandes.forEach { bonCommande ->
                // Convertir BonCommande en DeplacementOutByUser
                val cliNom = listImmobilisation.firstOrNull { it.cLICode == bonCommande.dEVCodeClient }?.cLINomPren ?: ""

                val deplacementOut = DeplacementOutByUser(
                    bONLIVExerc = bonCommande.bONLIVExerc,
                    bONLIVNum = bonCommande.bONLIVNum,
                    dEVNum = bonCommande.dEVNum,
                    dEVDate = bonCommande.dEVDate ?: "",
                    dEVCodeClient = bonCommande.dEVCodeClient ?: "",
                    dEVClient = cliNom,
                    dEVStation = bonCommande.dEVStation ?: "",
                    dEVStationOrigine = bonCommande.dEVStationOrigine ?: "",
                    dEVEtat = bonCommande.dEVEtat ?: "",
                    dEVEtatBon = bonCommande.dEVEtatBon ?: "",
                    dEVUser = bonCommande.dEVUser ?: "",
                    dEVObservation = bonCommande.dEVObservation,
                    dEVTyMvtCode = bonCommande.dEVTyMvtCode,
                    dEVCodeM = bonCommande.devCodeM,
                    dEVCodeSF = bonCommande.devCodeSF ?: "",
                    dEVCodeSO = bonCommande.devCodeSO ?: "",
                    dEVExerc = bonCommande.dEVExerc ?: ""
                )

                localDeplacementOutList.add(deplacementOut)
            }

            // Insérer les DeplacementOutByUser convertis
            if (localDeplacementOutList.isNotEmpty()) {
                proCaisseLocalDb.deplacementOutByUser.upsertAll(localDeplacementOutList)
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

