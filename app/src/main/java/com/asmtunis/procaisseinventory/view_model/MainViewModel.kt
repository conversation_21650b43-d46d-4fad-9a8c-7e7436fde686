@file:OptIn(SavedStateHandleSaveableApi::class)

package com.asmtunis.procaisseinventory.view_model

import android.graphics.Bitmap
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCodeBar
import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.UniteArticle
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals.devise
import com.asmtunis.procaisseinventory.core.enum_classes.ArticleType
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.insertNetworkError
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.ImageUtils.base64ToBmp
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.core.utils.mobilecode.MobileCodeGeneration
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import com.asmtunis.procaisseinventory.data.devise.domaine.Devise
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.AddSessionCaisseResponse
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionActivation
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.data.ville.domaine.Ville
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.simapps.ui_kit.utils.durationSincePast
import com.simapps.ui_kit.utils.getCurrentDateTime
import com.simapps.ui_kit.utils.getCurrentTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import kotlinx.serialization.decodeFromString
import java.io.IOException
import java.util.Locale
import javax.inject.Inject


@OptIn(SavedStateHandleSaveableApi::class)
@HiltViewModel
class MainViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proCaisseRemote: ProCaisseRemote,
    savedStateHandle: SavedStateHandle
) : ViewModel() {
    init {
        // Load only ESSENTIAL data for app startup (navigation drawer + dashboard)
        loadEssentialData()
    }

    /**
     * Load only essential data needed for navigation drawer and dashboard
     */
    private fun loadEssentialData() {

        // Essential for navigation drawer
        getUtilisateur()
        getParametrage()
        getActiveDevise()


        getTicketList()
        getListReglementCaisse()


        getMarque() // For patrimoine affectation screens
        getArticleList() // For patrimoine article selection
        getFamilleList() // For article filtering
        getClients() // For client selection in patrimoine

    }



    /**
     * Load data needed for client-related screens
     */
    fun loadClientData() {
        if (!isClientDataLoaded) {
            // getClients() // Already loaded in loadEssentialData()
            getVilleList()
            isClientDataLoaded = true
        }
    }

    /**
     * Load data needed for product/article screens
     */
    fun loadProductData() {
        if (!isProductDataLoaded) {
            // getArticleList() // Already loaded in loadEssentialData()
            // getFamilleList() // Already loaded in loadEssentialData()
            // getMarque() // Already loaded in loadEssentialData()
            getTvaList()
            getUniteList()
            getUniteArticleList()
            getArticleCodBarList()
            isProductDataLoaded = true
        }
    }

    /**
     * Load data needed for inventory screens
     */
    fun loadInventoryData() {
        if (!isInventoryDataLoaded) {
            getAllStationStockArticle()
            getStationList()
            getFournisseurList()
            getListLigneInventaire()
            getInventairePrefix()
            isInventoryDataLoaded = true
        }
    }

    /**
     * Load data needed for patrimoine screens
     */
    fun loadPatrimoineData() {
        if (!isPatrimoineDataLoaded) {
            getImmobilisationList()
            getStationList()
            // getArticleList() // Already loaded in loadEssentialData()
            // getUtilisateur() // Already loaded in loadEssentialData()
            getTypeMouvement() // For type de mouvement dropdown
            isPatrimoineDataLoaded = true
        }
    }

    /**
     * Check if essential patrimoine data (marque, famille) is available
     * Returns true if data is available, false if sync is needed
     */
    fun hasEssentialPatrimoineData(): Boolean {
        return marqueList.isNotEmpty() && listFamille.isNotEmpty()
    }

    /**
     * Load data needed for other features
     */
    fun loadOtherData() {
        if (!isOtherDataLoaded) {
            getFamilleDNList()
            getConcurentVCList()
            getTypeMouvement()
            getExercice()
            getListBanque()
            getActifTimbres()
            getTicketRestoListList()
            getImmobilisationList()
            getImageList()
            isOtherDataLoaded = true
        }
    }

    // Flags to track what data has been loaded
    private var isClientDataLoaded = false
    private var isProductDataLoaded = false
    private var isInventoryDataLoaded = false
    private var isPatrimoineDataLoaded = false
    private var isOtherDataLoaded = false


    var imageList: List<ImagePieceJoint> by mutableStateOf(emptyList())
        private set
    private fun getImageList() {
        viewModelScope.launch {
            proCaisseLocalDb.imageVC.getAll().collectLatest {
                if(imageList != it) {
                    imageList = it?: emptyList()
                }
            }
        }
    }

    private fun getActiveDevise() {

        viewModelScope.launch {
            proCaisseLocalDb.devise.activeOne().collect {
                devise = it?: Devise()
            }

        }
    }



    var prefixList: List<Prefixe> by mutableStateOf(emptyList())
        private set
    private fun getInventairePrefix() {

        viewModelScope.launch {
            proCaisseLocalDb.prefix.getAll().collect {
                if(prefixList != it) {
                    prefixList = it
                }
                }
            }
    }

    var inventaireList:List<Inventaire>  by mutableStateOf(emptyList())
        private set
    private fun getListLigneInventaire() {

        viewModelScope.launch {
            proInventoryLocalDb.inventaire.getAll().collect { newList ->
                if (inventaireList != newList) { // Avoid unnecessary updates if the list hasn't changed
                    inventaireList = newList
                }
            }
        }

    }


    var reglementCaisseList:List<ReglementCaisse>  by mutableStateOf(emptyList())
        private set
    private fun getListReglementCaisse() {

        viewModelScope.launch {
            proCaisseLocalDb.reglementCaisse.getAll().collect { it ->
                if(reglementCaisseList != it) {
                    reglementCaisseList = it
                }
            }
        }

    }
    var marqueGeneratedCode: String by mutableStateOf("")
        private set
    fun generteNewMarqueCode() {

        viewModelScope.launch {

       val prefix = prefixList.firstOrNull { it.pREIdTable == "Marque" }

                if(prefix!=null){
                    proInventoryLocalDb.marque.getNewCode(prefix.pREDesignation).collectLatest {
                        marqueGeneratedCode =prefix.pREDesignation + it
                    }
                }



        }

    }

    private fun saveMarque(marque: Marque) {
        viewModelScope.launch(dispatcherIO) {
            proInventoryLocalDb.marque.upsert(marque)
        }
    }

    fun addNewMarque(
        designationMarque: String,
        onSelectedMarqueChange:(Marque) -> Unit = {}
    ) {


        val marque = Marque(
            mARCode = marqueGeneratedCode,
            mARDesignation = designationMarque,
            mARStation = null,
            mARUser = null,
            mARExport = null,
            mARDDm = getCurrentDateTime(),
            photoPathM = null
        )
        marque.isSync = false
        marque.status = ItemStatus.INSERTED.status

        saveMarque(marque)

        onSelectedMarqueChange(marque)
       onAddNewMarqueVisibilityChange(false)

    }

    var addNewMarqueIsVisible: Boolean by mutableStateOf(false)
        private set
    fun onAddNewMarqueVisibilityChange(value: Boolean) {
        addNewMarqueIsVisible = value
    }

    var codeM : String by mutableStateOf("")
        private set

    var imageCodeM: String by mutableStateOf("")
        private set

    fun setCodM(code: String) {
        codeM = code
    }
    fun generateCodeM(
        utilisateur: Utilisateur,
        prefix: String,
        prefixImage: String = ""
    ) {
        val stationUtilisateur = utilisateur.Station
        val userID = utilisateur.codeUt

        val generatedCode = "_" + stationUtilisateur + "_" + userID  + "_" + MobileCodeGeneration.generateCommonCode(
            num =  Math.random().toInt().toString(),
            numLign = Math.random().toInt().toString())
        codeM = prefix+generatedCode


        codeM = codeM.replace("__", "_")

        if (prefixImage.isNotEmpty()) imageCodeM = prefixImage + generatedCode

    }


    var showDatePicker by mutableStateOf(false)
        private set
    fun onShowDatePickerChange(value: Boolean) {
        showDatePicker = value
    }

    var showTimePicker by mutableStateOf(false)
        private set
    fun onShowTimePickerChange(value: Boolean) {
        showTimePicker = value
    }

    var clientByCode: Client by mutableStateOf(Client())
        private set
    fun onSelectedClientChange(value: Client) {
        clientByCode = value
    }



    fun resetClientByCode() {
                clientByCode = Client()
    }




    var listTicketandLignes: Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>> by mutableStateOf(emptyMap())
    var listTicket = mutableStateListOf<Ticket>()
//    private var listLigneTicket = mutableStateListOf<LigneTicket>()
//        private set

    private fun getTicketList() {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getAll().collectLatest { result->

                if(listTicketandLignes == result) return@collectLatest

                listTicketandLignes = result


                    listTicketandLignes.forEach { (key, value) ->
                        run {
                            listTicket.add(key.ticket?: Ticket())
                          //  listLigneTicket.addAll(value.map { it.ligneTicket!! })
                        }
                    }
                }
        }
    }



    var showFondCaisseDialogue by savedStateHandle.saveable { mutableStateOf(false) }
        private set


    fun setFondCaisseDialogueVisibility(value: Boolean) {
        showFondCaisseDialogue = value
    }

    var fondcaisse by savedStateHandle.saveable { mutableStateOf("") }
        private set


    fun setfondcaisse(fondcaiss: String) {
        fondcaisse = fondcaiss
    }


    var openAddNewProductDialogue by savedStateHandle.saveable { mutableStateOf(false) }
        private set


    fun setAddNewProductDialogueVisibility(value: Boolean) {
        openAddNewProductDialogue = value
    }


    var isAutoScanMode by savedStateHandle.saveable { mutableStateOf(false) }
        private set

    fun setAutoAddMode(value: Boolean) {
        isAutoScanMode = value
    }


    var modify by savedStateHandle.saveable { mutableStateOf(false) }
        private set

    fun canModify(value: Boolean) {
        modify = value
    }




    var stationList by  mutableStateOf(emptyList<Station>())
        private set



    private fun getStationList() {
        viewModelScope.launch {
            proInventoryLocalDb.stations.getAll().collect {
                if(stationList == it) return@collect
                stationList = it.ifEmpty { emptyList() }
            }
        }
    }


    var banqueList by  mutableStateOf(emptyList<Banque>())
        private set

    private fun getListBanque() {
        viewModelScope.launch {
            proCaisseLocalDb.banque.getAll().collect {
                if(banqueList == it) return@collect
                banqueList = it.ifEmpty { banqueList }
            }
        }
    }

    var utilisateur: Utilisateur by mutableStateOf(Utilisateur())
        private set
    private fun getUtilisateur() {
        viewModelScope.launch {
            proCaisseLocalDb.utilisateur.getUser().collect { utilisat ->
                utilisateur = utilisat?: Utilisateur()
            }
        }
    }



    var ticketRestoList by  mutableStateOf(emptyList<CarteResto>())
        private set

    private fun getTicketRestoListList() {
        viewModelScope.launch {
            proCaisseLocalDb.carteResto.getAll().collect {
                if(ticketRestoList == it) return@collect
                ticketRestoList = it.ifEmpty { emptyList() }
            }
        }
    }

    var marqueList by  mutableStateOf(emptyList<Marque>())
        private set
    private fun getMarque() {

        viewModelScope.launch {
            proInventoryLocalDb.marque.getAll().collect {
                if(marqueList == it) return@collect
                marqueList = it?: emptyList()
            }
        }
    }




    var typeMouvementList by  mutableStateOf(emptyList<TypeMouvement>())
        private set
    private fun getTypeMouvement() {
        viewModelScope.launch {
            proCaisseLocalDb.typeMouvement.getAll().collect {
                if(typeMouvementList == it) return@collect
                typeMouvementList = it?: emptyList()
            }
        }
    }

    var exerciceList by  mutableStateOf(emptyList<Exercice>())
        private set


    private fun getExercice() {
        viewModelScope.launch {
            proCaisseLocalDb.exercice.getAll().collect {
                if(exerciceList == it) return@collect
                exerciceList = it ?: emptyList()
            }
        }
    }

    /**
     * Get exercice code safely without crashing when exerciceList is empty
     * This prevents crashes when exerciceList is empty
     */
    fun getExerciceCode(): String {
        return exerciceList.firstOrNull()?.exerciceCode ?: "2024"
    }

    var listActifTimber by  mutableStateOf(emptyList<Timbre>())
        private set
    private fun getActifTimbres() {
        viewModelScope.launch {
            proCaisseLocalDb.timbre.getActif().collect {
              if(listActifTimber == it) return@collect
                listActifTimber = it.ifEmpty { emptyList() }
            }
        }
    }



    var clientList by mutableStateOf<List<Client>>(emptyList())
        private set

    var cltListWithCoordinates = mutableStateListOf<Client>()
        private set

    var cltListWithoutCoordonate = mutableStateListOf<Client>()
        private set

//    fun filterClientList(clientSearchText: String) {
//        cltListWithoutCoordonate = cltListWithoutCoordonate.filter {
//            it.cLINomPren.lowercase(Locale.getDefault()).contains(clientSearchText.lowercase(Locale.getDefault()))
//                    || it.cLICode.lowercase(Locale.getDefault()).contains(clientSearchText.lowercase(Locale.getDefault()))
//        }
//
//        val filteredSelectedLgOrdMissionWithNoCoordClient =
//            selectedLgOrdMissionWithNoCoordClient.filter {
//                it.client?.cLINomPren?.lowercase(Locale.getDefault())?.contains(clientSearchText.lowercase(Locale.getDefault())) == true
//                        || it.client?.cLICode?.lowercase(Locale.getDefault())?.contains(clientSearchText.lowercase(Locale.getDefault())) == true
//            }
//    }

    private fun getClients() {
        viewModelScope.launch {
            proCaisseLocalDb.clients.getAll().collect { clients ->
                // If no clients are fetched, exit early
                if (clients == null) {
                    clientList = emptyList()
                    cltListWithCoordinates.clear()
                    cltListWithoutCoordonate.clear()
                    return@collect
                }

                // Update clientList first, then separate clients by coordinates
                clientList = clients

                // Filter clients into respective lists


                clients.forEach { clt ->
                    if (clt.cltLatitude != null && clt.cltLongitude != null &&
                        clt.cltLatitude != 0.0 && clt.cltLongitude != 0.0) {
                        cltListWithCoordinates.add(clt)
                    } else {
                        cltListWithoutCoordonate.add(clt)
                    }
                }
            }
        }
    }



    fun filterCltListWithoutCoordonate(searchText: String) {
        if(searchText.isEmpty()){
            getClients()
        }
        else {

            cltListWithoutCoordonate.clear()

            for (clt in clientList) {
                if((clt.cltLatitude == null || clt.cltLongitude == null || clt.cltLatitude == 0.0 || clt.cltLongitude == 0.0)
                    &&  (clt.cLINomPren.lowercase(Locale.getDefault()).contains(searchText.lowercase(Locale.getDefault())) ||
                            clt.cLICode.lowercase(Locale.getDefault()).contains(searchText.lowercase(Locale.getDefault()))       ))

                    cltListWithoutCoordonate.add(clt)
            }
        }


    }

    var fournisseurList by  mutableStateOf(emptyList<Fournisseur>())
        private set
    private fun getFournisseurList() {
        viewModelScope.launch {
            proInventoryLocalDb.fournisseur.getAll().collect {
                if(fournisseurList == it) return@collect
                fournisseurList = it.ifEmpty { emptyList() }
            }
        }
    }


    var tvaExpand by savedStateHandle.saveable { mutableStateOf(false) }
        private set
    fun onTvaExpandedChange(value: Boolean) {
        tvaExpand = value
    }




    var uniteArticleExpand by savedStateHandle.saveable { mutableStateOf(false) }
        private set
    fun onUniteArticleExpandedChange(value: Boolean) {
        uniteArticleExpand = value
    }


    var tvaList by mutableStateOf(emptyList<Tva>())
        private set
    private fun getTvaList() {
        viewModelScope.launch {
            proInventoryLocalDb.tva.getAll().collect {
                if(tvaList == it) return@collect
              //  if(it.isNullOrEmpty()) return@collect
                tvaList = it?: emptyList()
            }
        }
    }


    var uniteList by mutableStateOf(emptyList<Unite>())
        private set
    private fun getUniteList() {
        viewModelScope.launch {
            proInventoryLocalDb.unite.getAll().collect {
                if(uniteList == it) return@collect
                uniteList = it.ifEmpty { emptyList() }
            }
        }
    }

    var uniteArticleList by mutableStateOf(emptyList<UniteArticle>())
        private set
    private fun getUniteArticleList() {
        viewModelScope.launch {
            proCaisseLocalDb.uniteArticles.getAll().collect {
                if(uniteArticleList == it) return@collect
                uniteArticleList = it.ifEmpty { emptyList() }
            }
        }
    }




    @OptIn(SavedStateHandleSaveableApi::class)
    var montantTotalStck by  savedStateHandle.saveable { mutableDoubleStateOf(0.0) }
        private set

    var articleMapByBarCode: Map<String, Article> by mutableStateOf(emptyMap())
      private set


    private fun getArticleList() {
        viewModelScope.launch {
            proCaisseLocalDb.articles.getAll().collect { list ->
                articleMapByBarCode = emptyMap()
                var totalMnt = 0.0
                  montantTotalStck = 0.0
                if(list.isNullOrEmpty()) return@collect

                // Create a map for efficientarticle lookup by aRTCode
                articleMapByBarCode = list.associateBy { it.aRTCode }

                 for (i in list.indices) {// TODO DONT INCLUDE PATRIMOINE ARTICLES IN QUERY
                  if(list[i].typeProduit?.lowercase(Locale.ROOT) == ArticleType.ARTICLE.value)
                    //  totalMnt += stringToDouble(list[i].aRTQteStock)  * stringToDouble(list[i].artPrixPublique)
                      totalMnt += list[i].sARTQte  * list[i].pvttc
                }

                montantTotalStck = totalMnt
            }
        }
    }

    var articleCodeBarList by mutableStateOf(emptyList<ArticleCodeBar>())
        private set

    private fun getArticleCodBarList() {
        viewModelScope.launch {
            proCaisseLocalDb.articlesBarCode.getAll().collect { list ->
                if(articleCodeBarList == list) return@collect
                articleCodeBarList = list

            }
        }
    }


    var immobilisationList by mutableStateOf(emptyList<Immobilisation>())
        private set
    private fun getImmobilisationList() {
        viewModelScope.launch {
            proCaisseLocalDb.immobilisation.getAll().collect { listImmobilisation->
                 if(immobilisationList == listImmobilisation) return@collect
                immobilisationList = listImmobilisation?: emptyList()
            }
        }
    }



    var listFamilleDn by  mutableStateOf(emptyList<FamilleDn>())
        private set
    fun getFamille(codeFamille: String): FamilleDn {
        return listFamilleDn.firstOrNull { it.codeFamille == codeFamille }?: FamilleDn()
    }



    private fun getFamilleDNList() {
        viewModelScope.launch() {
            proCaisseLocalDb.familleDn.getAll().collect {
                if(listFamilleDn == it) return@collect
                listFamilleDn = it.ifEmpty { emptyList() }
            }
        }
    }

    var listFamille by  mutableStateOf(emptyList<Famille>())
        private set

    private fun getFamilleList() {
        viewModelScope.launch() {
            proInventoryLocalDb.famille.getAll().collect {
                if(listFamille == it) return@collect
                listFamille = it.ifEmpty { emptyList() }
            }
        }
    }



    var logo by mutableStateOf<Bitmap?>(null)
        private set


    var parametrage by  mutableStateOf(Parametrages())
        private set
    private fun getParametrage() {
        viewModelScope.launch {
            proInventoryLocalDb.parametrage.getOne().collect {
                parametrage = it?: Parametrages()

                if (it == null) return@collect
                if (it.pARAMLogo.isNotBlank()) {
                    logo = base64ToBmp(it.pARAMLogo)
                }

            }
        }
    }

    fun resetLogo() {
        logo = null
    }

    var listVilleDn by  mutableStateOf(emptyList<Ville?>())
        private set
    private fun getVilleList() {
        viewModelScope.launch() {
            proCaisseLocalDb.ville.getAll().collect {
                  if(listVilleDn == it) return@collect
                listVilleDn = it?: emptyList()
            }
        }
    }





 //   var listStationStockArticl = mutableStateListOf<StationStockArticle>()
   //     private set

    var stationStockArticlMapByBarCode: Map<String, StationStockArticle> by mutableStateOf(emptyMap())
        private set

    private fun getAllStationStockArticle() {

        viewModelScope.launch(mainDispatcher) {
            proInventoryLocalDb.stationsArticle.getAllStationStockArticle().collect {list ->
                  if(list == null) {
                      stationStockArticlMapByBarCode = emptyMap()
                      return@collect
                  }
                stationStockArticlMapByBarCode = list.associateBy { it.sARTCodeArt + it.sARTCodeSatation }



            }

        }
    }







    fun insertStationStockArticle(stationStockArticl: StationStockArticle) {
        viewModelScope.launch(dispatcherIO) {
            proInventoryLocalDb.stationsArticle.upsert(
                value = stationStockArticl
            )

        }
    }

    fun updateArtQteStock(
        newQteAllStations: String,
        newQteStation: String,
        codeArticle: String,
    ) {
        viewModelScope.launch(dispatcherIO) {
            proCaisseLocalDb.articles.updateArtQteStock(
                newQteAllStations = newQteAllStations,
                newQteStation = newQteStation,
                codeArticle = codeArticle
            )

        }
    }

    fun updateQtePerStation(
        newQteStation: String,
        newSartQteDeclare: String,
        codeArticle: String,
        codeStation: String
    ) {
        viewModelScope.launch(dispatcherIO) {
            proInventoryLocalDb.stationsArticle.updateQtePerStation(
                newQteStation = newQteStation,
                sartQteDeclare = newSartQteDeclare,
                codeArticle = codeArticle,
                codeStation = codeStation
            )

        }
    }



    var listConcurentVC by  mutableStateOf(emptyList<ConcurrentVC>())
        private set
    fun getConcurentVC(codeConcurent: String): ConcurrentVC {
        return if(listConcurentVC.any{ it.codeconcurrent == codeConcurent }) listConcurentVC.first { it.codeconcurrent == codeConcurent }
        else ConcurrentVC(   codeconcurrent = codeConcurent,
         concurrent ="N/A",)


    }

    private fun getConcurentVCList() {
        viewModelScope.launch() {
            proCaisseLocalDb.listConcurrentVC.getAll().collect {
                if(listConcurentVC == it) return@collect
                listConcurentVC = it?: emptyList()
            }
        }
    }



    var selectedDate by savedStateHandle.saveable { mutableStateOf("") }
        private set
    fun onSelectedDateChange(value: String) {
        selectedDate = value
    }


    private var selectedTime by savedStateHandle.saveable { mutableStateOf("") }
        private set
    fun onSelectedTimeChange(value: String) {
        selectedTime = value
    }


    fun getSelectedDateTime(): String =
        if (selectedDate == "" && selectedTime == "")
            getCurrentDateTime()
        else {
            if (selectedDate != "" && selectedTime == "") "$selectedDate ${getCurrentTime()}"
            else "$selectedDate $selectedTime"
        }


    fun resetTimePicker() {
        selectedDate = ""
        selectedTime = ""

    }

    var showDismissScreenAlertDialog by savedStateHandle.saveable { mutableStateOf(false) }
        private set
    fun onShowDismissScreenAlertDialogChange(value: Boolean) {
        showDismissScreenAlertDialog = value
    }


    var showAlertDialog by savedStateHandle.saveable { mutableStateOf(false) }
        private set
    fun onShowAlertDialogChange(value: Boolean) {
        showAlertDialog = value
    }



    var stationExpand by savedStateHandle.saveable { mutableStateOf(false) }
        private set
    fun onStationExpandedChange(value: Boolean) {
        stationExpand = value
    }


    var stationDestinationExpand by savedStateHandle.saveable { mutableStateOf(false) }
        private set
    fun onStationDestinationExpandedChange(value: Boolean) {
        stationDestinationExpand = value
    }





    var closeSessionCaisseState: RemoteResponseState<Boolean> by mutableStateOf(RemoteResponseState())
        private set

    fun resetCloseSessionCaisseState(){
        closeSessionCaisseState = RemoteResponseState()
    }

    fun updateSessionAfterClose(sessionCaisse: SessionCaisse, navDrawerViewModel: NavigationDrawerViewModel? = null) {
        viewModelScope.launch(dispatcherIO) {
            try {
                val updatedSession = sessionCaisse.copy(sCClotCaisse = 1)
                proCaisseLocalDb.sessionCaisse.upsert(updatedSession)
                println("DEBUG: Session updated in database with sCClotCaisse = 1")

                // Switch to Main dispatcher for UI updates
                withContext(Dispatchers.Main) {
                    // Update NavigationDrawerViewModel state immediately for instant UI updates
                    navDrawerViewModel?.updateSessionState(updatedSession)

                    // Force immediate state update by triggering a recomposition
                    // This ensures the UI updates immediately without waiting for database observation
                    closeSessionCaisseState = closeSessionCaisseState.copy(
                        data = true,
                        loading = false,
                        error = null
                    )
                }
            } catch (e: Exception) {
                println("DEBUG: Error updating session: ${e.message}")
                withContext(Dispatchers.Main) {
                    closeSessionCaisseState = closeSessionCaisseState.copy(
                        data = false,
                        loading = false,
                        error = e.message
                    )
                }
            }
        }
    }

    /**
     * Open a new session
     */
    fun openSession(
        baseConfig: BaseConfig,
        sessionActivation: SessionActivation,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        try {
            viewModelScope.launch {
                val baseConfigObj = GenericObject(
                    baseConfig,
                    Json.encodeToJsonElement(sessionActivation)
                )

                proCaisseRemote.sessionCaisse.addSessionVendeur(Json.encodeToString(baseConfigObj), "").collect { result ->
                    when (result) {
                        is DataResult.Success -> {
                            result.data?.let { newSession ->
                                // Update local database
                                proCaisseLocalDb.sessionCaisse.upsert(newSession)
                                onSuccess()
                            } ?: run {
                                onError("Failed to create session")
                            }
                        }
                        is DataResult.Error -> {
                            onError(result.message ?: "Unknown error")
                        }
                        is DataResult.Loading -> {
                            // Handle loading state if needed
                        }
                    }
                }
            }
        } catch (e: Exception) {
            onError(e.message ?: "Unknown error")
        }
    }
    fun closeSessionCaisse(
        baseConfig: BaseConfig,
        sessionCaisse: SessionCaisse
    ) {
        try {
            viewModelScope.launch {
                val closeSessionCaisse = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sessionCaisse.sCIdSCaisse)
                    )
                    proCaisseRemote.sessionCaisse.closeSessionVendeur(Json.encodeToString(baseConfigObj), facture = true)
                }
                closeSessionCaisse.await().onEach { result ->
                    closeSessionCaisseState = when (result) {
                        is DataResult.Success -> {
                            RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            RemoteResponseState(data = null, loading = false, error = result.message)
                        }

                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }

        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/
    }




    var addSessionCaisseState: RemoteResponseState<AddSessionCaisseResponse> by mutableStateOf(RemoteResponseState())
        private set

    fun resetAddSessionCaisseState(){
        addSessionCaisseState = RemoteResponseState()
    }
    fun addSessionCaisse(
        baseConfig: BaseConfig,
        utilisateur: Utilisateur,
        sessionCaisse: SessionCaisse,
        androidId: String
    ) {
        try {
            viewModelScope.launch {
                val addSessionCaisse = async {

                    val sessionActivation = SessionActivation(
                        //session = utilisateur.Code_Caisse,
                     caisse = utilisateur.Code_Caisse,
                     utilisateur = utilisateur.codeUt,
                     carnet = utilisateur.Code_Carnet,
                     station = utilisateur.Station,
                     fondCaisse = fondcaisse,
                     nomMachine = androidId
                    )
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(sessionActivation)
                    )
                    // Use addSessionVendeur instead of addSession to only create a new session
                    // without automatically closing the current session
                    proCaisseRemote.sessionCaisse.addSessionVendeur(
                        Json.encodeToString(baseConfigObj),
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse
                    )
                }
                addSessionCaisse.await().onEach { result ->
                    addSessionCaisseState = when (result) {
                        is DataResult.Success -> {
                            if(result.data != null){
                                // For addSessionVendeur, result.data is directly a SessionCaisse object
                                val sessionCaisse: SessionCaisse = result.data
                                proCaisseLocalDb.sessionCaisse.upsert(sessionCaisse)
                                // The NavigationDrawerViewModel will automatically observe this change
                                // and update the sessionCaisse property, which will trigger UI recomposition

                                // Create a compatible response for the UI
                                val compatibleResponse = AddSessionCaisseResponse(
                                    sessionCaisse = sessionCaisse,
                                    error = false,
                                    message = "Session created successfully"
                                )
                                RemoteResponseState<AddSessionCaisseResponse>(data = compatibleResponse, loading = false, error = null)
                            } else {
                                RemoteResponseState<AddSessionCaisseResponse>(data = null, loading = false, error = "No session data received")
                            }
                        }

                        is DataResult.Loading -> {
                            RemoteResponseState<AddSessionCaisseResponse>(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            RemoteResponseState<AddSessionCaisseResponse>(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }

        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)
    }*/
    }


    var selectedBaseconfig by mutableStateOf(BaseConfig())
        private set

    fun onSelectedBaseconfigChange(baseConfig: BaseConfig){
         selectedBaseconfig = baseConfig
    }



    fun hasPromo(station: Station?): Boolean {

        return if (station == null) false
        else durationSincePast(
            dateInPast = station.sTATSoldeDD ?: "2023-05-15 01:00:00"
        ) <= 0 &&
                station.sTATSolde.equals("1")

    }


}

