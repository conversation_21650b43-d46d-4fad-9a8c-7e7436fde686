package com.asmtunis.procaisseinventory.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.authorizations.AuthorizationFunction.addToAuthList
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory.INEVENTORY_DRAWER_ITEM
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory.PURCHASE_DRAWER_ITEM
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory.TRANSFERT_DRAWER_ITEM
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.ktor.insertNetworkError
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import java.io.IOException
import javax.inject.Inject


@HiltViewModel
class GetProInventoryDataViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proInventoryRemote: ProInventoryRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb,
) : ViewModel() {
    init {
        viewModelScope.launch {
            proCaisseLocalDb.authorization.getAll().collect {
                if(it==null) return@collect
                setAuthList(autorisationUser = it)
            }
        }


    }

    var authorizationList = ArrayList<Authorization>() // ArrayList<String>()
    private val grantedAuthorizationList = ArrayList<String>() // ArrayList<String>()


   
    var stationState: RemoteResponseState<List<Station>> by mutableStateOf(RemoteResponseState())
        private set




    var typePrixUnitaireHTState: RemoteResponseState<List<TypePrixUnitaireHT>> by mutableStateOf(RemoteResponseState())
        private set

    var tvaState: RemoteResponseState<List<Tva>> by mutableStateOf(RemoteResponseState())
        private set




    var fournisseurState: RemoteResponseState<List<Fournisseur>> by mutableStateOf(RemoteResponseState())
        private set




    var bonEntreeState: RemoteResponseState<List<BonEntree>> by mutableStateOf(RemoteResponseState())
        private set

    var ligneBonEntreeState: RemoteResponseState<List<LigneBonEntree>> by mutableStateOf(RemoteResponseState())
        private set

    var bonLivraisonState: RemoteResponseState<List<BonLivraison>> by mutableStateOf(RemoteResponseState())
        private set

    var ligneBonLivraisonState: RemoteResponseState<List<LigneBonLivraison>> by mutableStateOf(RemoteResponseState())
        private set


    var inventaireState: RemoteResponseState<List<Inventaire>> by mutableStateOf(RemoteResponseState())
        private set

    var ligneInventaireState: RemoteResponseState<List<LigneInventaire>> by mutableStateOf(RemoteResponseState())
        private set


    fun resetligneInventaireState() {
        ligneInventaireState = RemoteResponseState()
    }
    














    /*fun getLigneInventaire(
        baseConfig: BaseConfig
    ): Flow<DataResult<List<LigneInventaire>>> {
        //  var baseConfigObj   : GenericObject

        val baseConfigObj = GenericObject(
            baseConfig,
            null
        )

        return proInventoryRemote.ligneInventaire.getLigneInventaires(Json.encodeToString(baseConfigObj))

    }*/

    fun getLigneInventaires(baseConfig: BaseConfig, code : String){
        viewModelScope.launch {
            val baseConfigObj = GenericObject(
                baseConfig,
                null
            )

           val firstRequest = async {
                proInventoryRemote.ligneInventaire.getLigneInventairesByCode(baseConfig = Json.encodeToString(baseConfigObj), code = code)
       }



            firstRequest.await().onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        proInventoryLocalDb.ligneInventaire.upsertAll(result.data!!)


                        ligneInventaireState = RemoteResponseState(data = result.data, loading = false, error = null)

                    }

                    is DataResult.Loading -> {
                        ligneInventaireState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        insertNetworkError(
                            proCaisseLocalDb = proCaisseLocalDb,
                            url = Urls.GET_LIGNE_INVENTAIRES_BY_CODE,
                            extraInfo = "Inventaire Code: $code",
                            errorMessage = result.message
                        )

                        ligneInventaireState = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)

        }
    }



    private fun setAuthList(autorisationUser: List<Authorization>?) {
        authorizationList.clear()
        grantedAuthorizationList.clear()
        authorizationList.addAll(addToAuthList(result = autorisationUser?: emptyList()))
        grantedAuthorizationList.addAll(addToAuthList(result = autorisationUser?: emptyList()).map { authorisation -> authorisation.AutoCodeAu })
    }

    suspend fun getProInventoryData(baseConfig: BaseConfig, utilisateur: Utilisateur) = coroutineScope {
        try {
            if (grantedAuthorizationList.isEmpty()) {
                setAuthList(autorisationUser = utilisateur.autorisationUser)

            }

            // List of tasks to be executed concurrently
            val tasks =  if (grantedAuthorizationList.isNotEmpty()) {
                listOf(
                    async { getTypePrixUnitaireHT(baseConfig) },
                    async { getTva(baseConfig) },
                    async { getFournisseur(baseConfig) }
                    )
            } else emptyList()

            val bonEntreeTask =  if (grantedAuthorizationList.contains(PURCHASE_DRAWER_ITEM)) {
                listOf(
                    async { getBonEntree(baseConfig) },
                    async { getLigneBonEntree(baseConfig) },
                )
            } else emptyList()


            val tranfertTask =  if (grantedAuthorizationList.contains(TRANSFERT_DRAWER_ITEM)) {
                 listOf(
                     async { getBonLivraison(baseConfig) },
                     async { getLigneBonLivraison(baseConfig) },
                )
            } else emptyList()

            val inventaireTask =  if (grantedAuthorizationList.contains(INEVENTORY_DRAWER_ITEM)) {
                 listOf(async { getInventaire(baseConfig) })
            } else emptyList()


            // Wait for all tasks to complete
            (tasks + inventaireTask + tranfertTask + bonEntreeTask).awaitAll()
        } catch (e: IOException) {
            // Handle the error and log it

            /* Example: Insert network error into local DB if required
            insertNetworkError(
                proCaisseLocalDb = proCaisseLocalDb,
                url = "get all data Inventory error",
                extraInfo = "",
                errorMessage = e.message ?: "Unknown error"
            )
            */
        }
    }


    fun getTypePrixUnitaireHT(baseConfig: BaseConfig) {
        try {
            viewModelScope.launch {

                val getTypePrixUnitaireHT = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                proInventoryRemote.typePrix.getTypePrixUnitaireHT(Json.encodeToString(baseConfigObj))
                }

                getTypePrixUnitaireHT.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proInventoryLocalDb.typePrix.deleteAll()
                            proInventoryLocalDb.typePrix.upsertAll(result.data!!)

                            typePrixUnitaireHTState = RemoteResponseState(data = result.data, loading = false, error = null)

                        }

                        is DataResult.Loading -> {

                            typePrixUnitaireHTState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_TYPE_PRIX_UNITAIRE_HT,
                                errorMessage = result.message
                            )

                            typePrixUnitaireHTState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)*/
    }

    fun getTva(baseConfig: BaseConfig) {
        try {
            viewModelScope.launch {


                val getTva = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proInventoryRemote.tva.getTVA(Json.encodeToString(baseConfigObj))
                }

                getTva.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proInventoryLocalDb.tva.deleteAll()
                            proInventoryLocalDb.tva.upsertAll(result.data!!)

                            tvaState = RemoteResponseState(data = result.data, loading = false, error = null)

                        }

                        is DataResult.Loading -> {

                            tvaState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_TVA,
                                errorMessage = result.message
                            )
                            tvaState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)



            }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)*/
    }

    fun getFournisseur(baseConfig: BaseConfig) {
        try {
            viewModelScope.launch {
                val getFournisseur = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proInventoryRemote.fournisseur.getFournisseurs(Json.encodeToString(baseConfigObj))
                }

                getFournisseur.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proInventoryLocalDb.fournisseur.deleteAll()
                            proInventoryLocalDb.fournisseur.upsertAll(result.data!!)

                            fournisseurState = RemoteResponseState(data = result.data, loading = false, error = null)

                        }

                        is DataResult.Loading -> {

                            fournisseurState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_FOURNISSEURS,
                                errorMessage = result.message
                            )
                            fournisseurState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)*/
    }


    fun getBonEntree(
        baseConfig: BaseConfig,
        nbrMois: String = ""
    ) {
        try {
            viewModelScope.launch {

                if (grantedAuthorizationList.contains(PURCHASE_DRAWER_ITEM)) {
                    val getBonEntree = async {
                        val baseConfigObj = GenericObject(
                            baseConfig,
                            null
                        )

                         proInventoryRemote.bonEntree.getBonEntrees(
                             baseConfig = Json.encodeToString(baseConfigObj),
                             mois =  nbrMois
                         )
                    }
                    getBonEntree.await().onEach { result ->
                        when (result) {
                            is DataResult.Success -> {
                                proInventoryLocalDb.bonEntree.deleteAll()
                                proInventoryLocalDb.bonEntree.upsertAll(result.data!!)

                                bonEntreeState = RemoteResponseState(data = result.data, loading = false, error = null)
                            }

                            is DataResult.Loading -> {
                                bonEntreeState = RemoteResponseState(data = null, loading = true, error = null)
                            }

                            is DataResult.Error -> {
                                proInventoryLocalDb.bonEntree.deleteAll()

                                insertNetworkError(
                                    proCaisseLocalDb = proCaisseLocalDb,
                                    url = Urls.GET_BON_ENTREES,
                                    extraInfo = nbrMois,
                                    errorMessage = result.message
                                )
                                bonEntreeState = RemoteResponseState(data = null, loading = false, error = result.message)
                            }
                        }
                    }.flowOn(dispatcherIO).launchIn(this)
                }



            }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)*/
    }


    fun getLigneBonEntree(
        baseConfig: BaseConfig,
        nbrMois: String  = ""
    ) {
        try {
            viewModelScope.launch {

                if (grantedAuthorizationList.contains(PURCHASE_DRAWER_ITEM)) {
                    val getLigneBonEntree = async {
                        val baseConfigObj = GenericObject(
                            baseConfig,
                            null
                        )

                          proInventoryRemote.ligneBonEntree.getLigneBonEntrees(
                              baseConfig = Json.encodeToString(baseConfigObj),
                              mois = nbrMois
                        )
                    }

                    getLigneBonEntree.await().onEach { result ->
                        when (result) {
                            is DataResult.Success -> {
                                proInventoryLocalDb.ligneBonEntree.deleteAll()
                                proInventoryLocalDb.ligneBonEntree.upsertAll(result.data!!)

                                ligneBonEntreeState = RemoteResponseState(data = result.data, loading = false, error = null)

                            }

                            is DataResult.Loading -> {

                                ligneBonEntreeState = RemoteResponseState(data = null, loading = true, error = null)
                            }

                            is DataResult.Error -> {
                                proInventoryLocalDb.ligneBonEntree.deleteAll()

                                proInventoryLocalDb.bonEntree.deleteAll()

                                insertNetworkError(
                                    proCaisseLocalDb = proCaisseLocalDb,
                                    url = Urls.GET_LIGNE_BON_ENTREES,
                                    extraInfo = nbrMois,
                                    errorMessage = result.message
                                )
                                ligneBonEntreeState =
                                    RemoteResponseState(
                                        data = null,
                                        loading = false,
                                        error = result.message
                                    )
                            }
                        }
                    }.flowOn(dispatcherIO).launchIn(this)

                }



            }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)*/
    }


    fun getBonLivraison(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {

                if (grantedAuthorizationList.contains(TRANSFERT_DRAWER_ITEM)) {
                    val getBonLivraison = async {
                        val baseConfigObj = GenericObject(
                            baseConfig,
                            null
                        )

                         proInventoryRemote.bonLivraison.getBonLivraison(Json.encodeToString(baseConfigObj))
                    }

                    getBonLivraison.await().onEach { result ->
                        when (result) {
                            is DataResult.Success -> {
                                proInventoryLocalDb.bonLivraison.deleteAll()
                                proInventoryLocalDb.bonLivraison.upsertAll(result.data!!)

                                bonLivraisonState = RemoteResponseState(data = result.data, loading = false, error = null)

                            }

                            is DataResult.Loading -> {

                                bonLivraisonState = RemoteResponseState(data = null, loading = true, error = null)
                            }

                            is DataResult.Error -> {

                                insertNetworkError(
                                    proCaisseLocalDb = proCaisseLocalDb,
                                    url = Urls.GET_BON_LIVRAISON,
                                    errorMessage = result.message
                                )

                                bonLivraisonState =
                                    RemoteResponseState(
                                        data = null,
                                        loading = false,
                                        error = result.message
                                    )
                            }
                        }
                    }.flowOn(dispatcherIO).launchIn(this)

                }

            }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)*/
    }


    fun getLigneBonLivraison(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {

                if (grantedAuthorizationList.contains(TRANSFERT_DRAWER_ITEM)) {


                    val getLigneBonLivraison = async {
                        val baseConfigObj = GenericObject(
                            baseConfig,
                            null
                        )

                          proInventoryRemote.ligneBonLivraison.getLigneBonLivraison(Json.encodeToString(baseConfigObj))
                    }

                    getLigneBonLivraison.await().onEach { result ->
                        when (result) {
                            is DataResult.Success -> {
                                proInventoryLocalDb.ligneBonLivraison.deleteAll()
                                proInventoryLocalDb.ligneBonLivraison.upsertAll(result.data!!)

                                ligneBonLivraisonState = RemoteResponseState(data = result.data, loading = false, error = null)

                            }

                            is DataResult.Loading -> {

                                ligneBonLivraisonState = RemoteResponseState(data = null, loading = true, error = null)
                            }

                            is DataResult.Error -> {

                                insertNetworkError(
                                    proCaisseLocalDb = proCaisseLocalDb,
                                    url = Urls.GET_LIGNE_BON_LIVRAISON,
                                    errorMessage = result.message
                                )

                                ligneBonLivraisonState =
                                    RemoteResponseState(
                                        data = null,
                                        loading = false,
                                        error = result.message
                                    )
                            }
                        }
                    }.flowOn(dispatcherIO).launchIn(this)

                }



            }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)*/
    }


    fun getInventaire(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {

                /**
                 * ***********************************************
                 * I N V E N T A I R E
                 * **********************************************
                 */


                    val getInventaire = async {

                        val baseConfigObj = GenericObject(
                            baseConfig,
                            null
                        )

                          proInventoryRemote.inventaire.getInventaires(Json.encodeToString(baseConfigObj))
                    }

                    getInventaire.await().onEach { result ->
                        when (result) {
                            is DataResult.Success -> {
                                proInventoryLocalDb.inventaire.deleteAll()
                                proInventoryLocalDb.ligneInventaire.deleteAll()
                                proInventoryLocalDb.inventaire.upsertAll(result.data!!)




                                //todo see if this implemenattion causes problems then simply fetch lignes when user click on parent
                               /* for (inventaire in result.data) {
                                    getLigneInventaires(baseConfig, inventaire.iNVCode)
                               }*/


                                if(result.data.isNotEmpty()) {
                                    getLigneInventaires(baseConfig, result.data.last().iNVCode)
                                }




                                inventaireState = RemoteResponseState(data = result.data, loading = false, error = null)

                            }

                            is DataResult.Loading -> {

                                inventaireState =
                                    RemoteResponseState(data = null, loading = true, error = null)
                            }

                            is DataResult.Error -> {


                                insertNetworkError(
                                    proCaisseLocalDb = proCaisseLocalDb,
                                    url = Urls.GET_INVENTAIRES,
                                    errorMessage = result.message
                                )

                                inventaireState =
                                    RemoteResponseState(
                                        data = null,
                                        loading = false,
                                        error = result.message
                                    )
                            }
                        }
                    }.flowOn(dispatcherIO).launchIn(this)




                    /*   val getLigneInventaire = async {
                           getLigneInventaire(baseConfig)
                       }

                       getLigneInventaire.await().onEach { result ->
                           when (result) {
                               is Resource.Success -> {
                                   proInventoryLocalDb.ligneInventaire.upsertAll(result.data!!)

                                   ligneInventaireState =
                                       LigneInventaireState(
                                           data = result.data,
                                           loading = false,
                                           error = null
                                       )

                               }

                               is Resource.Loading -> {

                                   ligneInventaireState =
                                       LigneInventaireState(data = null, loading = true, error = null)
                               }

                               is Resource.Error -> {
                                   ligneInventaireState =
                                       LigneInventaireState(
                                           data = null,
                                           loading = false,
                                           error = result.message
                                       )
                               }
                           }
                       }.flowOn(dispatcherIO).launchIn(this)*/
                }
        } catch (e: IOException) {
            // handle this

            // throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
        // handle this too
        throw MyOtherException("Error doing something", e)*/
    }


    fun resetStates(){
            stationState = RemoteResponseState()
            typePrixUnitaireHTState = RemoteResponseState()
            tvaState = RemoteResponseState()

            fournisseurState = RemoteResponseState()
            bonEntreeState = RemoteResponseState()
            ligneBonEntreeState = RemoteResponseState()
            bonLivraisonState = RemoteResponseState()
            ligneBonLivraisonState = RemoteResponseState()
            inventaireState = RemoteResponseState()
            ligneInventaireState = RemoteResponseState()
}


}
