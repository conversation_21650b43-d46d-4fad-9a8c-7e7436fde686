package com.asmtunis.procaisseinventory.core.navigation.nav_graph

import android.content.Intent
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.navigationBarsIgnoringVisibility
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalFocusManager
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ArticleTextValidationViewModel
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.articles.consultation.view_model.SyncArticlesViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.SelectPatrimoineViewModel
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AuthGraph
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.core.sync_workmanager.SyncWorkerViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model.BonCommandeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model.SyncBonCommandeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.BonLivraisonViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model.BonRetourViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model.SyncBonRetourViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.text_validation.ClientTextValidationViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.view_model.ClientViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.view_model.SyncClientViewModel
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.DashboardScreenViewModel
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model.DistributionNumeriqueViewModel
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model.SyncDistributionNumViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.view_model.DeplacementOutByUserViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.InventaireBatimentViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.SyncInvBatimentViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.SyncInvPatrimoineViewModel
import com.asmtunis.procaisseinventory.pro_caisse.reglement.view_model.ReglementCaisseViewModel
import com.asmtunis.procaisseinventory.pro_caisse.reglement.view_model.SyncReglementViewModel
import com.asmtunis.procaisseinventory.pro_caisse.tournee.view_model.SyncTourneeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.tournee.view_model.TourneeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.AutreViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.NewProductViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.PrixViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.PromotionViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.SyncVcViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.VeilleConcurentielViewModel
import com.asmtunis.procaisseinventory.pro_inventory.achat.AchatViewModel
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.BonTransfertViewModel
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.InventaireViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens.TicketRayonViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.view_model.PaymentViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.asmtunis.procaisseinventory.view_model.ProInventoryViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.FlowPreview

@Composable
@OptIn(ExperimentalLayoutApi::class, FlowPreview::class)
@ExperimentalMaterial3Api

fun AppNavigation(
    intent: Intent,
    dataViewModel: DataViewModel,
    settingViewModel: SettingViewModel,
    barCodeViewModel: BarCodeViewModel,
    bluetoothVM: BluetoothViewModel,
    syncWorkerVM: SyncWorkerViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    syncCompletionViewModel: com.asmtunis.procaisseinventory.core.sync.SyncCompletionViewModel
) {
    val navController = rememberNavController() // It’s considered best practice to not pass a NavHostController to individual composables.




 //   val clientTextValidationViewModel = hiltViewModel<ClientTextValidationViewModel>()

    val getProCaisseDataViewModel = hiltViewModel<GetProCaisseDataViewModel>()
    val networkViewModel = hiltViewModel<NetworkViewModel>()

    val locationViewModule = hiltViewModel<LocationViewModule>()
    val articlesViewModel = hiltViewModel<ArticlesViewModel>()

    val cameraViewModel = hiltViewModel<CameraViewModel>()


    val getProInventoryDataViewModel = hiltViewModel<GetProInventoryDataViewModel>()
    val articleTxtValidViewModel = hiltViewModel<ArticleTextValidationViewModel>()


    val mainViewModel = hiltViewModel<MainViewModel>()





    val selectArtMobilityVM = hiltViewModel<SelectArticleCalculViewModel>()

    val printViewModel = hiltViewModel<PrintViewModel>()
    val wifiPrintVM = hiltViewModel<WifiPrintViewModel>()
    val sunmiPrintManager = hiltViewModel<SunmiPrintManager>()



    /**
     * INVENTORY
     */

    val proInventoryViewModels = ProInventoryViewModels(
         achatViewModel = hiltViewModel<AchatViewModel>(),
     bonTransfertViewModel = hiltViewModel<BonTransfertViewModel>(),
     inventaireViewModel = hiltViewModel<InventaireViewModel>()
    )


    val getSharedDataViewModel = hiltViewModel<GetSharedDataViewModel>()
    val syncInventoryViewModel = hiltViewModel<SyncInventoryViewModel>()
    val ticketRayonViewModel = hiltViewModel<TicketRayonViewModel>()

    val selectArtInventoryVM = hiltViewModel<SelectArticleNoCalculViewModel>()


    val authViewModel = hiltViewModel<AuthViewModel>()
    val networkErrorsVM = hiltViewModel<NetworkErrorsViewModel>()

    val proCaisseViewModels = ProCaisseViewModels (
        batimentViewModel = hiltViewModel<InventaireBatimentViewModel>(),
        clientViewModel = hiltViewModel<ClientViewModel>(),
        distNumViewModel = hiltViewModel<DistributionNumeriqueViewModel>(),
        veilleConcurentielViewModel = hiltViewModel<VeilleConcurentielViewModel>(),
        newProdViewModel = hiltViewModel<NewProductViewModel>(),
        promotionViewModel = hiltViewModel<PromotionViewModel>(),
        prixViewModel = hiltViewModel<PrixViewModel>(),
        autreViewModel = hiltViewModel<AutreViewModel>(),
        clientTextValidationViewModel = hiltViewModel<ClientTextValidationViewModel>(),
        reglementCaisseViewModel = hiltViewModel<ReglementCaisseViewModel>(),
        bonLivraisonViewModel = hiltViewModel<BonLivraisonViewModel>(),
        bonCommandeViewModel = hiltViewModel<BonCommandeViewModel>(),
        bonRetourViewModel = hiltViewModel<BonRetourViewModel>(),
        invPatViewModel = hiltViewModel<com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.InventaireViewModel>(),
        selectPatrimoineVM = hiltViewModel<SelectPatrimoineViewModel>(),
        //dashboardScreenVM = hiltViewModel<DashboardScreenViewModel>(),
        tourneeViewModel = hiltViewModel<TourneeViewModel>(),
        deplacementOutByUserViewModel = hiltViewModel<DeplacementOutByUserViewModel>(),
        paymentViewModel = hiltViewModel<PaymentViewModel>(),
    )
    val syncSharedViewModels: SyncSharedViewModels = SyncSharedViewModels(
        syncArticlesViewModel = hiltViewModel<SyncArticlesViewModel>()
    )

    val syncProcaisseViewModels = SyncProcaisseViewModels(
         syncVcViewModel = hiltViewModel<SyncVcViewModel>(),
     syncDistNumViewModel = hiltViewModel<SyncDistributionNumViewModel>(),
     syncReglementViewModel = hiltViewModel<SyncReglementViewModel>(),
     syncBonRetourViewModel = hiltViewModel<SyncBonRetourViewModel>(),
     syncInvPatrimoineViewModel = hiltViewModel<SyncInvPatrimoineViewModel>(),
     syncBonCommandeViewModel = hiltViewModel<SyncBonCommandeViewModel>(),
        syncBonLivraisonVM = hiltViewModel<SyncBonLivraisonViewModel>(),
     syncClientViewModel = hiltViewModel<SyncClientViewModel>(),
     syncTourneeViewModel = hiltViewModel<SyncTourneeViewModel>(),
     syncInvBatimentViewModel = hiltViewModel<SyncInvBatimentViewModel>()
    )

    val dashboardScreenVM = hiltViewModel<DashboardScreenViewModel>()
    // val baseConfigViewModel: BaseConfigViewModel = hiltViewModel()
    val focusManager = LocalFocusManager.current // this to make keyboard hide when tap on screen
    NavHost(
        modifier = Modifier
            .windowInsetsPadding(WindowInsets.navigationBarsIgnoringVisibility)
            .pointerInput(Unit) {
            detectTapGestures (onTap = { focusManager.clearFocus() })
        },
        navController = navController,
        startDestination = AuthGraph,
//        enterTransition = {
//            slideIntoContainer(
//                AnimatedContentTransitionScope.SlideDirection.Left,
//                animationSpec = tween(500)
//            )
//        },
//        exitTransition = {
//            slideOutOfContainer(
//                AnimatedContentTransitionScope.SlideDirection.Left,
//                animationSpec = tween(500)
//            )
//        },
//        popEnterTransition = {
//            slideIntoContainer(
//                AnimatedContentTransitionScope.SlideDirection.Right,
//                animationSpec = tween(500)
//            )
//        },
//        popExitTransition = {
//            slideOutOfContainer(
//                AnimatedContentTransitionScope.SlideDirection.Right,
//                animationSpec = tween(500)
//            )
//        }
     //   route = ROOT_GRAPH_ROUTE,
    ) {

        authNavGraph(
            navigate = { navController.navigate(it) },
            navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                navController.navigate(route) {
                    popUpTo(popUpTo) {
                        inclusive = isInclusive
                    }
                }
            },
            popBackStack = { navController.popBackStack() },
            navigateUp = { navController.navigateUp() },
            dataViewModel = dataViewModel,
            networkViewModel = networkViewModel,
            getProCaisseDataViewModel = getProCaisseDataViewModel,
            getProInventoryDataViewModel = getProInventoryDataViewModel,
            navigationDrawerViewModel = navigationDrawerViewModel,
            getSharedDataViewModel = getSharedDataViewModel,
            authViewModel = authViewModel,
            settingViewModel = settingViewModel,
            mainViewModel = mainViewModel,
            networkErrorsVM = networkErrorsVM,
        )

        proCaisseNavGraph(
            navigate = { navController.navigate(it) /*{
                // Pop up to the start destination of the graph to
                // avoid building up a large stack of destinations
                // on the back stack as users select items
                popUpTo(navController.graph.findStartDestination().id) {
                    saveState = true
                }
                // Avoid multiple copies of the same destination when
                // reselecting the same item
                launchSingleTop = true
                // Restore state when reselecting a previously selected item
                restoreState = true
            }*/

                       },
            navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                navController.navigate(route) {
                     popUpTo(popUpTo) {
                        inclusive = isInclusive
                    }
                }
            },
            popBackStack = { navController.popBackStack() },
            navigateUp = { navController.navigateUp() },
            networkErrorsVM = networkErrorsVM,
            intent = intent,
            dataViewModel = dataViewModel,
            networkViewModel = networkViewModel,
            getProCaisseDataViewModel = getProCaisseDataViewModel,
            getProInventoryDataViewModel = getProInventoryDataViewModel,
            navigationDrawerViewModel = navigationDrawerViewModel,
            locationViewModule = locationViewModule,
            articlesViewModel = articlesViewModel,
            mainViewModel = mainViewModel,
            cameraViewModel = cameraViewModel,
            barCodeViewModel = barCodeViewModel,
            articleTxtValidViewModel = articleTxtValidViewModel,
            settingVM = settingViewModel,
            getSharedDataViewModel = getSharedDataViewModel,
            selectArtMobilityVM = selectArtMobilityVM,
            printViewModel = printViewModel,
            bluetoothVM = bluetoothVM,
            syncWorkerVM = syncWorkerVM,
            wifiPrintVM = wifiPrintVM,
            sunmiPrintManager = sunmiPrintManager,
            syncInventoryViewModel = syncInventoryViewModel,
            syncSharedViewModels = syncSharedViewModels,
            syncProcaisseViewModels = syncProcaisseViewModels,
            proCaisseViewModels = proCaisseViewModels,
            dashboardScreenVM = dashboardScreenVM,
            syncCompletionViewModel = syncCompletionViewModel,
        )

        proInventoryNavGraph(
            navigate = { navController.navigate(it)/*{
                // Pop up to the start destination of the graph to
                // avoid building up a large stack of destinations
                // on the back stack as users select items
                popUpTo(navController.graph.findStartDestination().id) {
                    saveState = true
                }
                // Avoid multiple copies of the same destination when
                // reselecting the same item
                launchSingleTop = true
                // Restore state when reselecting a previously selected item
                restoreState = true
            } */
                       },
            navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                navController.navigate(route) {
                    popUpTo(popUpTo) {
                        inclusive = isInclusive
                    }
                }
            },
            popBackStack = { navController.popBackStack() },
            navigateUp = { navController.navigateUp() },
            dataViewModel = dataViewModel,
            networkErrorsVM = networkErrorsVM,
            networkViewModel = networkViewModel,
            navigationDrawerViewModel = navigationDrawerViewModel,
            getProCaisseDataViewModel = getProCaisseDataViewModel,
            articlesViewModel = articlesViewModel,
            articleTxtValidViewModel = articleTxtValidViewModel,
            barCodeViewModel = barCodeViewModel,
            getProInventoryDataViewModel = getProInventoryDataViewModel,
            syncInventoryViewModel = syncInventoryViewModel,
            mainViewModel = mainViewModel,
            ticketRayonViewModel = ticketRayonViewModel,
            getSharedDataViewModel = getSharedDataViewModel,
            selectArtInventoryVM = selectArtInventoryVM,
            selectArtMobilityVM = selectArtMobilityVM,
            printViewModel = printViewModel,
            bluetoothVM = bluetoothVM,
            sunmiPrintManager = sunmiPrintManager,
            syncSharedViewModels = syncSharedViewModels,
            syncProcaisseViewModels = syncProcaisseViewModels,
            settingVM = settingViewModel,
            proInventoryViewModels = proInventoryViewModels,
            syncCompletionViewModel = syncCompletionViewModel
        )

        sharedNavGraph(
            navigate = {route, findStartDestination -> navController.navigate(route)
            {
                // Pop up to the start destination of the graph to
                // avoid building up a large stack of destinations
                // on the back stack as users select items

                if(findStartDestination) {
                    popUpTo(navController.graph.findStartDestination().id) {
                        saveState = true
                    }
                }
                // Avoid multiple copies of the same destination when
                // reselecting the same item
                launchSingleTop = true
                // Restore state when reselecting a previously selected item
                restoreState = true
            }
                       },
            navigatePopUpTo = { route: String, popUpTo: String, isInclusive: Boolean ->
                navController.navigate(route) {
                    popUpTo(popUpTo) {
                        inclusive = isInclusive
                    }
                }
            },
            popBackStack = { navController.popBackStack() },
            navigateUp = { navController.navigateUp() },
            dataViewModel = dataViewModel,
            getSharedDataViewModel = getSharedDataViewModel,
            networkViewModel = networkViewModel,
            navigationDrawerViewModel = navigationDrawerViewModel,
            getProCaisseDataViewModel = getProCaisseDataViewModel,
            articlesViewModel = articlesViewModel,
            articleTxtValidViewModel = articleTxtValidViewModel,
            barCodeViewModel = barCodeViewModel,
            syncSharedViewModels = syncSharedViewModels,
            getProInventoryDataViewModel = getProInventoryDataViewModel,
            syncInventoryViewModel = syncInventoryViewModel,
            mainViewModel = mainViewModel,
            ticketRayonViewModel = ticketRayonViewModel,
            printViewModel = printViewModel,
            bluetoothVM = bluetoothVM,
            sunmiPrintManager = sunmiPrintManager,
            networkErrorsVM = networkErrorsVM,
            authViewModel = authViewModel,
            settingViewModel = settingViewModel,
            syncCompletionViewModel = syncCompletionViewModel
        )
    }
}

@Composable
inline fun <reified T : ViewModel> NavBackStackEntry.sharedViewModel(navController: NavController): T {
    val navGraphRoute = destination.parent?.route ?: return hiltViewModel()
    val parentEntry =
        remember(this) {
            navController.getBackStackEntry(navGraphRoute)
        }
    return hiltViewModel(parentEntry)
}
