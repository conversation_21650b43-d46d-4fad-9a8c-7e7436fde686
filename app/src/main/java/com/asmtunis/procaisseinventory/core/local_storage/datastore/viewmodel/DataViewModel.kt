package com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.ktor.Urls.BASE_URL
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.ARTICLE_COUNT
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.AUTO_FACTURE_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.EXERCICE_KEY
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.DARK_THEME
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.FIREBASE_TOKEN
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.HAVE_CAMERA_DEVICE
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.INVENTAIRE_STATION_ORIGNE_IS_FROM_UTILISATEUR
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.INVENTORY_ACTIVATION_SENT
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.INVENTORY_ACTIVATION_STATE_KEY
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.INVENTORY_BN_ENTREE_SELECTED_MONTHS_NBR
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.IS_LOGGED_IN_KEY
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.IS_PROCAISSE_LICENSE_SELECTED
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.IS_PROINVENTORY_LICENSE_SELECTED
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.MAX_NUM_TICKET
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PINTING_PARAMS
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_ACTIVATION_SENT
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_ACTIVATION_STATE_KEY
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROINVENTORY_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.STATION
import com.asmtunis.procaisseinventory.core.print.PrintingData
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.validateBaseUrl
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json
import javax.inject.Inject

@HiltViewModel
class DataViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val repository: DataStoreRepository,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb
) : ViewModel() {
    var selectedBaseConfig: BaseConfig by mutableStateOf(BaseConfig())
        private set

    var printData: PrintingData by mutableStateOf(PrintingData())
        private set
    init {
            getPrintingParams()
            getCurrentBaseConfig()
    }

    fun saveInventoryBnEntreSelectedMonthsNbr(value: String) {
        viewModelScope.launch(dispatcher) {
            repository.putString(INVENTORY_BN_ENTREE_SELECTED_MONTHS_NBR, value)
        }
    }

    fun getInventoryBnEntreSelectedMonthsNbr(default: String = ""): String = runBlocking {
        repository.getString(INVENTORY_BN_ENTREE_SELECTED_MONTHS_NBR, default = default).first()?:""
    }



    fun saveArticleCount(value: Int) {
        viewModelScope.launch(dispatcher) {
            repository.putInt(ARTICLE_COUNT, value)
        }
    }

    fun getArticleCount(): Int = runBlocking {
        repository.getInt(ARTICLE_COUNT).first()
    }

    fun saveExercice(value: String) {
        viewModelScope.launch(dispatcher) {
            repository.putString(EXERCICE_KEY, value)
        }
    }

    fun getExercice(default: String = "2024"): String = runBlocking {
        repository.getString(EXERCICE_KEY, default = default).first() ?: default
    }

    fun saveHaveCameraDevice(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(HAVE_CAMERA_DEVICE, value)
        }
    }

    fun getHaveCameraDevice(): Boolean = runBlocking {
        repository.getBoolean(HAVE_CAMERA_DEVICE).first()
    }




    fun saveFirebaseToken(value: String) {
        viewModelScope.launch(dispatcher) {
            repository.putString(FIREBASE_TOKEN, value)
        }
    }

    fun getFirebaseToken(): String = runBlocking {
        repository.getString(FIREBASE_TOKEN).first()?:""
    }


    fun saveSelectedBaseConfig(value: String) {
        viewModelScope.launch(dispatcher) {
            repository.putString(SELECTED_BASE_CONFIG, value)
        }
    }

    fun getSelectedBaseConfigr(): String = runBlocking {
        repository.getString(SELECTED_BASE_CONFIG).first()?:""
    }


    fun saveIsProcaisseLicenseSelected(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(IS_PROCAISSE_LICENSE_SELECTED, value)
        }
    }

    fun getIsProcaisseLicenseSelected(): Boolean = runBlocking {
        repository.getBoolean(IS_PROCAISSE_LICENSE_SELECTED).first()
    }


    fun saveIsProInventoryLicenseSelected(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(IS_PROINVENTORY_LICENSE_SELECTED, value)
        }
    }

    fun getIsProInventoryLicenseSelected(): Boolean = runBlocking {
        repository.getBoolean(IS_PROINVENTORY_LICENSE_SELECTED).first()
    }







    fun saveStation(value: String) {
        viewModelScope.launch(dispatcher) {
            repository.putString(STATION, value)
        }
    }

    fun getStation(): String = runBlocking {
        repository.getString(STATION).first()?:""
    }

    fun saveIsLoggedIn(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(IS_LOGGED_IN_KEY, value)
        }
    }

    fun getIsLoggedIn(): Boolean = runBlocking {
        repository.getBoolean(IS_LOGGED_IN_KEY).first()
    }



    fun saveProcaisseActivationState(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(PROCAISSE_ACTIVATION_STATE_KEY, value)
        }
    }

    fun getProcaisseActivationState(): Boolean = runBlocking {
        repository.getBoolean(PROCAISSE_ACTIVATION_STATE_KEY).first()
    }

    fun saveInventoryActivationState(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(INVENTORY_ACTIVATION_STATE_KEY, value)
        }
    }

    fun getInventoryActivationState(): Boolean = runBlocking {
        repository.getBoolean(INVENTORY_ACTIVATION_STATE_KEY).first()
    }

    fun saveProcaisseSubscribtionSent(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(PROCAISSE_ACTIVATION_SENT, value)
        }
    }

    fun getProcaisseSubscribtionSent(): Boolean = runBlocking {
        repository.getBoolean(PROCAISSE_ACTIVATION_SENT).first()
    }

    fun saveProInventorySubscribtionSent(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(INVENTORY_ACTIVATION_SENT, value)
        }
    }

    fun getProInventorySubscribtionSent(): Boolean = runBlocking {
        repository.getBoolean(INVENTORY_ACTIVATION_SENT).first()
    }

    fun getInventaireStationOrigineIsFromUtilisateur(): Boolean = runBlocking {
        repository.getBoolean(INVENTAIRE_STATION_ORIGNE_IS_FROM_UTILISATEUR).first()
    }

    fun saveInventaireStationOrigineIsFromUtilisateu(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(INVENTAIRE_STATION_ORIGNE_IS_FROM_UTILISATEUR, value)
        }
    }


    fun getDarkTheme(): String = runBlocking {
        repository.getString(DARK_THEME).first()?:""
    }

    fun saveDarkTheme(value: String) {
        viewModelScope.launch(dispatcher) {
            repository.putString(DARK_THEME, value)
        }
    }


    fun getMaxNumTicket(): Int = runBlocking {
        repository.getInt(MAX_NUM_TICKET).first()
    }

    fun saveMaxNumTicket(value: Int) {
        viewModelScope.launch(dispatcher) {
            repository.putInt(MAX_NUM_TICKET, value)
        }
    }


    fun getAutoFactureAuthorization(): Boolean = runBlocking {
        repository.getBoolean(key = AUTO_FACTURE_AUTHORISATION).first()
    }

    fun saveAutoFactureAuthorization(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(AUTO_FACTURE_AUTHORISATION, value)
        }
    }

    fun getProcaisseAutoSyncAuthorization(): Boolean = runBlocking {
        repository.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).first()
    }

    fun saveProcaisseAutoSyncAuthorization(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(PROCAISSE_AUTO_SYNC_AUTHORISATION, value)
        }
    }



    fun getProInventroryAutoSyncAuthorization(): Boolean = runBlocking {
        repository.getBoolean(PROINVENTORY_AUTO_SYNC_AUTHORISATION, default = true).first()
    }

    fun saveProInventroryAutoSyncAuthorization(value: Boolean) {
        viewModelScope.launch(dispatcher) {
            repository.putBoolean(PROINVENTORY_AUTO_SYNC_AUTHORISATION, value)
        }
    }



    fun getPrintParamsx(): String = runBlocking {
        repository.getString(key = PINTING_PARAMS, default = "").first()?:""
    }

    fun savePrintParams(value: String) {
        viewModelScope.launch(dispatcher) {
            repository.putString(PINTING_PARAMS, value)
        }
    }


    private fun getCurrentBaseConfig() {
        viewModelScope.launch {
            repository.getString(SELECTED_BASE_CONFIG).collect {
                selectedBaseConfig = if(!it.isNullOrEmpty()) {

                    Json.decodeFromString(it)?: BaseConfig()
                }
                else BaseConfig()

                BASE_URL = String.format(validateBaseUrl(selectedBaseConfig), selectedBaseConfig.adresse_ip, selectedBaseConfig.port)

                // Debug logging for BASE_URL
                android.util.Log.d("BASE_URL_DEBUG", "Constructed BASE_URL: $BASE_URL")
                android.util.Log.d("BASE_URL_DEBUG", "IP: ${selectedBaseConfig.adresse_ip}, Port: ${selectedBaseConfig.port}")

            }

        }
    }



    private fun getPrintingParams() {
        viewModelScope.launch {
            repository.getString(key = PINTING_PARAMS, default = "").collect {
                printData = if(!it.isNullOrEmpty())
                    Json.decodeFromString(it)?: PrintingData()
                else PrintingData()
            }

        }
    }
    fun resetDataStore() {
        saveInventoryBnEntreSelectedMonthsNbr(Globals.THIS_MONTH)
        saveIsLoggedIn(false)
        viewModelScope.launch(dispatcher) {

            repository.putInt(MAX_NUM_TICKET, 0)
            repository.putBoolean(INVENTORY_ACTIVATION_SENT, false)
            repository.putBoolean(PROCAISSE_ACTIVATION_SENT, false)
            repository.putBoolean(AUTO_FACTURE_AUTHORISATION, false)

            repository.putInt(ARTICLE_COUNT, -0)


           // proCaisseLocalDb.utilisateur.deleteAll()  delete user after reset firebase token


            proCaisseLocalDb.networkErrors.deleteAll()
            proCaisseLocalDb.articlesBarCode.deleteAll()
            proCaisseLocalDb.facture.deleteAll()
            proCaisseLocalDb.etatOrdreMission.deleteAll()
            proCaisseLocalDb.typeMouvement.deleteAll()
            proInventoryLocalDb.unite.deleteAll()


            proCaisseLocalDb.immobilisation.deleteAll()
            proCaisseLocalDb.inventairePieceJoint.deleteAll()


            proCaisseLocalDb.imageVC.deleteAll()
            proCaisseLocalDb.newProductVC.deleteAll()
            proCaisseLocalDb.autreVC.deleteAll()
            proCaisseLocalDb.listConcurrentVC.deleteAll()
            proCaisseLocalDb.newProductVC.deleteAll()
            proCaisseLocalDb.prixVC.deleteAll()
            proCaisseLocalDb.promoVC.deleteAll()
            proCaisseLocalDb.typeCommunicationVC.deleteAll()

            proCaisseLocalDb.devise.deleteAll()
            proCaisseLocalDb.chequeCaisse.deleteAll()
            proCaisseLocalDb.banque.deleteAll()
            proCaisseLocalDb.carteResto.deleteAll()

            proCaisseLocalDb.clients.deleteAll()
            proCaisseLocalDb.articles.deleteAll()
            proCaisseLocalDb.uniteArticles.deleteAll()
            proCaisseLocalDb.etablisement.deleteAll()
            proCaisseLocalDb.sessionCaisse.deleteAll()
            proCaisseLocalDb.statistiques.deleteAll()
            proCaisseLocalDb.prefix.deleteAll()
            proCaisseLocalDb.exercice.deleteAll()

            proCaisseLocalDb.authorization.deleteAll()
            proCaisseLocalDb.reglementCaisse.deleteAll()

            proCaisseLocalDb.ticketResto.deleteAll()



            proCaisseLocalDb.bonLivraison.deleteAll()
            proCaisseLocalDb.ligneBonLivraison.deleteAll()

            proCaisseLocalDb.bonCommande.deleteAll()
            proCaisseLocalDb.ligneBonCommande.deleteAll()


            proCaisseLocalDb.bonRetour.deleteAll()
            proCaisseLocalDb.ligneBonRetour.deleteAll()

            proCaisseLocalDb.superficieDn.deleteAll()
            proCaisseLocalDb.familleDn.deleteAll()
            proCaisseLocalDb.typePointVenteDn.deleteAll()
            proCaisseLocalDb.typeServicesDn.deleteAll()

            proCaisseLocalDb.visitesDn.deleteLigneVisiteAll()
            proCaisseLocalDb.visitesDn.deleteVisiteAll()

            proCaisseLocalDb.pricePerStation.deleteAll()
            proCaisseLocalDb.timbre.deleteAll()
            proCaisseLocalDb.ville.deleteAll()



            proInventoryLocalDb.typePrix.deleteAll()
            proInventoryLocalDb.stationsArticle.deleteAll()
            proInventoryLocalDb.stations.deleteAll()
            proInventoryLocalDb.tva.deleteAll()
            proInventoryLocalDb.famille.deleteAll()
            proInventoryLocalDb.marque.deleteAll()
            proInventoryLocalDb.fournisseur.deleteAll()

            proInventoryLocalDb.bonLivraison.deleteAll()
            proInventoryLocalDb.ligneBonLivraison.deleteAll()
            proInventoryLocalDb.bonEntree.deleteAll()
            proInventoryLocalDb.ligneBonEntree.deleteAll()
            proInventoryLocalDb.inventaire.deleteAll()
            proInventoryLocalDb.ligneInventaire.deleteAll()

            proCaisseLocalDb.deplacementOutByUser.deleteAll()

            proInventoryLocalDb.ticketRayon.deleteAll()


        }
    }
}
