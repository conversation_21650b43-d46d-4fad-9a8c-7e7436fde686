package com.asmtunis.procaisseinventory.core.sync_workmanager

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Gestionnaire pour déclencher la synchronisation instantanée lors de changements de données.
 * Implémente un mécanisme de debouncing pour éviter les synchronisations trop fréquentes.
 */
@Singleton
class InstantSyncTrigger @Inject constructor(
    private val syncService: SyncService,
    @SyncScope private val coroutineScope: CoroutineScope
) {
    
    private var syncJob: Job? = null
    private var pendingSyncCount = 0
    
    companion object {
        private const val DEBOUNCE_DELAY_MS = 2000L // 2 secondes de délai pour grouper les changements
        private const val MAX_PENDING_CHANGES = 5 // Nombre maximum de changements avant sync forcée
    }
    
    /**
     * Déclenche une synchronisation instantanée avec debouncing
     * @param source Source du déclenchement (ex: "BonLivraison", "Ticket", etc.)
     * @param changeType Type de changement (ex: "INSERT", "UPDATE", "DELETE")
     */
    fun triggerInstantSync(source: String, changeType: String = "CHANGE") {
        pendingSyncCount++
        
        
        // Annuler le job précédent s'il existe
        syncJob?.cancel()
        
        // Si trop de changements en attente, synchroniser immédiatement
        if (pendingSyncCount >= MAX_PENDING_CHANGES) {
            executeSyncNow(source)
            return
        }
        
        // Sinon, attendre le délai de debouncing
        syncJob = coroutineScope.launch {
            try {
                delay(DEBOUNCE_DELAY_MS)
                executeSyncNow(source)
            } catch (e: Exception) {
            }
        }
    }
    
    /**
     * Force une synchronisation immédiate sans debouncing
     * @param source Source du déclenchement
     */
    fun forceSyncNow(source: String) {
        syncJob?.cancel()
        executeSyncNow(source)
    }
    
    /**
     * Exécute la synchronisation maintenant
     */
    private fun executeSyncNow(source: String) {
        val totalChanges = pendingSyncCount
        pendingSyncCount = 0

        coroutineScope.launch {
            try {
                syncService.attemptInstantSync("InstantSyncTrigger-$source")
            } catch (e: Exception) {
            }
        }
    }
    
    /**
     * Annule toutes les synchronisations en attente
     */
    fun cancelPendingSync() {
        syncJob?.cancel()
        pendingSyncCount = 0
    }
    
    /**
     * Retourne le nombre de changements en attente de synchronisation
     */
    fun getPendingChangesCount(): Int = pendingSyncCount
}
