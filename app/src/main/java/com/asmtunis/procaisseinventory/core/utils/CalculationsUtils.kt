package com.asmtunis.procaisseinventory.core.utils

import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.roundToThreeDecimals
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva


object CalculationsUtils {
    fun timbersValueSum(listActifTimber: List<Timbre>): Double =
        roundToThreeDecimals(listActifTimber.sumOf { if(it.tIMBEtat == "Actif") stringToDouble(it.tIMBValue) else 0.0 })
    fun totalPriceArticle(
        price: String,
        quantity: String
    ): String {
        val result = stringToDouble(price) * (if(quantity.isNotEmpty()) stringToDouble(quantity) else 1.0)
        return roundToThreeDecimals(result).toString()
    }

    fun totalPriceWithoutDiscountTTC(
        listArt : List<SelectedArticle>,
        withTva: Boolean = false
    ) : Double{
        var result = 0.0
        for (article in listArt) {
            result+= if(withTva){
                if(article.tva != Tva())
                stringToDouble(article.lTMtTTC)

                else
                    stringToDouble(
                        totalPriceArticle(
                        price = article.lTPuHT,
                        quantity = article.quantity
                    )
                    )
            }
            else
                stringToDouble(
                    totalPriceArticle(
                price = /* if(withTva) article.lTMtHT else  */article.prixVente,
                quantity = article.quantity
            )
                )
        }

        return result
    }

    fun totalPriceCaissseTTC(
        listArt : List<SelectedArticle>,
        withTva: Boolean = false
    ) : Double{
        var result = 0.0
        for (article in listArt) {

            result +=

                if(withTva){
                    if(article.tva != Tva())
                        stringToDouble(article.lTMtTTC)

                    else
                        stringToDouble(
                            totalPriceArticle(
                            price = article.lTPuHT,
                            quantity = article.quantity
                        )
                        )
                }


                else stringToDouble(
                    totalPriceArticle(
                price = article.prixCaisse,
                quantity = article.quantity
            )
                )

        }

        return result
    }

    fun totalPriceHT(
        listArt : List<SelectedArticle>
    ) : Double {
        var result = 0.0
        for (article in listArt) {
            //  result+= calculateAmountHT(amount = StringFormatting.stringToDouble(article.article.aRTPrixUnitaireHT), tva = article.article.aRTTVA)
            result+= stringToDouble(article.lTMtBrutHT)  //* stringToDouble(article.quantity)
        }

        return roundToThreeDecimals(result)
    }

    fun totalPriceTTC(listArt : List<SelectedArticle>) : Double =
        roundToThreeDecimals(listArt.sumOf { stringToDouble(it.lTMtTTC) })



fun calculateAfterDiscount(amount: Double, discount: Double): Double {
return roundToThreeDecimals(amount - (amount* (discount/100)))
}

    fun calculateDiscountAmount(amount: Double, discount: Double): Double {
        return roundToThreeDecimals(amount * (discount/100))
    }




    fun calculateAmountTTC(price: Double, quantity: Double): Double {
        return roundToThreeDecimals(price * quantity)
    }

    fun calculateAmountTTCNet(amount: Double, discount: Double): Double {
        return roundToThreeDecimals(amount - (discount * amount) / 100.0)
    }

    fun calculateAmountTTCNet(price: Double, quantity: Double, discount: Double): Double {
        return roundToThreeDecimals(price * quantity)
    }

    fun calculateAmountHT(amount: Double, tva: Double): Double {
        return roundToThreeDecimals(amount / (1 + (tva / 100)))
    }

    fun calculateAmountHT(price: Double, quantity: Double, tva: Double): Double {
        return roundToThreeDecimals(price * quantity / (1 + tva / 100))
    }


    fun calculateAmountExcludingTax(price: Double, vat: Double): Double {
        return roundToThreeDecimals(price / (1 + vat / 100))
    }





    fun calculateAmountExcludingTax(price: Double, quantity: Double, vat: Double): Double {
        return roundToThreeDecimals(price / (1 + vat / 100) * quantity)
    }

    fun calculateAmountHTNet(amount: Double, discount: Double, tva: Double): Double {
        return roundToThreeDecimals((amount - discount * amount / 100.0) / (1 + tva / 100))
    }

    fun calculateAmountHTNet(
        price: Double,
        quantity: Double,
        discount: Double,
        tva: Double
    ): Double {
        return roundToThreeDecimals((price * quantity - discount * (price * quantity) / 100.0) / (1 + tva / 100))
    }

    fun calculateDiscountRate(price: Double, unitPrice: Double): Double = roundToThreeDecimals((1 - price / unitPrice) * 100.0)

   fun Double.returnZeroIfNegative(): Double = if(this <0) 0.0 else this

    fun Double.returnOneIfZero(): Double = if(this ==0.0) 1.0 else this



    fun calculateReceived(received: Double, chequeCaisses: Double, traiteCaisses: Double): Double {
        return roundToThreeDecimals(received + chequeCaisses + traiteCaisses)
    }

    fun calculateCheckAndCartResto(chequeCaisses: Double, traiteCaisses: Double): Double {
        return roundToThreeDecimals(chequeCaisses + traiteCaisses)
    }



    fun calculateTickets(traiteCaisses: ArrayList<TraiteCaisse>?): Double {
        var traiteCaisseAmount = 0.0
        if (traiteCaisses != null) {
            for (traiteCaisse in traiteCaisses) {
                traiteCaisseAmount += traiteCaisse.tRAITMontant
            }
        }
        return roundToThreeDecimals(traiteCaisseAmount)
    }

    /**
     * Calculate the total price for bon livraison including all components
     * This ensures consistent calculation across all screens
     *
     * @param tIKMtTTC The TTC amount from ticket
     * @param tIKMtRemise The discount amount from ticket
     * @param tIKTimbre The stamp amount from ticket
     * @param mntRevImp The tax amount (can be null)
     * @param includeDiscount Whether to include discount in total (for total before discount)
     * @param includeStamps Whether to include stamps in calculation
     * @param includeTax Whether to include tax in calculation
     * @return The calculated total price
     */
    fun calculateBonLivraisonTotal(
        tIKMtTTC: String,
        tIKMtRemise: String? = null,
        tIKTimbre: String? = null,
        mntRevImp: Double? = null,
        includeDiscount: Boolean = true,
        includeStamps: Boolean = true,
        includeTax: Boolean = true
    ): Double {
        val ttcAmount = stringToDouble(tIKMtTTC)
        val discountAmount = if (includeDiscount) stringToDouble(tIKMtRemise ?: "0") else 0.0
        val stampAmount = if (includeStamps) stringToDouble(tIKTimbre ?: "0") else 0.0
        val taxAmount = if (includeTax) (mntRevImp ?: 0.0) else 0.0

        // Total before discount = TTC + Discount + Stamps + Tax
        // Total after discount = TTC + Stamps + Tax
        return roundToThreeDecimals(ttcAmount + discountAmount + stampAmount + taxAmount)
    }

    /**
     * Calculate total from line items (alternative method for verification)
     *
     * @param ligneTicketList List of ligne tickets with articles
     * @param tIKTimbre Stamp amount
     * @param mntRevImp Tax amount
     * @param includeStamps Whether to include stamps
     * @param includeTax Whether to include tax
     * @return The calculated total from line items
     */
    fun calculateBonLivraisonTotalFromLineItems(
        ligneTicketList: List<Any>, // Can be LigneTicketWithArticle or SelectedArticle
        tIKTimbre: String? = null,
        mntRevImp: Double? = null,
        includeStamps: Boolean = true,
        includeTax: Boolean = true
    ): Double {
        val lineItemsTotal = when {
            ligneTicketList.isEmpty() -> 0.0
            ligneTicketList.first() is com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle -> {
                @Suppress("UNCHECKED_CAST")
                val items = ligneTicketList as List<com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle>
                items.sumOf {
                    stringToDouble(it.ligneTicket?.lTPrixVente ?: "0") * stringToDouble(it.ligneTicket?.lTQte ?: "0")
                }
            }
            ligneTicketList.first() is com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle -> {
                @Suppress("UNCHECKED_CAST")
                val items = ligneTicketList as List<com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle>
                items.sumOf { stringToDouble(it.lTMtTTC) }
            }
            else -> 0.0
        }

        val stampAmount = if (includeStamps) stringToDouble(tIKTimbre ?: "0") else 0.0
        val taxAmount = if (includeTax) (mntRevImp ?: 0.0) else 0.0

        return lineItemsTotal + stampAmount + taxAmount
    }


   /* fun calculateTicketAmount(ligneTickets: List<LigneTicket>, function: Int): Double {
        var result = 0.0
        when (function) {
            R.id.calculateAmountTTC -> for (ligneTicket in ligneTickets) {
                result += calculateAmountTTC(
                    ligneTicket.getArticle().getPvttc(),
                    ligneTicket.getlTQte()
                )
                //  result += calculateAmountTTC(ligneTicket.getlTMtTTC()/ligneTicket.getlTQte(), ligneTicket.getlTQte());
            }

            R.id.calculateAmountTTCNet -> for (ligneTicket in ligneTickets) {
                result += ligneTicket.getlTMtTTC()
            }

            R.id.calculateAmountHT -> for (ligneTicket in ligneTickets) {
                // result += calculateAmountHT(ligneTicket.getlTMtTTC(), ligneTicket.getlTTVA());
                result += ligneTicket.getlTMtHT()
            }

            R.id.calculateAmountHTNet -> for (ligneTicket in ligneTickets) {
                result += calculateAmountHTNet(
                    ligneTicket.getlTMtTTC(),
                    ligneTicket.getlTTauxRemise(),
                    ligneTicket.getlTTVA()
                )
            }

            R.id.calculateAmountVAT -> for (ligneTicket in ligneTickets) {
                result += ligneTicket.getlTMtTTC() - ligneTicket.getlTMtHT()
            }
        }
        return Utils.round(decimalFormat(result), 3)
    }*/
}