package com.asmtunis.procaisseinventory.core.local_storage.datastore.utils

const val IS_LOGGED_IN_KEY = "IS_LOGGED_IN_KEY"
const val IS_ALL_DATA_SAVED_KEY = "IS_ALL_DATA_SAVED"
const val SELECTED_BASE_CONFIG = "Seleceted_BASE_CONFIG"

const val FIREBASE_TOKEN = "Firebase_token"
const val INVENTORY_BN_ENTREE_SELECTED_MONTHS_NBR = "Inventory_BnEntre_Selected_Months_Nbr"
const val HAVE_CAMERA_DEVICE = "have_Camera_Device"
const val ARTICLE_COUNT = "article_count"
const val EXERCICE_KEY = "exercice"




const val INVENTAIRE_STATION_ORIGNE_IS_FROM_UTILISATEUR = "INVENTAIRE_STATION_ORIGNE_IS_FROM_UTILISATEUR"


const val IS_PROCAISSE_LICENSE_SELECTED = "IS_PROCAISSE_LICENSE_SELECTED"
const val IS_PROINVENTORY_LICENSE_SELECTED = "IS_PROINVENTORY_LICENSE_SELECTED"


const val USER_ID_KEY = "USER_ID_KEY"


const val STATION = "Station"


const val PROCAISSE_ACTIVATION_STATE_KEY = "PROCAISSE_ACTIOVATION_STATE"
const val INVENTORY_ACTIVATION_STATE_KEY = "INVENTORY_ACTIVATION_STATE_KEY"

const val PROCAISSE_ACTIVATION_SENT = "PROCAISSE_ACTIVATION_SENT"
const val INVENTORY_ACTIVATION_SENT = "INVENTORY_ACTIVATION_SENT"

//const val PROCAISSE_EXPIRATION_DATE_KEY = "PROCAISSE_expirationDate"


const val DARK_THEME = "darkTheme"
const val MAX_NUM_TICKET = "MaxNumTicket"


const val AUTO_FACTURE_AUTHORISATION = "Auto_facture_auth"
const val PROCAISSE_AUTO_SYNC_AUTHORISATION = "Procaisse_Auto_Sync_auth"

const val PROINVENTORY_AUTO_SYNC_AUTHORISATION = "ProInventory_Auto_Sync_auth"


// print

const val PINTING_PARAMS = "Print_params"

