package com.asmtunis.procaisseinventory.setting

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.DARK_THEME
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.INVENTAIRE_STATION_ORIGNE_IS_FROM_UTILISATEUR
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PINTING_PARAMS
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROINVENTORY_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.print.PrintingData
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.nav_components.data.model.UiWindowState
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.shared_ui_components.theme.ThemeValues
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import javax.inject.Inject


@HiltViewModel
    class SettingViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    savedStateHandle: SavedStateHandle,
    ) : ViewModel() {
      init {

           viewModelScope.launch(mainDispatcher) {
           proCaisseLocalDb.dataStore.getString(DARK_THEME, default = ThemeValues.SYSTEM_THEME.theme).collect { theme->
                   setThemeMode(theme?: ThemeValues.SYSTEM_THEME.theme)
               }




               isProcaisseAutoSync = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).first()
               isProInventoryAutoSync = proCaisseLocalDb.dataStore.getBoolean(key = PROINVENTORY_AUTO_SYNC_AUTHORISATION, default = true).first()

               val printParams = proCaisseLocalDb.dataStore.getString(key = PINTING_PARAMS, default = "").first()?: ""
               printingData =  if(printParams!="")
                   Json.decodeFromString(printParams)
               else PrintingData()



                invStationOrigineIsFromUtil = proCaisseLocalDb.dataStore.getBoolean(key = INVENTAIRE_STATION_ORIGNE_IS_FROM_UTILISATEUR, default = false).first()


           }



       }

    var showAuthorisationList: Boolean by mutableStateOf(false)
        private set
    fun onShowAuthorisationListChange(value : Boolean){
        showAuthorisationList = value
    }


    var isProInventoryAutoSync: Boolean by mutableStateOf(true)
        private set
    fun setProInventorySyncMode(value : Boolean){
        viewModelScope.launch {
            isProInventoryAutoSync = value
            proCaisseLocalDb.dataStore.putBoolean(PROINVENTORY_AUTO_SYNC_AUTHORISATION, value)
        }
    }

        var selectedTheme: String by mutableStateOf(ThemeValues.SYSTEM_THEME.theme)
            private set
        fun setThemeMode(value : String){
            selectedTheme = value

            viewModelScope.launch {

                proCaisseLocalDb.dataStore.putString(DARK_THEME,value)
            }

        }





    var uiWindowState by  mutableStateOf(UiWindowState())
    // var switchDrawer by mutableStateOf(false)

    fun onUiWindowStateChange(value: UiWindowState) {
        uiWindowState = value
    }

    @OptIn(SavedStateHandleSaveableApi::class)
    var isDarkTheme by savedStateHandle.saveable { mutableStateOf(false) }
    // var switchDrawer by mutableStateOf(false)

    fun onIsDarkThemeChange(value: Boolean) {
        isDarkTheme = value
    }

        var isProcaisseAutoSync: Boolean by mutableStateOf(true)
            private set
        fun setProCaisseSyncMode(value : Boolean){
            viewModelScope.launch {
                isProcaisseAutoSync = value
                proCaisseLocalDb.dataStore.putBoolean(PROCAISSE_AUTO_SYNC_AUTHORISATION, value)
            }
        }









        var printingData: PrintingData by mutableStateOf(PrintingData())
            private set
        fun setPrintParams(value : PrintingData){
            viewModelScope.launch {
                printingData = value
                proCaisseLocalDb.dataStore.putString(PINTING_PARAMS, Json.encodeToString(value))


            }
        }




    var invStationOrigineIsFromUtil by mutableStateOf(false)
        private set
    fun setInventaireStationOrigineIsFromUtilisateur(value : Boolean){
        viewModelScope.launch {
            invStationOrigineIsFromUtil = value
            proCaisseLocalDb.dataStore.putBoolean(INVENTAIRE_STATION_ORIGNE_IS_FROM_UTILISATEUR, value)
        }
    }

    var enableLazyLoading by mutableStateOf(true)
        private set
    fun setLazyLoadingEnabled(value : Boolean){
        viewModelScope.launch {
            enableLazyLoading = value
            proCaisseLocalDb.dataStore.putBoolean("ENABLE_LAZY_LOADING", value)
        }
    }
















}
