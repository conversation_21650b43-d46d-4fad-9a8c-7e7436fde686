package com.asmtunis.procaisseinventory.nav_components.nav_rail

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material3.Badge
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationRail
import androidx.compose.material3.NavigationRailItem
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.BuildConfig
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.CustomIcons
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationContentPosition
import com.asmtunis.procaisseinventory.nav_components.ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.data.model.MenuModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel

@Composable
fun ReplyNavigationRail(
    mainViewModel: MainViewModel,
    navDrawerVM: NavigationDrawerViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    navigationContentPosition: ReplyNavigationContentPosition,
    menus: List<MenuModel>,
    onMenuClick: (menu: MenuModel) -> Unit,
    onDrawerClicked: () -> Unit = {},
) {

val selectedItemIndex = if (menus.first().id == ID.INVENTORY_HOME_ID)
    menus.indexOf(navDrawerVM.proInventorySelectedMenu)
else menus.indexOf(navDrawerVM.proCaisseSelectedMenu)

    val utilisateur = mainViewModel.utilisateur
    NavigationRail(
        modifier = Modifier.fillMaxHeight(),
        containerColor = MaterialTheme.colorScheme.inverseOnSurface,
        header = {
            Column(
               // modifier = Modifier.layoutId(LayoutType.HEADER),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                NavigationRailItem(
                    selected = false,
                    onClick = {
                        onDrawerClicked()
                    },
                    icon = {
                        CustomIcons.BusinessIcon(
                            iconRes = CustomIcons.Menu,
                            contentDescription = "Menu",
                            size = 24.dp
                        )
                    }
                )
                /* FloatingActionButton(
                     onClick = { /*TODO*/ },
                     modifier = Modifier.padding(top = 8.dp, bottom = 32.dp),
                     containerColor = MaterialTheme.colorScheme.tertiaryContainer,
                     contentColor = MaterialTheme.colorScheme.onTertiaryContainer
                 ) {
                     Icon(
                         imageVector = Icons.Default.Edit,
                         contentDescription = "",
                         modifier = Modifier.size(18.dp)
                     )
                 }*/
                //  Spacer(Modifier.height(8.dp)) // NavigationRailHeaderPadding
               // Spacer(Modifier.height(4.dp)) // NavigationRailVerticalPadding
            }
        },
        content = {
            Column(
             //   modifier = Modifier.layoutId(LayoutType.CONTENT),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                LazyColumn(
                    state = rememberLazyListState(initialFirstVisibleItemIndex = selectedItemIndex),
                    modifier = Modifier.wrapContentSize(),
                    ) {
                    items(
                        menus.size,
                        key = {
                            menus[it].id
                        }
                    ) { index ->


                        if (navDrawerVM.addMenu(
                                menus = menus,
                                index = index,
                                clientList = mainViewModel.clientList,
                                authorizationList = utilisateur.autorisationUser.filter { it.AutEtat == "1" }
                            )
                        ) {
                            NavigationRailItem(
                                selected = menus[index] == if (menus.first().id == ID.INVENTORY_HOME_ID)
                                    navDrawerVM.proInventorySelectedMenu
                                else navDrawerVM.proCaisseSelectedMenu,
                                onClick = {
                                    onMenuClick(menus[index])

                                },
                                icon = {
                                    if(menus[index].badge.isEmpty()) {
                                        IconButton(onClick = { onMenuClick(menus[index]) }) {
                                            CustomIcons.AdaptiveIcon(
                                                icon = menus[index].icon,
                                                contentDescription = stringResource(id = menus[index].contentDescription)
                                            )
                                        }
                                    }
                                    else {
                                        Box(modifier = Modifier.padding(12.dp)) {
                                            IconButton(onClick = { onMenuClick(menus[index]) }) {
                                                CustomIcons.AdaptiveIcon(
                                                    icon = menus[index].icon,
                                                    contentDescription = stringResource(id = menus[index].contentDescription)
                                                )
                                            }
                                            Badge(
                                                modifier = Modifier
                                                    .border(1.dp, color = MaterialTheme.colorScheme.error, shape = CircleShape)
                                                    .align(Alignment.TopEnd)
                                                    .clip(CircleShape)
                                            ) {
                                                if (navDrawerVM.isLoading(
                                                        getProCaisseDataViewModel = getProCaisseDataViewModel,
                                                        getProInventoryDataViewModel = getProInventoryDataViewModel,
                                                        getSharedDataViewModel = getSharedDataViewModel,
                                                        menus = menus,
                                                        index = index
                                                    )
                                                )
                                                    LottieAnim(lotti = R.raw.loading, size = 25.dp)
                                                else if (navDrawerVM.errorGetData(
                                                        getProCaisseDataViewModel = getProCaisseDataViewModel,
                                                        getProInventoryDataViewModel = getProInventoryDataViewModel,
                                                        getSharedDataViewModel = getSharedDataViewModel,
                                                        menus = menus,
                                                        index = index
                                                    ) == ""
                                                )
                                                    Text(text = menus[index].badge)
                                                else if (BuildConfig.DEBUG && navDrawerVM.errorGetData(
                                                        getProCaisseDataViewModel = getProCaisseDataViewModel,
                                                        getProInventoryDataViewModel = getProInventoryDataViewModel,
                                                        getSharedDataViewModel = getSharedDataViewModel,
                                                        menus = menus,
                                                        index = index
                                                    ) != ""
                                                ) LottieAnim(lotti = R.raw.network_error, size = 25.dp)

                                            }
                                        }
                                    }

                                },
                                label = {
                                    Text(text = stringResource(id = menus[index].titleShort))
                                },
                                modifier = Modifier.padding(6.dp),
                                /*badge = {



                                }*/
                            )
                        }
                    }
                }



            }
        }
    )
}


enum class LayoutType {
    HEADER, CONTENT
}