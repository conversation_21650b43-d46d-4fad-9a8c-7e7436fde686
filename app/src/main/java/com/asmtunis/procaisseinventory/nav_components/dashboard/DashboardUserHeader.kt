package com.asmtunis.procaisseinventory.nav_components.dashboard

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.SubcomposeAsyncImage
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim

@Composable
fun DashboardUserHeader(
    utilisateur: Utilisateur,
    stationList: List<Station>,
    logo: Bitmap?,
    exerciceCode: String,
    onLogoutClick: () -> Unit,
    onSyncClick: () -> Unit,
    isConnected: Boolean = true,
    isSyncing: Boolean = false,
    modifier: Modifier = Modifier
) {
    val station = stationList.find { it.sTATCode == utilisateur.Station }

    // Match the drawer header style exactly with proper status bar spacing
    Row(
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .padding(
                top = 16.dp, // Add top spacing for status bar clearance
                end = 9.dp,
                start = 9.dp,
                bottom = 8.dp // Add bottom spacing for better separation
            )
            .clip(RoundedCornerShape(9.dp))
            .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.4f))
            .fillMaxWidth()
    ) {
        // User avatar/logo - exactly like drawer header
        SubcomposeAsyncImage(
            model = logo,
            contentDescription = "logo",
            modifier = Modifier
                .padding(start = 3.dp, bottom = 3.dp, top = 3.dp)
                .clip(CircleShape)
                .size(80.dp)
                .border(2.dp, Color.Black, CircleShape),
            loading = {
                LottieAnim(lotti = R.raw.loading, size = 25.dp)
            },
            error = {
                Image(
                    painter = painterResource(id = R.drawable.ic_asm),
                    contentDescription = null
                )
            }
        )

        Spacer(modifier = Modifier.width(9.dp))

        // User information - exactly like drawer header
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Spacer(modifier = Modifier.height(9.dp))

            // User name
            Text(
                text = "${utilisateur.Nom} ${utilisateur.Prenom}",
                fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                fontSize = MaterialTheme.typography.titleMedium.fontSize
            )

            Spacer(modifier = Modifier.height(6.dp))

            // Station information
            Text(
                text = if (station != null) {
                    "${station.sTATDesg} (${utilisateur.Station})"
                } else {
                    stringResource(R.string.station_value, utilisateur.Station)
                }
            )

            Spacer(modifier = Modifier.height(6.dp))

            // Exercise code (if available)
            if (exerciceCode.isNotEmpty()) {
                Text(
                    maxLines = 2,
                    text = exerciceCode
                )
            }

            Spacer(modifier = Modifier.height(9.dp))
        }

        // Action buttons - styled like drawer header
        Row(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(end = 12.dp)
        ) {
            // Sync button
            if (isSyncing) {
                LottieAnim(lotti = R.raw.loading, size = 25.dp)
            } else {
                IconButton(
                    enabled = isConnected,
                    onClick = onSyncClick
                ) {
                    com.asmtunis.procaisseinventory.core.utils.CustomIcons.BusinessIcon(
                        iconRes = com.asmtunis.procaisseinventory.core.utils.CustomIcons.Switch,
                        contentDescription = stringResource(id = R.string.sync_title),
                        size = 24.dp,
                        tint = if (isConnected) null else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                    )
                }
            }

            // Logout button
            IconButton(
                enabled = isConnected,
                onClick = onLogoutClick
            ) {
                com.asmtunis.procaisseinventory.core.utils.CustomIcons.BusinessIcon(
                    iconRes = com.asmtunis.procaisseinventory.core.utils.CustomIcons.UserLogout,
                    contentDescription = stringResource(id = R.string.cd_favorite_button),
                    size = 24.dp
                )
            }
        }
    }
}
