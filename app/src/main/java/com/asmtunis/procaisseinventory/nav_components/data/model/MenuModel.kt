package com.asmtunis.procaisseinventory.nav_components.data.model

import androidx.annotation.StringRes
import androidx.compose.ui.graphics.vector.ImageVector

data class MenuModel(
    val id: String,
    val route: Any,
    @StringRes var title: Int,
    @StringRes var titleShort: Int,
    @StringRes val contentDescription: Int,
    val icon: Any, // Changed from ImageVector to Any to support both Material Icons and drawable resources
    var badge: String = "",
    var authorizationID: String = ""
)
