package com.asmtunis.procaisseinventory.nav_components.dashboard

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.Sync
import androidx.compose.material.icons.twotone.Update
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.ProCaisseSyncRoute
import com.asmtunis.procaisseinventory.core.navigation.ProInventorySyncRoute
import com.asmtunis.procaisseinventory.core.utils.Sync.getProCaisseTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.getSharedTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.DashboardScreenViewModel
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.screen.PatrimoineDashBoard
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.screen.VendorDashBoard
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.rememberToasterState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DashboardAnalyticsScreen(
    navigate: (route: Any) -> Unit,
    onBackClick: () -> Unit,
    mainViewModel: MainViewModel,
    navDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    dashboardScreenVM: DashboardScreenViewModel,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncSharedViewModels: SyncSharedViewModels,
    networkErrorsVM: NetworkErrorsViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val utilisateur: Utilisateur = mainViewModel.utilisateur
    val exerciceCode = dataViewModel.getExercice()
    val toaster = rememberToasterState()
    val scope = rememberCoroutineScope()
    val isConnected = networkViewModel.isConnected
    val selectedBaseconfig = dataViewModel.selectedBaseConfig

    // Check user authorizations
    val haveBLAuthorisation = remember(utilisateur.autorisationUser) {
        utilisateur.autorisationUser.any {
            it.AutoCodeAu == AuthorizationValuesProCaisse.BL &&
            it.AutEtat == "1"
        }
    }

    val haveClotureSessionAutoAuthorisation = remember(utilisateur.autorisationUser) {
        utilisateur.autorisationUser.any {
            it.AutoCodeAu == AuthorizationValuesProCaisse.CLOT_SESSION_AUTO &&
            it.AutEtat == "1"
        }
    }

    // Loading states
    val isLoadingProCaisseData = UpdateLoadingStateUtils.isLoadingProCaisseData(getProCaisseDataViewModel = getProCaisseDataViewModel)
    val isLoadingSharedData = UpdateLoadingStateUtils.isLoadingSharedData(getSharedDataViewModel = getSharedDataViewModel)
    val isLoadingCommenSharedData = UpdateLoadingStateUtils.isLoadingCommenSharedData(getSharedDataViewModel = getSharedDataViewModel)

    // Sync count
    val noSyncCount = getProCaisseTotalNoSyncCount(syncProcaisseViewModels = syncProcaisseViewModels) +
                     getSharedTotalNoSyncCount(syncSharedViewModels = syncSharedViewModels)

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(R.string.dashboard_title),
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Retour"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = MaterialTheme.colorScheme.onSurface
                )
            )
        },
        floatingActionButton = {
            Column(
                verticalArrangement = Arrangement.Bottom,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // Update/Sync data button
                if (isConnected) {
                    FloatingActionButton(
                        onClick = {
                            scope.launch {
                                if (isLoadingProCaisseData || isLoadingSharedData || isLoadingCommenSharedData) {
                                    return@launch
                                }
                                networkErrorsVM.deleteNetworkErrorsList()

                                if (navDrawerViewModel.isProInventory) {
                                    getProInventoryDataViewModel.getProInventoryData(
                                        baseConfig = selectedBaseconfig,
                                        utilisateur = utilisateur
                                    )
                                } else {
                                    getProCaisseDataViewModel.getProcaisseData(
                                        baseConfig = selectedBaseconfig,
                                        utilisateur = utilisateur,
                                        exerciceCode = exerciceCode
                                    )
                                }

                                getSharedDataViewModel.getSharedData(
                                    baseConfig = selectedBaseconfig,
                                    utilisateur = utilisateur,
                                )

                                getSharedDataViewModel.getCommenSharedData(baseConfig = selectedBaseconfig)
                            }
                        },
                    ) {
                        if (isLoadingProCaisseData || isLoadingSharedData || isLoadingCommenSharedData) {
                            LottieAnim(lotti = R.raw.loading, size = 25.dp)
                        } else {
                            Icon(
                                imageVector = Icons.TwoTone.Update,
                                contentDescription = stringResource(id = R.string.update_db_title),
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // Sync button with badge
                if (noSyncCount > 0) {
                    ExtendedFloatingActionButton(
                        onClick = {
                            if (navDrawerViewModel.isProInventory) {
                                navigate(ProInventorySyncRoute)
                            } else {
                                navigate(ProCaisseSyncRoute)
                            }
                        },
                    ) {
                        Text(text = stringResource(id = R.string.sync_title))
                        Spacer(modifier = Modifier.width(9.dp))
                        BadgedBox(badge = { Badge { Text(noSyncCount.toString()) } }) {
                            Icon(
                                imageVector = Icons.TwoTone.Sync,
                                contentDescription = stringResource(id = R.string.sync_title),
                                modifier = Modifier.size(AssistChipDefaults.IconSize),
                            )
                        }
                        Spacer(modifier = Modifier.width(9.dp))
                    }
                }
            }
        }
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM(toaster = toaster)

            Spacer(modifier = Modifier.height(16.dp))

            // Show appropriate dashboard based on user type
            if (utilisateur.typeUser != Globals.OPERATEUR_PATRIMOINE) {
                VendorDashBoard(
                    navigateTo = { navigate(it) },
                    montantTotalStck = mainViewModel.montantTotalStck,
                    dashboardScreenVM = dashboardScreenVM,
                    toaster = toaster,
                    haveBLAuthorisation = haveBLAuthorisation
                )
            } else {
                PatrimoineDashBoard(
                    navDrawerViewModel = navDrawerViewModel,
                    utilisateur = utilisateur,
                    exerciceCode = exerciceCode,
                    logo = mainViewModel.logo,
                    haveClotureSessionAutoAuthorisation = haveClotureSessionAutoAuthorisation
                )
            }
        }
    }
}
