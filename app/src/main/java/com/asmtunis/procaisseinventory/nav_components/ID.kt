package com.asmtunis.procaisseinventory.nav_components

object ID {
    const val DASHBOARD_ID = "Dashboard"
    const val NETWORK_ERRORS_ID = "Network Errors"
    const val BLUETOOTH_CONNECT_SCREEN_ID = "Bluetooth Connect Screen"
    const val STAT_GRAPH_SCREEN_ID = "stat graph screen "
    const val PAYMENTS_ID = "Payments"
    const val BON_LIVRAISON_ID = "Bons de livraison"
    const val RECAP_BON_LIVRAISON = "Recap Bon Livraison"
    const val BON_LIVRAISON_DETAIL_ID = "Bons de livraison detail"
    const val ADD_BON_LIVRAISON_ID = "Add Bons de livraison"
    const val BON_COMMANDE_ID = "Bons de commande"
    const val ADD_BON_COMMANDE_ID = "Ajout Bons de commande"
    const val BON_COMMANDE_DETAIL_ID = "Bons de commande detail"
    const val BON_RETOUR_ID = "Bons de retour"
    const val ADD_BON_RETOUR_ID = "Ajout Bons de retour"
    const val BON_RETOUR_Detail_ID = "Ligne Bons detail"
    const val INVENTAIRE_PATRIMOINE_ID = "Inventaire patrimoine"
    const val AJOUT_AFFECTATION_PATRIMOINE_ID = "Ajout Affectation Inventaire patrimoine"
    const val AJOUT_DEP_IN_PATRIMOINE_ID = "Ajout deplacement In Inventaire patrimoine"
    const val AJOUT_DEP_OUT_PATRIMOINE_ID = "Ajout deplacement Out Inventaire patrimoine"
    const val AJOUT_INVENTAIRE_PATRIMOINE_ID = "Ajout Inventaire patrimoine"
    const val INVENTAIRE_PATRIMOINE_DETAIL_ID = "Inventaire patrimoine detail"
    const val SELECT_PATRIMOINE_ID = "Select patrimoine"

    const val INVENTAIRE_BATIMENT_ID = "Inventaire batiment"
    const val AJOUT_AFFECTATION_BATIMENT_ID = "Ajout Affectation Inventaire batiment"
    const val AJOUT_DEP_IN_BATIMENT_ID = "Ajout deplacement In Inventaire batiment"
    const val AJOUT_DEP_OUT_BATIMENT_ID = "Ajout deplacement Out Inventaire batiment"
    const val AJOUT_INVENTAIRE_BATIMENT_ID = "Ajout Inventaire batiment"
    const val INVENTAIRE_BATIMENT_DETAIL_ID = "Inventaire batiment detail"
    const val SELECT_BATIMENT_ID = "Select batiment"
    const val ZONE_CONSOMATION_ID = "Zone Consomation"
    const val DEPLACEMENT_OUT_BYUSER_ID = "Deplacement Out By User"
    const val DEPLACEMENT_OUT_BYUSER_WAITING_ID = "Deplacement Out By User Waiting"
    const val DEPLACEMENT_OUT_BYUSER_DETAIL_ID = "Deplacement Out By User DETAIL"
    const val CHOOSE_IMMO_ID = "Choose Immobilisation"
    const val CHOOSE_SITE_FINANCIER_ID = "Choose Site financier"
    const val ZONE_CONSOMATION_DETAIL_ID = "Zone Consomation Detail"
    const val CLIENTS_ID = "Clients"
    const val CLIENTS_INFO_ID = "Clients Info"
    const val DISTRIBUTION_NUMERIQUE_ID = "Distribution numérique"

    const val DEPENSE_ID = "Dépense"
    const val ADD_REGLEMENT_ID = "Ajout Règlements"
    const val REGLEMENT_ID = "Règlements"
    const val PRODUIT_ID = "Produits"
    const val PATRIMOINE_ID = "Patrimoines"
    const val TOURNEE_ID = "Tournée"
    const val SETTINGS_ID = "Settings"
    const val RECAP_BON_LIVRAISON_ID = "Recap Bons de livraison"
    const val ARCHIVES_ID = "Archives"
    const val VEILLE_CONCURENTIEL_ID = "Veille concurrentielle"
    const val NEW_PRODUCT_DETAIL_VC_ID = "Nouveau Produit"
    const val AUTRE_DETAIL_VC_ID = "Autre"
    const val PROMOTION_DETAIL_VC_ID = "Promotion"
    const val PRIX_DETAIL_VC_ID = "Prix"

    const val SELECTED_ARTICLE_SCREEN_CALCUL = "SelectArticlesScreenCalcul"



    /**
     * *************
     * Route
     * *************
     */

    const val FAMILLE_PRODUIT_DETAIL_SCREEN = "Famille Produit Detail Screen"
    const val ADD_MODIF_DIST_NUM = "Ajout Modid Distribution numérique"
    const val PRO_CAISSE_SYNC = "Sync ProCaisse"
    const val PRO_CAISSE_UPDATE = "Update ProCaisse"

    // /////// I N V E N T O R Y //////////

    const val INVENTORY_HOME_ID = "InventoryHome"
    const val INVENTORY_ACHAT_ID = "InventoryAchat"
    const val INVENTORY_ACHAT_DETAIL_SCREEN_ID = "InventoryAchat_DETAIL_SCREEN"
    const val INVENTORY_ADD_ACHAT_SCREEN_ID = "Inventory_Add_Achat_SCREEN"
    const val INVENTORY_INVENTAIRE_ID = "InventoryInventaire"
    const val INVENTORY_INVENTAIRE_DETAIL_SCREEN_ID = "InventoryInventaire_DETAIL_SCREEN"
    const val INVENTORY_INVENTAIRE_ADD_SCREEN_ID = "InventoryInventaire_ADD_SCREEN"
    const val INVENTORY_BON_TRANSFERT_ID = "InventoryBonTransfert"
    const val INVENTORY_BON_TRANSFERT_detail_ID = "InventoryBonTransfert_Detail"
    const val INVENTORY_BON_TRANSFERT_add_ID = "InventoryBonTransfert_Add"
    const val INVENTORY_TICKET_RAYON_ID = "InventoryTicketRayon"
    const val INVENTORY_CONSULTATION_ARTICLE_ID = "InventoryConsultationArticle"

    const val PRO_INVENTORY_SYNC = "Sync ProInventory"

    const val UPDATE_LOCAL_DB_INVENTORY = "Update Local Db Inventory"
    const val SELECTED_INVENTORY_ARTICLE_SCREEN = "SelectInventoryArticlesScreen"


    /**
     * S H A R E D
     */
    const val ARTICLE_DETAIL_SCREEN = "ArticleDetailScreen"
    const val ADD_ARTICLE_SCREEN = "AddArticleScreen"


    const val INVENTORY = "Inventory"
}
