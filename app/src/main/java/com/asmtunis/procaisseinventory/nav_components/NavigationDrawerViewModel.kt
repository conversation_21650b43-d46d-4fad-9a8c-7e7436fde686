package com.asmtunis.procaisseinventory.nav_components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Article
import androidx.compose.material.icons.automirrored.filled.FactCheck
import androidx.compose.material.icons.automirrored.filled.ReadMore
import androidx.compose.material.icons.automirrored.filled.SendAndArchive
import androidx.compose.material.icons.filled.AccountBox
import androidx.compose.material.icons.filled.AddHomeWork
import androidx.compose.material.icons.filled.AttachMoney
import androidx.compose.material.icons.filled.Hearing
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Inventory
import androidx.compose.material.icons.filled.Map
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.icons.filled.Output
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.ProductionQuantityLimits
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material.icons.filled.VerticalDistribute
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.OPERATEUR_PATRIMOINE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.BC
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.BL
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.BR
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.DEPENSE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.PATRIMOINE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.TOURNE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.VEILLE_CONCURENTIELLE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory.ARTICLE_DRAWER_ITEM
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory.INEVENTORY_DRAWER_ITEM
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory.PURCHASE_DRAWER_ITEM
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory.TICKET_RAYON_DRAWER_ITEM
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory.TRANSFERT_DRAWER_ITEM
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.utils.CustomIcons
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.IS_PROCAISSE_LICENSE_SELECTED
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.IS_PROINVENTORY_LICENSE_SELECTED
import com.asmtunis.procaisseinventory.core.navigation.AuthGraph
import com.asmtunis.procaisseinventory.core.navigation.BonCommandeRoute
import com.asmtunis.procaisseinventory.core.navigation.BonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.BonRetourRoute
import com.asmtunis.procaisseinventory.core.navigation.ClientListRoute
import com.asmtunis.procaisseinventory.core.navigation.DashboardAnalyticsRoute
import com.asmtunis.procaisseinventory.core.navigation.DepenceRoute
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserRoute
import com.asmtunis.procaisseinventory.core.navigation.DigitalDistributionRoute
import com.asmtunis.procaisseinventory.core.navigation.HomePageRoute
import com.asmtunis.procaisseinventory.core.navigation.InventaireBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryAchatRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryBonTransfertRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryHomeRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryInventaireRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryTicketRayonRoute
import com.asmtunis.procaisseinventory.core.navigation.ProductListRoute
import com.asmtunis.procaisseinventory.core.navigation.ReglementRoute
import com.asmtunis.procaisseinventory.core.navigation.SettingRoute
import com.asmtunis.procaisseinventory.core.navigation.TourneRoute
import com.asmtunis.procaisseinventory.core.navigation.VeilleConcurentielleRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationRoute
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.nav_components.ID.BON_COMMANDE_ID
import com.asmtunis.procaisseinventory.nav_components.ID.BON_LIVRAISON_ID
import com.asmtunis.procaisseinventory.nav_components.ID.BON_RETOUR_ID
import com.asmtunis.procaisseinventory.nav_components.ID.CLIENTS_ID
import com.asmtunis.procaisseinventory.nav_components.ID.DASHBOARD_ID
import com.asmtunis.procaisseinventory.nav_components.ID.DEPENSE_ID
import com.asmtunis.procaisseinventory.nav_components.ID.DEPLACEMENT_OUT_BYUSER_ID
import com.asmtunis.procaisseinventory.nav_components.ID.DISTRIBUTION_NUMERIQUE_ID
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTAIRE_BATIMENT_ID
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTORY
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTORY_ACHAT_ID
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTORY_BON_TRANSFERT_ID
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTORY_CONSULTATION_ARTICLE_ID
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTORY_HOME_ID
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTORY_INVENTAIRE_ID
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTORY_TICKET_RAYON_ID
import com.asmtunis.procaisseinventory.nav_components.ID.PRODUIT_ID
import com.asmtunis.procaisseinventory.nav_components.ID.REGLEMENT_ID
import com.asmtunis.procaisseinventory.nav_components.ID.SETTINGS_ID
import com.asmtunis.procaisseinventory.nav_components.ID.TOURNEE_ID
import com.asmtunis.procaisseinventory.nav_components.ID.VEILLE_CONCURENTIEL_ID
import com.asmtunis.procaisseinventory.nav_components.ID.ZONE_CONSOMATION_ID
import com.asmtunis.procaisseinventory.nav_components.data.model.MenuModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

@OptIn(SavedStateHandleSaveableApi::class)
@HiltViewModel
class NavigationDrawerViewModel
    @Inject
    constructor(
        private val listenNetwork: ListenNetwork,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        private val proInventoryLocalDb: ProInventoryLocalDb,
        @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
        savedStateHandle: SavedStateHandle,
    ) : ViewModel() {




        // var sessionCaisse by savedStateHandle.saveable { mutableStateOf(SessionCaisse()) }
         var sessionCaisse by mutableStateOf(SessionCaisse())
            private set
        var sessionCaisseList by mutableStateOf(emptyList<SessionCaisse>())
            private set

        /**
         * Manually update session state for immediate UI updates
         * This is called when session is closed to ensure immediate UI response
         */
        fun updateSessionState(updatedSession: SessionCaisse) {
            println("DEBUG NAV: Updating session state immediately - ID: ${updatedSession.sCIdSCaisse}, sCClotCaisse: ${updatedSession.sCClotCaisse}")
            sessionCaisse = updatedSession
            // Also update the list if it contains this session
            sessionCaisseList = sessionCaisseList.map { session ->
                if (session.sCIdSCaisse == updatedSession.sCIdSCaisse) {
                    updatedSession
                } else {
                    session
                }
            }
            println("DEBUG NAV: Session state updated - Current session sCClotCaisse: ${sessionCaisse.sCClotCaisse}")
        }

        /**
         * Check if current session is open
         * @return true if session exists and is open (sCClotCaisse = 0)
         */
        fun isSessionOpen(): Boolean {
            return sessionCaisse.sCIdSCaisse.isNotEmpty() && sessionCaisse.sCClotCaisse == 0
        }

        /**
         * Check if current session is closed
         * @return true if session exists and is closed (sCClotCaisse = 1)
         */
        fun isSessionClosed(): Boolean {
            return sessionCaisse.sCIdSCaisse.isNotEmpty() && sessionCaisse.sCClotCaisse == 1
        }

        /**
         * Check if no session exists
         * @return true if no session is available
         */
        fun hasNoSession(): Boolean {
            return sessionCaisse.sCIdSCaisse.isEmpty()
        }

        /**
         * Check if user can perform actions (add BC/BL/BR)
         * @return true if session is open, false if closed or no session
         */
        fun canPerformActions(): Boolean {
            return isSessionOpen()
        }

         var utilisateur: Utilisateur by mutableStateOf(Utilisateur())
            private set




        var proCaisseListMenu: List<MenuModel> by mutableStateOf(proCaisseDrawerItems)
            private set

        private fun setproCaisseListMenu() {
            proCaisseListMenu = proCaisseDrawerItems
        }

        var proInventoryListMenu: List<MenuModel> by mutableStateOf(proInventoryDrawerItems)
            private set

        private fun setproInventoryListMenu() {
            proInventoryListMenu = proInventoryDrawerItems
        }

        var isProInventory by savedStateHandle.saveable { mutableStateOf(false) }
        // var switchDrawer by mutableStateOf(false)

        fun onSwitchDrawerChange(state: Boolean) {
            isProInventory = state
        }

        var showNotSyncDialogue by savedStateHandle.saveable { mutableStateOf(false) }
        // var switchDrawer by mutableStateOf(false)

        fun onShowNotSyncDialogueChange(state: Boolean) {
            showNotSyncDialogue = state
        }

        init {
            getUtilisateur()
            getSessionCaissee()
            setproInventoryListMenu()
            setproCaisseListMenu()
        }


        private fun getUtilisateur() {

            viewModelScope.launch {


                proCaisseLocalDb.utilisateur.getUser().collect { utilisat ->

                    if (utilisat == null){
                        Globals.USER_ID = ""
                        return@collect
                    }
                    Globals.USER_ID = utilisat.codeUt

                    if (utilisat.typeUser == OPERATEUR_PATRIMOINE) {
                        proCaisseListMenu.firstOrNull { it.id == PRODUIT_ID }?.title = R.string.patrimoines
                    } else {
                        proCaisseListMenu.firstOrNull { it.id == PRODUIT_ID }?.title = R.string.products_title
                    }

                    utilisateur = utilisat

                    if(utilisateur!= Utilisateur()) {
                        if (proCaisseLocalDb.dataStore.getBoolean(IS_PROINVENTORY_LICENSE_SELECTED).first()) {
                            setProInventoryBadgeDrawer()
                        }

                        if (proCaisseLocalDb.dataStore.getBoolean(IS_PROCAISSE_LICENSE_SELECTED).first()) {
                            setProCaisseBadgeDrawer()
                        }
                    }



                }
            }
        }

        var proCaisseSelectedMenu: MenuModel by mutableStateOf(proCaisseDrawerItems.first())
            private set

        var proInventorySelectedMenu: MenuModel by mutableStateOf(proInventoryDrawerItems.first())
            private set

        fun onSelectedMenuChange(value: MenuModel) {
            android.util.Log.d("NavigationDrawerVM", "📍 Menu selected: ${value.id}")

            if (value.id.contains(INVENTORY)) {
                proInventorySelectedMenu = value
            } else {
                proCaisseSelectedMenu = value
            }
        }

        var heartChecked: Boolean by mutableStateOf(HEART_CHECKED_DEFAULT)
            private set

        fun onHeartCheckedChange() {
            heartChecked = !heartChecked
        }





        private fun getSessionCaissee() {
            viewModelScope.launch {
                proCaisseLocalDb.sessionCaisse.getAll().collect {
                    if (it.isNotEmpty()) {
                        sessionCaisse = it.firstOrNull() ?: SessionCaisse()
                        sessionCaisseList = it
                    } else {
                        sessionCaisseList = emptyList()
                        sessionCaisse = SessionCaisse()
                    }
                }
            }
        }

        private fun setProCaisseBadgeDrawer() {
            viewModelScope.launch {
                // Check if user should have station filtering applied
                // Users with station restrictions should see only their station's clients
                val shouldApplyStationFilter = utilisateur.Station.isNotEmpty() &&
                    !utilisateur.autorisationUser.any {
                        it.AutoCodeAu == AuthorizationValuesProCaisse.FILTRE_CLIENT && it.AutEtat == "1"
                    }

                if (shouldApplyStationFilter) {
                    // Use station-filtered count for users with station restrictions
                    proCaisseLocalDb.clients.countByStation(utilisateur.Station).collect { it ->
                        proCaisseListMenu.first { it.id == CLIENTS_ID }.badge = it.toString()
                    }
                } else {
                    // Use total count for users with full access or no station assigned
                    proCaisseLocalDb.clients.count().collect { it ->
                        proCaisseListMenu.first { it.id == CLIENTS_ID }.badge = it.toString()
                    }
                }
            }
            viewModelScope.launch {
                proCaisseLocalDb.articles.allCount().collect { it ->
                    proCaisseListMenu.first { it.id == PRODUIT_ID }.badge = it.toString()
                }
            }
            viewModelScope.launch {
                proCaisseLocalDb.visitesDn.allCount().collect { it ->
                    proCaisseListMenu.first { it.id == DISTRIBUTION_NUMERIQUE_ID }.badge =
                        it.toString()
                }
            }

            viewModelScope.launch {
                proCaisseLocalDb.bonRetour.getAll(station = utilisateur.Station).collect { list ->
                    proCaisseListMenu.first { it.id == BON_RETOUR_ID }.badge =
                        list.size.toString()
                }
            }

            viewModelScope.launch {
              proCaisseLocalDb.bonCommande.getAllByStation(station = utilisateur.Station).collect { it ->


                 proCaisseListMenu.first { it.id == BON_COMMANDE_ID }.badge = it.size.toString()

                }
            }

            viewModelScope.launch {
                proCaisseLocalDb.bonLivraison.getAllCountByStation(station = utilisateur.Station).collect { it ->
                    proCaisseListMenu.first { it.id == BON_LIVRAISON_ID }.badge = it.toString()
                }
            }

            viewModelScope.launch {
                proCaisseLocalDb.reglementCaisse.getAll().collect { it ->
                    proCaisseListMenu.first { it.id == REGLEMENT_ID }.badge = it.size.toString()
                }
            }
        }

        private fun setProInventoryBadgeDrawer() {
            viewModelScope.launch {
                proCaisseLocalDb.articles.allCount().collect { it ->
                    proInventoryListMenu.first { it.id == INVENTORY_CONSULTATION_ARTICLE_ID }.badge = it.toString()
                }
            }


            viewModelScope.launch {
                proInventoryLocalDb.bonLivraison.count().collect { it ->
                    proInventoryListMenu.first { it.id == INVENTORY_BON_TRANSFERT_ID }.badge = it.toString()
                }
            }

            viewModelScope.launch {
                proInventoryLocalDb.bonEntree.count().collect { it ->
                    proInventoryListMenu.first { it.id == INVENTORY_ACHAT_ID }.badge = it.toString()
                }
            }

            viewModelScope.launch {
                proInventoryLocalDb.inventaire.counts().collect { it ->
                    proInventoryListMenu.first { it.id == INVENTORY_INVENTAIRE_ID }.badge = it.size.toString()
                }
            }
        }

        private fun resetData() {
            utilisateur = Utilisateur()
            sessionCaisse = SessionCaisse()
        }

        fun addMenu(
            menus: List<MenuModel>,
            index: Int,
            clientList: List<Client>,
            authorizationList: List<Authorization>,
        ): Boolean {
            var addMenu = false
            if(menus[index].id == CLIENTS_ID) {
                addMenu = clientList.isNotEmpty() || authorizationList.any { it.AutoCodeAu == AuthorizationValuesProCaisse.AJOUT_CLIENT }
            }
          else  if(menus[index].authorizationID == "" && menus[index].id != CLIENTS_ID && menus[index].id != BON_COMMANDE_ID) {
                addMenu = true
            }
            else {
                if (menus[0].id == INVENTORY_HOME_ID) {
                    if (authorizationList.any { it.AutoCodeAu == menus[index].authorizationID }) {
                        addMenu = true
                    }
                } else {
                    //   else if(menus[index].id == CLIENTS_ID && (clientList.isNotEmpty() || (clientList.isEmpty() && proCaisseAuthorizationList.any { it.AutoCodeAu == AuthorizationValuesProCaisse.AJOUT_CLIENT }))) {

                    if(menus[index].id == CLIENTS_ID && authorizationList.any { it.AutoCodeAu == AuthorizationValuesProCaisse.AJOUT_CLIENT }) {
                        addMenu = true
                    }
                     else if(menus[index].id == CLIENTS_ID && clientList.isEmpty()) {
                        addMenu = false
                    }

                    else if(menus[index].id == BON_COMMANDE_ID && clientList.isEmpty()) {
                        addMenu = false
                    }
                    else if (authorizationList.any { it.AutoCodeAu == menus[index].authorizationID }) {
                        addMenu = true
                    }
                }
            }


            return addMenu
        }

        fun isLoading(
            getProCaisseDataViewModel: GetProCaisseDataViewModel,
            getProInventoryDataViewModel: GetProInventoryDataViewModel,
            getSharedDataViewModel: GetSharedDataViewModel,
            menus: List<MenuModel>,
            index: Int,
        ): Boolean =
            (getProCaisseDataViewModel.clientsState.loading && menus[index].id == ID.CLIENTS_ID) ||
                ((getProCaisseDataViewModel.ordreMissionWithLinesState.loading || getProCaisseDataViewModel.etatOrdreMissionState.loading) && menus[index].id == ID.TOURNEE_ID) ||
                ((getProCaisseDataViewModel.bonLivraisonState.loading || getProCaisseDataViewModel.ligneTicketCaisseState.loading) && menus[index].id == ID.BON_LIVRAISON_ID) ||
                ((getProCaisseDataViewModel.bonCommandeState.loading || getProCaisseDataViewModel.ligneBonCommandeState.loading) && (menus[index].id == ID.BON_COMMANDE_ID || menus[index].id == ID.INVENTAIRE_PATRIMOINE_ID)) ||
                ((getProCaisseDataViewModel.bonRetourState.loading || getProCaisseDataViewModel.ligneBonRetourState.loading) && menus[index].id == BON_RETOUR_ID) ||
                (getProCaisseDataViewModel.reglementCaisseState.loading && menus[index].id == ID.REGLEMENT_ID) ||
                (
                    (
                        getProCaisseDataViewModel.newProductState.loading ||
                            getProCaisseDataViewModel.promoState.loading ||
                            getProCaisseDataViewModel.prixState.loading ||
                            getProCaisseDataViewModel.autreState.loading
                    ) &&
                        menus[index].id == ID.VEILLE_CONCURENTIEL_ID
                ) ||
                (getSharedDataViewModel.articlesState.loading && menus[index].id == ID.PRODUIT_ID) ||

                ((getSharedDataViewModel.articlesState.loading || getSharedDataViewModel.articlesPaginationState.loading) && menus[index].id == ID.INVENTORY_CONSULTATION_ARTICLE_ID) ||
                ((getProInventoryDataViewModel.bonEntreeState.loading || getProInventoryDataViewModel.ligneBonEntreeState.loading) && menus[index].id == ID.INVENTORY_ACHAT_ID) ||
                ((getProInventoryDataViewModel.inventaireState.loading || getProInventoryDataViewModel.ligneInventaireState.loading) && menus[index].id == ID.INVENTORY_INVENTAIRE_ID) ||
                ((getProInventoryDataViewModel.bonLivraisonState.loading || getProInventoryDataViewModel.ligneBonLivraisonState.loading) && menus[index].id == ID.INVENTORY_BON_TRANSFERT_ID)

        fun errorGetData(
            getProCaisseDataViewModel: GetProCaisseDataViewModel,
            getProInventoryDataViewModel: GetProInventoryDataViewModel,
            getSharedDataViewModel: GetSharedDataViewModel,
            menus: List<MenuModel>,
            index: Int,
        ): String =

            if (getProCaisseDataViewModel.clientsState.error != null && menus[index].id == ID.CLIENTS_ID) {
                getProCaisseDataViewModel.clientsState.error?:"Unknow"
            } else if (getProCaisseDataViewModel.bonLivraisonState.error != null &&
                menus[index].id == ID.BON_LIVRAISON_ID
            ) {
                getProCaisseDataViewModel.bonLivraisonState.error?:"Unknow"
            } else if (getProCaisseDataViewModel.ligneTicketCaisseState.error != null &&
                menus[index].id == ID.BON_LIVRAISON_ID
            ) {
                getProCaisseDataViewModel.ligneTicketCaisseState.error!!
            } else if (getProCaisseDataViewModel.bonCommandeState.error != null &&
                (menus[index].id == ID.BON_COMMANDE_ID || menus[index].id == ID.INVENTAIRE_PATRIMOINE_ID)
            ) {
                getProCaisseDataViewModel.bonCommandeState.error?:"Unknow"
            } else if (getProCaisseDataViewModel.ligneBonCommandeState.error != null &&
                (menus[index].id == ID.BON_COMMANDE_ID || menus[index].id == ID.INVENTAIRE_PATRIMOINE_ID)
            ) {
                getProCaisseDataViewModel.ligneBonCommandeState.error?:"Unknow"
            } else if ((getProCaisseDataViewModel.bonRetourState.error != null || getProCaisseDataViewModel.ligneBonRetourState.error != null) && menus[index].id == BON_RETOUR_ID) {
                getProCaisseDataViewModel.bonRetourState.error + getProCaisseDataViewModel.ligneBonRetourState.error
            } else if ((getProCaisseDataViewModel.reglementCaisseState.error != null && menus[index].id == ID.REGLEMENT_ID)) {
                getProCaisseDataViewModel.reglementCaisseState.error?:"Unknow"
            } else if (getProCaisseDataViewModel.newProductState.error != null && menus[index].id == ID.VEILLE_CONCURENTIEL_ID) {
                getProCaisseDataViewModel.newProductState.error?:"Unknow"
            } else if (getProCaisseDataViewModel.promoState.error != null && menus[index].id == VEILLE_CONCURENTIEL_ID) {
                getProCaisseDataViewModel.promoState.error?:"Unknow"
            } else if (getProCaisseDataViewModel.prixState.error != null && menus[index].id == VEILLE_CONCURENTIEL_ID) {
                getProCaisseDataViewModel.prixState.error?:"Unknow"
            } else if (getProCaisseDataViewModel.autreState.error != null && menus[index].id == VEILLE_CONCURENTIEL_ID) {
                getProCaisseDataViewModel.autreState.error?:"Unknow"
            } else if (getSharedDataViewModel.articlesState.error != null && menus[index].id == ID.PRODUIT_ID) {
                getSharedDataViewModel.articlesState.error?:"Unknow"
            } else if (getSharedDataViewModel.articlesState.error != null  && menus[index].id == ID.INVENTORY_CONSULTATION_ARTICLE_ID) {
                getSharedDataViewModel.articlesState.error?:"Unknow"
            }
            else if (getSharedDataViewModel.articlesPaginationState.error != null && menus[index].id == ID.INVENTORY_CONSULTATION_ARTICLE_ID) {
           getSharedDataViewModel.articlesPaginationState.error?:"Unknow"
            }
            else if ((getProInventoryDataViewModel.bonEntreeState.error != null || getProInventoryDataViewModel.ligneBonEntreeState.error != null) && menus[index].id == ID.INVENTORY_ACHAT_ID) {
                getProInventoryDataViewModel.bonEntreeState.error + getProInventoryDataViewModel.ligneBonEntreeState.error
            } else if ((getProInventoryDataViewModel.inventaireState.error != null || getProInventoryDataViewModel.ligneInventaireState.error != null) && menus[index].id == ID.INVENTORY_INVENTAIRE_ID) {
                getProInventoryDataViewModel.inventaireState.error + getProInventoryDataViewModel.ligneInventaireState.error
            } else if ((getProInventoryDataViewModel.bonLivraisonState.error != null || getProInventoryDataViewModel.ligneBonLivraisonState.error != null) && menus[index].id == ID.INVENTORY_BON_TRANSFERT_ID) {
                getProInventoryDataViewModel.bonLivraisonState.error + getProInventoryDataViewModel.ligneBonLivraisonState.error
            } else {
                ""
            }






    fun onLogoutBtnClick(
        selectedBaseconfig: BaseConfig,
        resetDataStore:() -> Unit,
        navigate:(Any) -> Unit,
    ) {
        Globals.USER_ID = ""

        //TODO add this when back end add token
        //  if(!authViewModel.setTokenState.loading && authViewModel.setTokenState.error == null && (authViewModel.setTokenState.data == true)) {
        val defaultMenu =
            if (selectedBaseconfig.produit.contains(Globals.PRO_CAISSE_MOBILITY) && selectedBaseconfig.produit.contains(Globals.PRO_INVENTORY)) {
                proCaisseDrawerItems.first()
            }
        else if (selectedBaseconfig.produit.contains(Globals.PRO_INVENTORY)) {
                proInventoryDrawerItems.first()
            }

        else  if (selectedBaseconfig.produit.contains(Globals.PRO_CAISSE_MOBILITY)) {
                proCaisseDrawerItems.first()
            }
        else proCaisseDrawerItems.first()




        onSelectedMenuChange(defaultMenu)

        FirebaseMessaging.getInstance().deleteToken()

        resetDataStore()

        resetData()

        navigate(AuthGraph)
    }




    companion object {
        private const val HEART_CHECKED_DEFAULT = false

        var proCaisseDrawerItems =
            mutableListOf(
                MenuModel(
                    id = DASHBOARD_ID,
                    route = DashboardAnalyticsRoute,
                    title = R.string.dashboard_title,
                    titleShort = R.string.dashboard_title_Short,
                    contentDescription = R.string.cd_home_menu,
                    icon = CustomIcons.Dashboard,
                ),
                MenuModel(
                    id = CLIENTS_ID,
                    route = ClientListRoute,
                    title = R.string.clients_title,
                    titleShort = R.string.clients_title,
                    contentDescription = R.string.clients_title,
                    icon = CustomIcons.Client,
                ),
                MenuModel(
                    id = BON_LIVRAISON_ID,
                    route = BonLivraisonRoute(),
                    title = R.string.tickets_title,
                    titleShort = R.string.tickets_title_short,
                    contentDescription = R.string.tickets_title,
                    icon = CustomIcons.BonLivraison,
                    authorizationID = BL,
                ),
                MenuModel(
                    id = BON_COMMANDE_ID,
                    route = BonCommandeRoute(),
                    title = R.string.commande_title,
                    titleShort = R.string.commande_title_short,
                    contentDescription = R.string.commande_title,
                    icon = CustomIcons.BonCommande,
                    authorizationID = BC,
                ),
                MenuModel(
                    id = BON_RETOUR_ID,
                    route = BonRetourRoute(),
                    title = R.string.retour_title,
                    titleShort = R.string.retour_title_short,
                    contentDescription = R.string.retour_title,
                    icon = CustomIcons.BonRetour,
                    authorizationID = BR,
                ),
                /*   MenuModel(
                       id = INVENTAIRE_PATRIMOINE_ID,
                       title = R.string.patrimoine_title,
                       contentDescription = R.string.patrimoine_title,
                       icon = Icons.Default.Info,
                       authorizationID = PATRIMOINE
                   ),*/
                MenuModel(
                    id = INVENTAIRE_BATIMENT_ID,
                    route = InventaireBatimentRoute,
                    title = R.string.consultation_title,
                    titleShort = R.string.consultation_title,
                    contentDescription = R.string.consultation_title,
                    icon = CustomIcons.Patrimoine,
                    authorizationID = PATRIMOINE,
                ),
                MenuModel(
                    id = ZONE_CONSOMATION_ID,
                    route = ZoneConsomationRoute,
                    title = R.string.zone_consomation_title,
                    titleShort = R.string.zone_consomation_title_short,
                    contentDescription = R.string.zone_consomation_title,
                    icon = CustomIcons.ZoneConsommation,
                    authorizationID = PATRIMOINE,
                ),
                MenuModel(
                    id = DEPLACEMENT_OUT_BYUSER_ID,
                    route = DeplacementOutByUserRoute,
                    title = R.string.etat_depout_title,
                    titleShort = R.string.etat_depout_title_short,
                    contentDescription = R.string.etat_depout_title,
                    icon = CustomIcons.DeplacementOut,
                    authorizationID = PATRIMOINE,
                ),
                MenuModel(
                    id = DISTRIBUTION_NUMERIQUE_ID,
                    route = DigitalDistributionRoute(),
                    title = R.string.distribution_title,
                    titleShort = R.string.distribution_title_short,
                    contentDescription = R.string.distribution_title,
                    icon = Icons.Default.VerticalDistribute,
                    authorizationID = VEILLE_CONCURENTIELLE,
                ),
                MenuModel(
                    id = DEPENSE_ID,
                    route = DepenceRoute,
                    title = R.string.expense_label,
                    titleShort = R.string.expense_label,
                    contentDescription = R.string.expense_label,
                    icon = CustomIcons.Money,
                    authorizationID = DEPENSE,
                ),
                MenuModel(
                    id = REGLEMENT_ID,
                    route = ReglementRoute(),
                    title = R.string.payments_title,
                    titleShort = R.string.payments_title,
                    contentDescription = R.string.payments_title,
                    icon = CustomIcons.Payment,
                    authorizationID = BL,
                ),
                MenuModel(
                    id = VEILLE_CONCURENTIEL_ID,
                    route = VeilleConcurentielleRoute,
                    title = R.string.vc_label,
                    titleShort = R.string.vc_label_short,
                    contentDescription = R.string.vc_label,
                    icon = CustomIcons.Hearing,
                    authorizationID = VEILLE_CONCURENTIELLE,
                ),
                MenuModel(
                    id = PRODUIT_ID,
                    route = ProductListRoute,
                    title = R.string.products_title,
                    titleShort = R.string.products_title,
                    contentDescription = R.string.products_title,
                    icon = CustomIcons.Produit,
                ),
                /*   MenuModel(
                       id = PATRIMOINE_ID,
                       title = R.string.patrimoines,
                       contentDescription = R.string.patrimoines,
                       icon = Icons.Default.L
                   ),*/
                MenuModel(
                    id = TOURNEE_ID,
                    route = TourneRoute,
                    title = R.string.tournee_title,
                    titleShort = R.string.tournee_title,
                    contentDescription = R.string.tournee_title,
                    icon = CustomIcons.Tourney,
                    authorizationID = TOURNE,
                ),
                MenuModel(
                    id = SETTINGS_ID,
                    route = SettingRoute,
                    title = R.string.settings,
                    titleShort = R.string.settings,
                    contentDescription = R.string.cd_settings_menu,
                    icon = CustomIcons.Setting,
                ),
                /*   MenuModel(
                       id = PRO_CAISSE_SYNC,
                       title = R.string.sync_title,
                       contentDescription = R.string.sync_title,
                       icon = Icons.Default.Sync,
                   ),
           MenuModel(
               id = RECAP_BON_LIVRAISON_ID,
               title = R.string.Recap_Current_Session_tickets_title,
               contentDescription = R.string.Recap_Current_Session_tickets_title,
               icon = Icons.Default.Info,
               authorizationID = BL
           ),
         MenuModel(
               id = ARCHIVES_ID,
               title = R.string.archive_label,
               contentDescription = R.string.archive_label,
               icon = Icons.Default.Info
           ),

           MenuModel(
               id = "Settings",
               title = R.string.settings,
               contentDescription = R.string.cd_help_menu,
               icon = Icons.Default.Settings
           ),
           MenuModel(
               id = "Logout",
               title = R.string.logout_title,
               contentDescription = R.string.cd_help_menu,
               icon = Icons.Default.ExitToApp
           )*/
            )

        var proInventoryDrawerItems =
            mutableListOf(
                MenuModel(
                    id = INVENTORY_HOME_ID,
                    route = InventoryHomeRoute,
                    title = R.string.pro_inventory,
                    titleShort = R.string.pro_inventory,
                    contentDescription = R.string.pro_inventory,
                    icon = CustomIcons.Home,
                ),
                MenuModel(
                    id = INVENTORY_ACHAT_ID,
                    route = InventoryAchatRoute,
                    title = R.string.achat,
                    titleShort = R.string.achat,
                    contentDescription = R.string.achat,
                    icon = CustomIcons.ShoppingCart,
                    authorizationID = PURCHASE_DRAWER_ITEM,
                ),
                MenuModel(
                    id = INVENTORY_INVENTAIRE_ID,
                    route = InventoryInventaireRoute,
                    title = R.string.inventaire,
                    titleShort = R.string.inventaire,
                    contentDescription = R.string.inventaire,
                    icon = CustomIcons.Inventaire,
                    authorizationID = INEVENTORY_DRAWER_ITEM,
                ),
                MenuModel(
                    id = INVENTORY_BON_TRANSFERT_ID,
                    route = InventoryBonTransfertRoute,
                    title = R.string.bon_Transfert,
                    titleShort = R.string.bon_Transfert_short,
                    contentDescription = R.string.bon_Transfert,
                    icon = Icons.AutoMirrored.Filled.SendAndArchive,
                    authorizationID = TRANSFERT_DRAWER_ITEM,
                ),
                MenuModel(
                    id = INVENTORY_TICKET_RAYON_ID,
                    route = InventoryTicketRayonRoute,
                    title = R.string.ticket_rayon,
                    titleShort = R.string.ticket_rayon_short,
                    contentDescription = R.string.ticket_rayon,
                    icon = Icons.Default.QrCode,
                    authorizationID = TICKET_RAYON_DRAWER_ITEM,
                ),
                MenuModel(
                    id = INVENTORY_CONSULTATION_ARTICLE_ID,
                    route = ProductListRoute,
                    title = R.string.consultation_article,
                    titleShort = R.string.article_field_title,
                    contentDescription = R.string.consultation_article,
                    icon = CustomIcons.Consultation,
                    authorizationID = ARTICLE_DRAWER_ITEM,
                ),
                MenuModel(
                    id = SETTINGS_ID,
                    route = SettingRoute,
                    title = R.string.settings,
                    titleShort = R.string.settings,
                    contentDescription = R.string.cd_settings_menu,
                    icon = CustomIcons.Setting,
                ),
            )
    }

}
