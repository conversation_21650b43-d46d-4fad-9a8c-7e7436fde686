package com.asmtunis.procaisseinventory.nav_components.sync

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Done
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.core.sync.SyncCompletionViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim

/**
 * Overlay component that shows sync progress and blocks navigation drawer access
 * until data synchronization is complete
 */
@Composable
fun SyncLoadingOverlay(
    syncCompletionViewModel: SyncCompletionViewModel,
    modifier: Modifier = Modifier
) {
    val isSyncComplete = syncCompletionViewModel.isSyncComplete
    val isCheckingSync = syncCompletionViewModel.isCheckingSync
    val syncProgress = syncCompletionViewModel.getSyncProgress()
    val syncStatusMessage = syncCompletionViewModel.getSyncStatusMessage()
    val syncError = syncCompletionViewModel.syncError

    // Animate progress
    val animatedProgress by animateFloatAsState(
        targetValue = syncProgress,
        animationSpec = tween(durationMillis = 500),
        label = "sync_progress"
    )

    // Show overlay only when sync is not complete
    if (!isSyncComplete) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(
                    Color.Black.copy(alpha = 0.7f)
                ),
            contentAlignment = Alignment.Center
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .wrapContentHeight(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Loading animation
                    if (isCheckingSync || syncError == null) {
                        LottieAnim(
                            modifier = Modifier.size(80.dp),
                            lotti = com.asmtunis.procaisseinventory.R.raw.loading
                        )
                    }

                    // Title
                    Text(
                        text = if (syncError != null) "Sync Error" else "Synchronizing Data",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = if (syncError != null) 
                            MaterialTheme.colorScheme.error 
                        else 
                            MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center
                    )

                    // Status message
                    Text(
                        text = syncStatusMessage,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )

                    // Progress bar (only show if not in error state)
                    if (syncError == null) {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            LinearProgressIndicator(
                                progress = { animatedProgress },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(8.dp),
                                color = MaterialTheme.colorScheme.primary,
                                trackColor = MaterialTheme.colorScheme.surfaceVariant,
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = "${(animatedProgress * 100).toInt()}%",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }

                    // Module status indicators
                    SyncModuleStatus(syncCompletionViewModel)

                    // Error retry button
                    if (syncError != null) {
                        Button(
                            onClick = {
                                // Reset error state to allow retry
                                syncCompletionViewModel.resetSyncState()
                            },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("Retry Sync")
                        }
                    }

                    // Info text
                    Text(
                        text = if (syncError != null) 
                            "Please check your internet connection and try again."
                        else 
                            "Please wait while we download the latest data. Navigation will be available once sync is complete.",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.alpha(0.8f)
                    )
                }
            }
        }
    }
}

/**
 * Shows the status of individual module synchronization
 */
@Composable
private fun SyncModuleStatus(
    syncCompletionViewModel: SyncCompletionViewModel
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "Sync Status:",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )

        // Shared Data Status
        SyncModuleStatusItem(
            moduleName = "Shared Data",
            isComplete = syncCompletionViewModel.isSharedDataSyncComplete,
            isRequired = true
        )

        // ProCaisse Status (if user has permission)
        if (syncCompletionViewModel.isProCaisseSyncComplete || !syncCompletionViewModel.isProCaisseSyncComplete) {
            SyncModuleStatusItem(
                moduleName = "ProCaisse Data",
                isComplete = syncCompletionViewModel.isProCaisseSyncComplete,
                isRequired = true // Will be hidden if not required
            )
        }

        // ProInventory Status (if user has permission)
        if (syncCompletionViewModel.isProInventorySyncComplete || !syncCompletionViewModel.isProInventorySyncComplete) {
            SyncModuleStatusItem(
                moduleName = "ProInventory Data",
                isComplete = syncCompletionViewModel.isProInventorySyncComplete,
                isRequired = true // Will be hidden if not required
            )
        }
    }
}

/**
 * Individual module status item
 */
@Composable
private fun SyncModuleStatusItem(
    moduleName: String,
    isComplete: Boolean,
    isRequired: Boolean
) {
    if (!isRequired) return

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = moduleName,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            if (isComplete) {
                Icon(
                    imageVector = androidx.compose.material.icons.Icons.Default.Done,
                    contentDescription = "Complete",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = "Complete",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary,
                    fontSize = 12.sp
                )
            } else {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "Syncing...",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontSize = 12.sp
                )
            }
        }
    }
}
