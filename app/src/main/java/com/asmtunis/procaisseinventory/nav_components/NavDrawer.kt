
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ModalNavigationDrawer
import androidx.compose.material3.PermanentNavigationDrawer
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.nav_drawer.ui.DrawerContent
import com.asmtunis.procaisseinventory.nav_components.nav_rail.ReplyNavigationRail
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.launch

@Composable
fun NavDrawer(
    navigate: (route: Any) -> Unit,
    drawer: DrawerState,
    mainViewModel: MainViewModel,
    navDrawerViewmodel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    gesturesEnabled: Boolean = false, // Disabled navigation drawer gestures
    settingViewModel: SettingViewModel,
    syncCompletionViewModel: com.asmtunis.procaisseinventory.core.sync.SyncCompletionViewModel? = null,
    content: @Composable () -> Unit
) {
    val menus =
        if (navDrawerViewmodel.isProInventory) navDrawerViewmodel.proInventoryListMenu
        else navDrawerViewmodel.proCaisseListMenu

    val uiWindowState = settingViewModel.uiWindowState
    val scope = rememberCoroutineScope()
    val utilisateur = mainViewModel.utilisateur

    // Check sync completion status (only if syncCompletionViewModel is provided)
    syncCompletionViewModel?.let { syncVM ->
        LaunchedEffect(utilisateur) {
            if (utilisateur != com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur()) {
                syncVM.checkSyncCompletion(
                    getSharedDataViewModel = getSharedDataViewModel,
                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                    utilisateur = utilisateur
                )
            }
        }

        // Continuously monitor sync status
        LaunchedEffect(
            getSharedDataViewModel.stationState.loading,
            getSharedDataViewModel.parametragesState.loading,
            getProCaisseDataViewModel.clientsState.loading,
            getProInventoryDataViewModel.typePrixUnitaireHTState.loading
        ) {
            syncVM.checkSyncCompletion(
                getSharedDataViewModel = getSharedDataViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                utilisateur = utilisateur
            )
        }
    }

     if(uiWindowState.navigationType == ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER) {
        Box {
            PermanentNavigationDrawer(
                drawerContent = {
                    DrawerContent(
                        navigateTo = { navigate(it) },
                        onMenuClick = { menu ->
                            // Only allow navigation if sync is complete (or if syncCompletionViewModel is not provided)
                            if (syncCompletionViewModel?.isSyncComplete != false) {
                                navDrawerViewmodel.onSelectedMenuChange(menu)
                                navigate(menu.route)
                            }
                        },
                        menus = menus,
                        networkViewModel = networkViewModel,
                        navDrawerVM = navDrawerViewmodel,
                        dataViewModel = dataViewModel,
                        mainViewModel = mainViewModel,
                        getProCaisseDataViewModel = getProCaisseDataViewModel,
                        getProInventoryDataViewModel = getProInventoryDataViewModel,
                        getSharedDataViewModel = getSharedDataViewModel,
                        syncInventoryViewModel = syncInventoryViewModel,
                        syncSharedViewModels = syncSharedViewModels,
                        syncProcaisseViewModels = syncProcaisseViewModels,
                        settingViewModel = settingViewModel,
                        syncCompletionViewModel = syncCompletionViewModel
                    )
                }) {
                content()
            }

            // Show sync loading overlay if sync is not complete (only if syncCompletionViewModel is provided)
            syncCompletionViewModel?.let { syncVM ->
                com.asmtunis.procaisseinventory.nav_components.sync.SyncLoadingOverlay(
                    syncCompletionViewModel = syncVM
                )
            }
        }
         }
         else {
             Box {
                 ModalNavigationDrawer(
                     modifier = Modifier,
                     drawerState = drawer,
                     gesturesEnabled = if (syncCompletionViewModel?.isSyncComplete != false) gesturesEnabled else false,
                     drawerContent = {
                         DrawerContent(
                             navigateTo = { navigate(it) },
                             onMenuClick = { menu ->
                                 // Only allow navigation if sync is complete (or if syncCompletionViewModel is not provided)
                                 if (syncCompletionViewModel?.isSyncComplete != false) {
                                     scope.launch { drawer.close() }
                                     navDrawerViewmodel.onSelectedMenuChange(menu)
                                     navigate(menu.route)
                                 }
                             },
                             menus = menus,
                             mainViewModel = mainViewModel,
                             networkViewModel = networkViewModel,
                             navDrawerVM = navDrawerViewmodel,
                             dataViewModel = dataViewModel,
                             syncInventoryViewModel = syncInventoryViewModel,
                             syncSharedViewModels = syncSharedViewModels,
                             syncProcaisseViewModels = syncProcaisseViewModels,
                             getProCaisseDataViewModel = getProCaisseDataViewModel,
                             getProInventoryDataViewModel = getProInventoryDataViewModel,
                             getSharedDataViewModel = getSharedDataViewModel,
                             settingViewModel = settingViewModel,
                             syncCompletionViewModel = syncCompletionViewModel
                         )
                     }
                 ) {
                     Row(modifier = Modifier.fillMaxSize()) {
                         AnimatedVisibility(visible = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_RAIL) {
                             ReplyNavigationRail(
                                 navDrawerVM = navDrawerViewmodel,
                                 mainViewModel = mainViewModel,
                                 getSharedDataViewModel = getSharedDataViewModel,
                                 menus = menus,
                                 onMenuClick = { menu ->
                                     // Only allow navigation if sync is complete (or if syncCompletionViewModel is not provided)
                                     if (syncCompletionViewModel?.isSyncComplete != false) {
                                         navDrawerViewmodel.onSelectedMenuChange(menu)
                                         navigate(menu.route)
                                     }
                                 },
                                 getProCaisseDataViewModel = getProCaisseDataViewModel,
                                 getProInventoryDataViewModel = getProInventoryDataViewModel,
                                 navigationContentPosition = uiWindowState.navigationContentPosition,
                                 onDrawerClicked = {
                                     if (syncCompletionViewModel?.isSyncComplete != false) {
                                         // Navigate to shortcuts screen instead of opening drawer
                                         navigate(com.asmtunis.procaisseinventory.core.navigation.HomePageRoute)
                                     }
                                 },
                             )
                         }
                         content()
                     }
                 }

                 // Show sync loading overlay if sync is not complete (only if syncCompletionViewModel is provided)
                 syncCompletionViewModel?.let { syncVM ->
                     com.asmtunis.procaisseinventory.nav_components.sync.SyncLoadingOverlay(
                         syncCompletionViewModel = syncVM
                     )
                 }
             }
   }
}
