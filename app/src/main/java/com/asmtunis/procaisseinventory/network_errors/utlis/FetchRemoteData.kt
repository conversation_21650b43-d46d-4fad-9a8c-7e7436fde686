package com.asmtunis.procaisseinventory.network_errors.utlis

import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_ARTICLES_BY_STATION
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_ARTICLES_CLIENT_PRIX
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_CARTES_RESTO
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_CARTES_RESTO_BY_CODE
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_CHEQUE_BY_REGLEMENTS
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_CLIENTS
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_CODE_TEST
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_IMMOBILISATION
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_PRICES_BY_STATION
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_REGLEMENT_CAISSE_BY_SESSION
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_SESSION_CAISSE_BY_USER
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_TICKETS_BY_CAISSE_ID
import com.asmtunis.procaisseinventory.core.ktor.extractCurrentPage
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.messaging.FirebaseMessaging
import com.simapps.ui_kit.utils.getCurrentDate

object FetchRemoteData {
    fun fetchRemoteData(
        url: String,
        extraInfo: String = "",
        baseConfig: BaseConfig,
        utilisateur: Utilisateur,
        tickets: List<Ticket>,
        listImmobilisation: List<Immobilisation>,
        listInventaire: List<Inventaire>,
        clientList: List<Client>,
        reglementCaisseList: List<ReglementCaisse>,
        sessionCaisse: SessionCaisse,
        exerciceCode: String,
        networkErrorsList: List<NetworkError>,
        reglementsCaisseList: List<ReglementCaisse>,
        authViewModel: AuthViewModel,
        networkErrorsVM: NetworkErrorsViewModel,
        getProCaisseDataViewModel: GetProCaisseDataViewModel,
        getSharedDataViewModel: GetSharedDataViewModel,
        getProInventoryDataViewModel: GetProInventoryDataViewModel,
    ) {
        networkErrorsVM.deleteNetworkErrorByUrl(url = url, extraInfo = extraInfo)

        val succesGetClients = !networkErrorsList.any { it.url == GET_CLIENTS }
        val succesGetSessionCaisseByUser =
            !networkErrorsList.any { it.url == GET_SESSION_CAISSE_BY_USER }
        val succesGetSessionCaisseBySession =
            !networkErrorsList.any { it.url == GET_REGLEMENT_CAISSE_BY_SESSION }
        val succesGetTicketsByCaisseId = !networkErrorsList.any { it.url == GET_TICKETS_BY_CAISSE_ID }
        val succesGetIMMOBILISATION = !networkErrorsList.any { it.url == GET_IMMOBILISATION }


        when (url) {
            Urls.INSERT_USER_TOKEN -> {
                FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
                    if (!task.isSuccessful) {
                        return@OnCompleteListener
                    }
                    // Get new FCM registration token

                    val token = task.result

                    authViewModel.setUserFireBaseToken(
                        token = token,
                        baseConfig = baseConfig
                    )


                }
                )
            }
            Urls.GET_BANQUES -> {
                getProCaisseDataViewModel.getBanques(baseConfig = baseConfig)
            }

            Urls.GET_ARTICLES, Urls.GET_ARTICLES_PAGINATION -> {
                if (extraInfo.isNotEmpty())
                    getSharedDataViewModel.onCurrentArticlePageChange(value = extractCurrentPage(input = extraInfo)?: 1)


                getSharedDataViewModel.getArticl(
                    baseConfig = baseConfig,
                    utilisateur = utilisateur,
                )
            }

            Urls.GET_UNITE_ARTICLE, Urls.GET_UNITE_ARTICLE_PAGINATION -> {
                if (extraInfo.isNotEmpty())
                    getSharedDataViewModel.onCurrentUniteArticlePageChange(value = extractCurrentPage(input = extraInfo)?: 1)


                getSharedDataViewModel.getUniteArticle(
                    baseConfig = baseConfig,
                )
            }

            Urls.GET_ARTICLES_CODE_BARE_PAGINATION -> {
                if (extraInfo.isNotEmpty())
                    getSharedDataViewModel.onCurrentArticleCodeBarePageChange(value = extractCurrentPage(input = extraInfo)?: 1)


                getSharedDataViewModel.getArticleCodeBare(
                    baseConfig = baseConfig,
                )
            }




            Urls.GET_COUNT_ARTICLE -> {
                getSharedDataViewModel.getArticleCount(baseConfig = baseConfig)
            }

            GET_ARTICLES_BY_STATION -> {

            }

            GET_ARTICLES_CLIENT_PRIX -> {

            }

            GET_CODE_TEST -> {

            }


            GET_PRICES_BY_STATION -> {
                getProCaisseDataViewModel.getPricePerStation(baseConfig = baseConfig)
            }

            GET_CARTES_RESTO -> {
                getProCaisseDataViewModel.getCarteResto(baseConfig = baseConfig)
            }

            GET_CARTES_RESTO_BY_CODE -> {

            }


            GET_CHEQUE_BY_REGLEMENTS -> {

                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getChequeCaisse(
                        baseConfig = baseConfig,
                        reglementsCaisse = reglementsCaisseList
                    )
                } else {
                    //todo show message to GET_SESSION_CAISSE_BY_USER and or  GET_CLIENTS
                }

            }

            Urls.GET_CHEQUE_BY_REGLEMENT -> {

            }


            Urls.GET_DEVISES -> {
                getSharedDataViewModel.getDevises(baseConfig = baseConfig)
            }

            Urls.GET_ETABLISSEMENTS -> {
                getProCaisseDataViewModel.getEtablissement(baseConfig = baseConfig)
            }

            Urls.GET_EXERCICE -> {
                getSharedDataViewModel.getExercice(baseConfig = baseConfig)
            }

            Urls.GET_FACTURE -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getFatures(baseConfig = baseConfig)
                }

            }


            Urls.GET_FAMILLES -> {
                getSharedDataViewModel.getFamille(baseConfig = baseConfig)
            }


            Urls.GET_MARQUES -> {
                getSharedDataViewModel.getMarques(baseConfig = baseConfig)
            }


            Urls.GET_PARAMETRAGE -> {
                getSharedDataViewModel.getParametrages(baseConfig = baseConfig)
            }

            Urls.GET_PREFIXES -> {
                getSharedDataViewModel.getPrefix(baseConfig = baseConfig)
            }

            Urls.GET_SESSION_CAISSES -> {
                getProCaisseDataViewModel.getSessionCaisses(
                    baseConfig = baseConfig,
                    utilisateur = utilisateur,
                    clientList = clientList,
                    exerciceCode = exerciceCode
                )
            }

            Urls.GET_SESSION_CAISSE_BY_USER -> {
                if (succesGetClients) {
                    getProCaisseDataViewModel.getSessionCaissesByUser(
                        baseConfig = baseConfig,
                        utilisateur = utilisateur,
                        clientList = clientList,
                        exerciceCode = exerciceCode
                    )
                }
            }

            Urls.GET_STATISTICS -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getStatistics(
                        baseConfig = baseConfig,
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse
                    )

                }
            }

            Urls.GET_TRAITE_CAISSE_BY_REGLEMENTS -> {
                if (succesGetSessionCaisseBySession) {
                    getProCaisseDataViewModel.getTraiteCaisse(
                        baseConfig = baseConfig,
                        reglementsCaisse = reglementCaisseList
                    )
                }
            }

            Urls.GET_ALL_TIMBRE -> {
                getSharedDataViewModel.getTimbre(baseConfig = baseConfig, utilisateur = utilisateur)
            }

            Urls.GET_TVA -> {
                getProInventoryDataViewModel.getTva(baseConfig = baseConfig)
            }
            Urls.GET_UNITE -> {
                getSharedDataViewModel.getUnite(baseConfig = baseConfig)
            }

            Urls.GET_VILLE -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getVilles(
                        baseConfig = baseConfig,
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse,
                    )
                }
            }

            Urls.GET_COMMANDE -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getBonCommande(
                        baseConfig = baseConfig,
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse,
                        clientList = clientList
                    )
                }
            }

            Urls.GET_LIGNE_COMMANDE -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getLigneBonCommande(
                        baseConfig = baseConfig,
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse
                    )
                }
            }

            Urls.GET_IMAGES -> {

                getProCaisseDataViewModel.getPiecesJointInventaire(baseConfig = baseConfig)

            }

            Urls.GET_LIGNE_TICKET_BY_TICKETS -> {
                if (succesGetSessionCaisseByUser && succesGetTicketsByCaisseId && succesGetClients) {

                    getProCaisseDataViewModel.getLigneTickets(
                        baseConfig = baseConfig,
                        tickets = tickets
                    )
                }
            }

            Urls.GET_TICKETS_BY_CAISSE_ID -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getTickets(
                        baseConfig = baseConfig,
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse,
                        ddm = getCurrentDate(),
                        exercice = exerciceCode,
                        archive = false,
                        zone = true
                    )
                }

            }

            Urls.GET_MAX_NUM_TICKET -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getMaxNumTicket(
                        baseConfig = baseConfig,
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse,
                        exercice = exerciceCode,
                        carnet = sessionCaisse.sCIdCarnet!!
                    )
                }

            }

            Urls.GET_BON_RETOUR -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getBonRetour(
                        baseConfig = baseConfig,
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse,
                        clientList = clientList
                    )
                }
            }

            Urls.GET_LIGNE_BON_RETOUR -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel.getLigneBonRetour(
                        baseConfig = baseConfig,
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse
                    )
                }

            }


            Urls.GET_CLIENTS -> {

                getProCaisseDataViewModel.getClient(
                    baseConfig = baseConfig,
                    utilisateur = utilisateur,
                    exerciceCode = exerciceCode
                )
            }

            Urls.GET_ALL_TYPE_SERVICE -> {
                getProCaisseDataViewModel.getTypeServicesDn(baseConfig = baseConfig)
            }

            Urls.GET_ALL_FAMILLE -> {
                getProCaisseDataViewModel.getFamilleDn(baseConfig = baseConfig)
            }

            Urls.GET_ALL_SUPERFICIE -> {

                getProCaisseDataViewModel.getSuperficieDn(baseConfig = baseConfig)
            }

            Urls.GET_ALL_TYPE_P_VENTE -> {
                getProCaisseDataViewModel.getTypePointVenteDn(baseConfig = baseConfig)
            }

            Urls.GET_ALL_VISITE -> {

                if (succesGetClients) {
                    getProCaisseDataViewModel.getVisitesDn(
                        baseConfig = baseConfig,
                        clientList = clientList
                    )
                }
            }

            Urls.GET_ALL_LIGNE_VISITE -> {

                if (succesGetClients) {
                    getProCaisseDataViewModel.getLignesVisitesDn(baseConfig = baseConfig)
                }
            }

            Urls.GET_VISITE_BY_CODE -> {

            }


            Urls.GET_ALL_DEPLACEMENT_OUT_BY_USER -> {
                if (succesGetIMMOBILISATION) {
                    getProCaisseDataViewModel.getDeplacementOutByUser(
                        baseConfig = baseConfig,
                        listImmobilisation = listImmobilisation
                    )

                }
            }

            Urls.GET_IMMOBILISATION -> {
                getProCaisseDataViewModel.getImmobilisation(baseConfig = baseConfig)
            }

            Urls.GET_BATIMENT_BY_USER -> {
                if (succesGetIMMOBILISATION) {
                    getProCaisseDataViewModel.getBatimentsByUser(baseConfig = baseConfig)
                }
            }

            Urls.GET_ALL_TYPE_MOUVEMENT -> {
                getProCaisseDataViewModel.getTypeMouvement(baseConfig = baseConfig)
            }

            Urls.GET_REGLEMENT_CAISSE_BY_TICKET -> {}

            Urls.GET_REGLEMENT_CAISSE_BY_TICKETS -> {}

            Urls.GET_REGLEMENT_CAISSE_BY_SESSION -> {
                if (succesGetSessionCaisseByUser && succesGetClients) {
                    getProCaisseDataViewModel. getReglementCaiss(
                        baseConfig = baseConfig,
                        sCIdSCaisse = sessionCaisse.sCIdSCaisse,
                        exercice = exerciceCode
                    )
                }

            }


            Urls.GET_ORDRE_MISSION_WITH_LINES -> {
                getProCaisseDataViewModel. getOrdreWithLines(
                    baseConfig = baseConfig,
                    utilisateur = utilisateur
                )
            }

            Urls.GET_ETAT_ORDRE_MISSION -> {
                getProCaisseDataViewModel.getEtatOrdreMission(baseConfig = baseConfig)
            }


            Urls.GET_VC_LISTE_CONCURRENT -> {
                getProCaisseDataViewModel.getVCListeConcurrent(baseConfig = baseConfig)
            }

            Urls.GET_VC_NEW_PRODUCT -> {

                getProCaisseDataViewModel.getVCNewProduct(baseConfig = baseConfig)
            }

            Urls.GET_VC_PROMO -> {
                getProCaisseDataViewModel.getVCPromo(baseConfig = baseConfig)
            }

            Urls.GET_VC_PRIX -> {
                getProCaisseDataViewModel.getVCPrix(baseConfig = baseConfig)
            }

            Urls.GET_VC_AUTRE -> {
                getProCaisseDataViewModel.getVCAutre(baseConfig = baseConfig)
            }

            Urls.GET_VC_TYPE_COMMUNICATION -> {
                getProCaisseDataViewModel.getVCTypeCommunication(baseConfig = baseConfig)
            }

            Urls.GET_VC_IMAGE -> {
                getProCaisseDataViewModel.getVCImage(baseConfig = baseConfig)
            }


            Urls.GET_BON_ENTREES -> {
                getProInventoryDataViewModel.getBonEntree(baseConfig= baseConfig)
            }

            Urls.GET_LIGNE_BON_ENTREES -> {
                getProInventoryDataViewModel.getLigneBonEntree(baseConfig= baseConfig)
            }


            Urls.GET_BON_LIVRAISON -> {
                getProInventoryDataViewModel.getBonLivraison(baseConfig= baseConfig)
            }

            Urls.GET_LIGNE_BON_LIVRAISON -> {
                getProInventoryDataViewModel.getLigneBonLivraison(baseConfig= baseConfig)
            }


            Urls.GET_FOURNISSEURS -> {
                getProInventoryDataViewModel.getFournisseur(baseConfig= baseConfig)
            }


            Urls.GET_STATIONS -> {
                getSharedDataViewModel.getStations(baseConfig= baseConfig)
            }

            Urls.GET_STOCK_ARTICLE_BY_STATIONS, Urls.GET_STOCK_ARTICLE_BY_STATIONS_PAGINATION -> {
                if (extraInfo.isNotEmpty())
                    getSharedDataViewModel.onCurrentStockArticleByStationPageChange(value = extractCurrentPage(input = extraInfo)?: 1)


                getSharedDataViewModel.getstockArticleByStation(baseConfig = baseConfig)
            }


            Urls.GET_TYPE_PRIX_UNITAIRE_HT -> {
                getProInventoryDataViewModel.getTypePrixUnitaireHT(baseConfig= baseConfig)
            }


            Urls.GET_INVENTAIRES -> {
                getProInventoryDataViewModel.getInventaire(baseConfig= baseConfig)
            }

            Urls.GET_LIGNE_INVENTAIRES -> {}

            Urls.GET_LIGNE_INVENTAIRES_BY_CODE -> {

                for (inventaire in listInventaire) {
                    getProInventoryDataViewModel.getLigneInventaires(
                        baseConfig = baseConfig,
                        code = inventaire.iNVCode
                    )
                }
            }

        }
    }
}