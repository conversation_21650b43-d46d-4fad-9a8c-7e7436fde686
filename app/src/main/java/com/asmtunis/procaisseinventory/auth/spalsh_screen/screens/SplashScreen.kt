package com.asmtunis.procaisseinventory.auth.spalsh_screen.screens

import android.util.Log
import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CutCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.SubcomposeAsyncImage
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.auth.base_config.HeaderStatus
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AuthGraph
import com.asmtunis.procaisseinventory.core.navigation.BaseConfigRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryGraph
import com.asmtunis.procaisseinventory.core.navigation.LoginRoute
import com.asmtunis.procaisseinventory.core.navigation.NoLicenseRoute
import com.asmtunis.procaisseinventory.core.navigation.ProcaisseGraph
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.shared_ui_components.text_animation.LetterByLetterAnimatedText
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.LoadingAnimation
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun SplashScreen(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    authViewModel: AuthViewModel,
    settingViewModel: SettingViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
) {
    val context = LocalContext.current
    val checkLicenState = authViewModel.checkLicensestate
    val responseCheckLicense = checkLicenState.data
    val isConnected = networkViewModel.isConnected
    val scope = rememberCoroutineScope()
    val utilisateur = mainViewModel.utilisateur
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)


    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    LaunchedEffect(key1 = checkLicenState, key2 = isConnected) {

        authViewModel.loginFinshed()
      /*  if (!isConnected) {
            return@LaunchedEffect
        }*/

        if (checkLicenState.loading) return@LaunchedEffect
        if (!checkLicenState.error.isNullOrEmpty()) {
            navigateTo(
                navigate = { navigate(it)},
                navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                    navigatePopUpTo(route, popUpTo, isInclusive)
                },
                dataViewModel = dataViewModel,
                selectedBaseconfig = selectedBaseconfig,
                "1",
            )
            return@LaunchedEffect
        }

        if (responseCheckLicense == null) {
            navigateTo(
                navigate = { navigate(it)},
                navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                    navigatePopUpTo(route, popUpTo, isInclusive)
                },
                dataViewModel = dataViewModel,
                selectedBaseconfig = selectedBaseconfig,
                "3",
            )
            return@LaunchedEffect
        }


        val haveProCaisseLicence = selectedBaseconfig.licences.any { it.produit?.contains(Globals.PRO_CAISSE_MOBILITY) == true }
        val haveProInventoryLicence = selectedBaseconfig.licences.any { it.produit?.contains(Globals.PRO_INVENTORY) == true }


        dataViewModel.saveProcaisseActivationState(haveProCaisseLicence)

        dataViewModel.saveInventoryActivationState(haveProInventoryLicence)

        val demandeProCaisseSent = responseCheckLicense.demandes.any { it.produit.contains(Globals.PRO_CAISSE_MOBILITY) }
        val demandeProInventorySent = responseCheckLicense.demandes.any { it.produit.contains(Globals.PRO_INVENTORY) }

        dataViewModel.saveProInventorySubscribtionSent(demandeProInventorySent)
        dataViewModel.saveProcaisseSubscribtionSent(demandeProCaisseSent)

        if (responseCheckLicense.baseConfig.isEmpty() && responseCheckLicense.demandes.isEmpty()) {

            dataViewModel.resetDataStore()

            dataViewModel.saveInventoryActivationState(false)
            dataViewModel.saveProcaisseActivationState(false)

            navigate(NoLicenseRoute)
            return@LaunchedEffect
        }
        if (responseCheckLicense.baseConfig.isEmpty()) {



            scope.launch {

                showToast(
                    context = context,
                    toaster = toaster,
                    message = context.resources.getString(R.string.demandeLicenceDejaEnvoyer) + "\n"+ context.resources.getString(R.string.demande_en_attente, responseCheckLicense.demandes.size.toString()),
                    type =  ToastType.Info,
                )
                delay(3000)
              //  navigate(Screen.WaitingLicenceActivation.Route)
                navigate(BaseConfigRoute)
                return@launch
            }
           return@LaunchedEffect
        }

        // TODO()  if response.demandes not empty show there is pending demande

        // TODO() getBaseConfigProcaisse will be getBaseConfig and delete for inventory

        if (selectedBaseconfig == BaseConfig()) {
            // TODO() go to select base config and dont FOGET TO SELECT LICENCE ALSO
            navigate(BaseConfigRoute)
            return@LaunchedEffect
        }


        if (authViewModel.getSelectedBaseConfingFromRemoteResponse(
                baseConfigList = responseCheckLicense.baseConfig,
                baseConfig = selectedBaseconfig,
            ) == null
        ) {
            dataViewModel.saveSelectedBaseConfig("")
            navigate(BaseConfigRoute)
            // TODO() go to select base config and dont FOGET TO SELECT LICENCE ALSO
            return@LaunchedEffect
        }

        if (authViewModel.getSelectedBaseConfingFromRemoteResponse(
                baseConfigList = responseCheckLicense.baseConfig,
                baseConfig = selectedBaseconfig,
            )?.licences?.isEmpty() != false
        ) {
            dataViewModel.saveSelectedBaseConfig("")
            navigate(BaseConfigRoute)
            return@LaunchedEffect
        }

        if (authViewModel.getSelectedBaseConfingFromRemoteResponse(
                baseConfigList = responseCheckLicense.baseConfig,
                baseConfig = selectedBaseconfig,
            )?.licences?.first()?.activat != "true"
        ) {
            dataViewModel.saveSelectedBaseConfig("")
            navigate(BaseConfigRoute)
            return@LaunchedEffect
        }




        if (!dataViewModel.getIsLoggedIn()) {
            navigate(LoginRoute)
            return@LaunchedEffect
        }
        if (authViewModel.getLoginHeader() == HeaderStatus.EMPTY.header) {
            showToast(
                context = context,
                toaster = toaster,
                message = context.resources.getString(R.string.erreurLogin) + "\n"+ HeaderStatus.EMPTY.header,
                type =  ToastType.Error,
            )

            navigate(BaseConfigRoute)
            return@LaunchedEffect
        }

       // BASE_URL = String.format(validateBaseUrl(selectedBaseconfig), selectedBaseconfig.adresse_ip, selectedBaseconfig.port)

        if(utilisateur != Utilisateur()) {
            authViewModel.postLogin(
                baseConfig = selectedBaseconfig,
                utilisateur =
                Utilisateur(
                    Login = utilisateur.Login,
                    Passe = utilisateur.Passe,
                    //  Type_user = "Vendeur"
                ),
                getData = false,
            )
        } else {
            navigate(LoginRoute)
        }


        if (authViewModel.getLoginHeader() == HeaderStatus.PROCAISSE_AND_INVENTORY.header ||
            authViewModel.getLoginHeader() == HeaderStatus.PROCAISSE.header
        ) {
            navigate(ProcaisseGraph)

           // return@LaunchedEffect
        } else if (authViewModel.getLoginHeader() == HeaderStatus.INVENTORY.header) {
            navigate(InventoryGraph)

           // return@LaunchedEffect
        }
    }



    Scaffold { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFF6366F1), // Indigo
                            Color(0xFF8B5CF6), // Purple
                            Color(0xFFEC4899)  // Pink
                        )
                    )
                )
                .padding(padding)
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Animated logo with pulse effect
                val scale by animateFloatAsState(
                    targetValue = 1f,
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessLow
                    )
                )

                val alpha by animateFloatAsState(
                    targetValue = 1f,
                    animationSpec = tween(1500)
                )

                // Logo container with glassmorphism effect
                Box(
                    modifier = Modifier
                        .scale(scale)
                        .background(
                            Color.White.copy(alpha = 0.2f),
                            RoundedCornerShape(24.dp)
                        )
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    SubcomposeAsyncImage(
                        model = mainViewModel.logo,
                        contentDescription = "logo",
                        modifier = Modifier
                            .height(100.dp)
                            .fillMaxWidth(0.6f),
                        loading = {
                            LottieAnim(
                                lotti = R.raw.loading,
                                size = 100.dp
                            )
                        },
                        error = {
                            Image(
                                painter = painterResource(id = R.drawable.ic_asm),
                                contentDescription = "",
                                modifier = Modifier.scale(scale)
                            )
                        }
                    )
                }

                Spacer(modifier = Modifier.height(40.dp))

                // App name with fade-in animation
                Text(
                    text = "ProCaisse Mobility",
                    style = MaterialTheme.typography.headlineLarge.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = Color.White,
                    modifier = Modifier.alpha(alpha)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Subtitle
                Text(
                    text = "Gestion Commerciale Mobile",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White.copy(alpha = 0.8f),
                    modifier = Modifier.alpha(alpha)
                )

                Spacer(modifier = Modifier.height(60.dp))

                // Status animation
                if (!networkViewModel.isConnected) {
                    LottieAnim(
                        lotti = R.raw.no_connection,
                        size = 120.dp
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Vérification de la connexion...",
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color.White.copy(alpha = 0.9f)
                    )
                } else {
                    LottieAnim(
                        lotti = R.raw.licence_check,
                        size = 120.dp
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    LetterByLetterAnimatedText(
                        text = "Initialisation...",
                        color = Color.White,
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    }
}

fun navigateTo(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    dataViewModel: DataViewModel,
    selectedBaseconfig: BaseConfig,
    from: String,
) {
    val destination =
        if (selectedBaseconfig != BaseConfig()) {
            if (dataViewModel.getIsLoggedIn()) {
                if (dataViewModel.getIsProcaisseLicenseSelected()) {
                    ProcaisseGraph
                } else {
                    InventoryGraph
                }
            } else {
                LoginRoute
            }
        } else {
            BaseConfigRoute
        }

    if (destination == InventoryGraph || destination == ProcaisseGraph) {
        navigatePopUpTo(destination, AuthGraph, true)
    }
    else navigate(destination)
      
}

@Composable
fun ShowMsgThenNavigate(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    destination: Any,
    showLoading: Boolean = false,
    msg: String = "",
    resetState: () -> Unit = {},
) {
    var show by remember { mutableStateOf(true) }

    LaunchedEffect(key1 = Unit) {
        delay(3000)
        show = false
    }

    if (show) {
        Text(
            text = msg,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            fontSize = 25.sp,
        )
        Spacer(modifier = Modifier.padding(top = 12.dp))
        if (showLoading) LoadingAnimation()
    } else {
        resetState()
        LaunchedEffect(key1 = Unit) {
            if (destination == InventoryGraph || destination == ProcaisseGraph) {
                navigatePopUpTo(destination, AuthGraph, true)
            }
            else navigate(destination)
        }
    }
}

@Composable
private fun AnimatedSplashContent(
    padding: PaddingValues,
    mainViewModel: MainViewModel,
    networkViewModel: NetworkViewModel,
    settingViewModel: SettingViewModel
) {
    var currentStep by remember { mutableIntStateOf(0) }
    val animationDuration = 2000L

    // Auto-advance through steps
    LaunchedEffect(Unit) {
        while (currentStep < 3) {
            delay(animationDuration)
            currentStep++
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        MaterialTheme.colorScheme.secondary.copy(alpha = 0.1f),
                        MaterialTheme.colorScheme.tertiary.copy(alpha = 0.1f)
                    )
                )
            )
            .padding(padding)
    ) {
            AnimatedContent(
                targetState = currentStep,
                transitionSpec = {
                    slideInHorizontally(
                        initialOffsetX = { it },
                        animationSpec = tween(800)
                    ) togetherWith slideOutHorizontally(
                        targetOffsetX = { -it },
                        animationSpec = tween(800)
                    )
                },
                modifier = Modifier.fillMaxSize()
            ) { step ->
                when (step) {
                    0 -> SplashStep1(mainViewModel)
                    1 -> SplashStep2()
                    2 -> SplashStep3()
                    else -> SplashStep4(networkViewModel)
                }
        }
    }
}

@Composable
private fun SplashStep1(mainViewModel: MainViewModel) {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Logo with scale animation
        val scale by animateFloatAsState(
            targetValue = 1f,
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessLow
            )
        )

        SubcomposeAsyncImage(
            model = mainViewModel.logo,
            contentDescription = "logo",
            modifier = Modifier
                .scale(scale)
                .padding(18.dp)
                .background(
                    MaterialTheme.colorScheme.primaryContainer,
                    RoundedCornerShape(16.dp)
                )
                .padding(24.dp)
                .height(120.dp)
                .fillMaxWidth(0.7f),
            loading = { LottieAnim(lotti = R.raw.loading, size = 120.dp) },
            error = {
                Image(
                    painter = painterResource(id = R.drawable.ic_asm),
                    contentDescription = "",
                    modifier = Modifier.scale(scale)
                )
            }
        )

        Spacer(modifier = Modifier.height(32.dp))

        LetterByLetterAnimatedText(
            text = "ProCaisse Mobility",
            color = MaterialTheme.colorScheme.primary,
            style = MaterialTheme.typography.headlineMedium.copy(
                fontWeight = FontWeight.Bold
            )
        )
    }
}

@Composable
private fun SplashStep2() {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        LottieAnim(
            lotti = R.raw.homeshop,
            size = 300.dp,
            iterations = 1
        )

        Spacer(modifier = Modifier.height(32.dp))

        LetterByLetterAnimatedText(
            text = "Gestion Commerciale",
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.SemiBold
            )
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Gérez vos ventes, stocks et clients",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = 32.dp)
        )
    }
}

@Composable
private fun SplashStep3() {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        LottieAnim(
            lotti = R.raw.sync_data,
            size = 300.dp,
            iterations = 1
        )

        Spacer(modifier = Modifier.height(32.dp))

        LetterByLetterAnimatedText(
            text = "Synchronisation",
            color = MaterialTheme.colorScheme.tertiary,
            style = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.SemiBold
            )
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Synchronisez vos données en temps réel",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = 32.dp)
        )
    }
}

@Composable
private fun SplashStep4(networkViewModel: NetworkViewModel) {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (!networkViewModel.isConnected) {
            LottieAnim(lotti = R.raw.no_connection, size = 250.dp)

            Spacer(modifier = Modifier.height(24.dp))

            Text(
                text = "Vérification de la connexion...",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.error
            )
        } else {
            LottieAnim(lotti = R.raw.licence_check, size = 250.dp)

            Spacer(modifier = Modifier.height(24.dp))

            LetterByLetterAnimatedText(
                text = "Vérification des licences...",
                color = MaterialTheme.colorScheme.primary,
                style = MaterialTheme.typography.titleMedium
            )
        }
    }
}
