package com.asmtunis.procaisseinventory.shared_ui_components.session_management

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.LockOpen
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionActivation
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.view_model.MainViewModel

@Composable
fun SessionManagementButton(
    currentSession: SessionCaisse,
    utilisateur: Utilisateur,
    baseConfig: BaseConfig,
    mainViewModel: MainViewModel,
    modifier: Modifier = Modifier,
    onSessionStateChanged: () -> Unit = {}
) {
    var showOpenDialog by remember { mutableStateOf(false) }
    var showCloseDialog by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    // Determine session state
    val isSessionOpen = currentSession.sCIdSCaisse.isNotEmpty() && currentSession.sCClotCaisse == 0
    val isSessionClosed = currentSession.sCIdSCaisse.isNotEmpty() && currentSession.sCClotCaisse == 1
    val hasNoSession = currentSession.sCIdSCaisse.isEmpty()

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        when {
            isSessionOpen -> {
                // Show Close Session button
                Button(
                    onClick = { showCloseDialog = true },
                    enabled = !isLoading,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    ),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.Lock,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = stringResource(R.string.close_session),
                        fontWeight = FontWeight.Medium
                    )
                }
                
                // Session info
                Text(
                    text = "Session: ${currentSession.sCIdSCaisse}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            isSessionClosed || hasNoSession -> {
                // Show Open Session button
                Button(
                    onClick = { showOpenDialog = true },
                    enabled = !isLoading,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    ),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.LockOpen,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = stringResource(R.string.open_session),
                        fontWeight = FontWeight.Medium
                    )
                }
                
                if (isSessionClosed) {
                    Text(
                        text = "Session fermée",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                } else {
                    Text(
                        text = "Aucune session",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // Show loading indicator
        if (isLoading) {
            Spacer(modifier = Modifier.height(8.dp))
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                strokeWidth = 2.dp
            )
        }

        // Show error message
        errorMessage?.let { error ->
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = error,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error
            )
        }
    }

    // Open Session Dialog
    if (showOpenDialog) {
        OpenSessionDialog(
            utilisateur = utilisateur,
            onConfirm = { sessionActivation ->
                isLoading = true
                errorMessage = null
                
                mainViewModel.openSession(
                    baseConfig = baseConfig,
                    sessionActivation = sessionActivation,
                    onSuccess = {
                        isLoading = false
                        showOpenDialog = false
                        onSessionStateChanged()
                    },
                    onError = { error ->
                        isLoading = false
                        errorMessage = error
                    }
                )
            },
            onDismiss = { 
                showOpenDialog = false
                errorMessage = null
            }
        )
    }

    // Close Session Dialog
    if (showCloseDialog) {
        CloseSessionDialog(
            currentSession = currentSession,
            onConfirm = {
                isLoading = true
                errorMessage = null
                
                mainViewModel.closeSessionCaisse(
                    baseConfig = baseConfig,
                    sessionCaisse = currentSession
                )
                
                // Update local session state immediately
                mainViewModel.updateSessionAfterClose(currentSession)
                
                isLoading = false
                showCloseDialog = false
                onSessionStateChanged()
            },
            onDismiss = { 
                showCloseDialog = false
                errorMessage = null
            }
        )
    }
}

@Composable
private fun OpenSessionDialog(
    utilisateur: Utilisateur,
    onConfirm: (SessionActivation) -> Unit,
    onDismiss: () -> Unit
) {
    var fondCaisse by remember { mutableStateOf("0.0") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(text = stringResource(R.string.open_session))
        },
        text = {
            Column {
                Text("Ouvrir une nouvelle session de caisse")
                Spacer(modifier = Modifier.height(16.dp))
                
                OutlinedTextField(
                    value = fondCaisse,
                    onValueChange = { fondCaisse = it },
                    label = { Text("Fond de caisse") },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val sessionActivation = SessionActivation(
                        utilisateur = utilisateur.codeUt.toString(),
                        station = utilisateur.Station,
                        fondCaisse = fondCaisse,
                        caisse = "1", // Default caisse
                        carnet = "1", // Default carnet
                        nomMachine = "Mobile"
                    )
                    onConfirm(sessionActivation)
                }
            ) {
                Text(stringResource(R.string.open))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        }
    )
}

@Composable
private fun CloseSessionDialog(
    currentSession: SessionCaisse,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(text = stringResource(R.string.close_session))
        },
        text = {
            Text("Êtes-vous sûr de vouloir fermer la session ${currentSession.sCIdSCaisse} ?")
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text(stringResource(R.string.close))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        }
    )
}
