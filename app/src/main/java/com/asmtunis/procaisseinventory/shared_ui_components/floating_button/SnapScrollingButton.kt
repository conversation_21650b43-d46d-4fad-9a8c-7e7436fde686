package com.asmtunis.procaisseinventory.shared_ui_components.floating_button

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardDoubleArrowUp
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.CustomIcons
import kotlinx.coroutines.launch

@Composable
fun SnapScrollingButton(isScrollInProgress: Boolean, isVisible: Boolean, density: Density, animateScrollToItem: suspend (Int) -> Unit) {
    val scope = rememberCoroutineScope()
    AnimatedVisibility(
        // modifier = modifier,
        visible = isVisible,
        enter = slideInVertically {
            with(density) { 40.dp.roundToPx() }
        } + fadeIn(),
        exit = fadeOut(
            animationSpec = keyframes {
                this.durationMillis = 120
            }
        )
    ) {


        FloatingActionButton(

            shape = if(!isScrollInProgress) FloatingActionButtonDefaults.extendedFabShape else FloatingActionButtonDefaults.shape ,
            onClick = {
                scope.launch {
                    animateScrollToItem(0)
                }
            }
        ) {
            CustomIcons.BusinessIcon(
                iconRes = CustomIcons.UpArrow,
                contentDescription = stringResource(id = R.string.add_Client_button),
                size = 24.dp
            )
        }
    }
}