package com.asmtunis.procaisseinventory.shared_ui_components.buttons

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.RunningWithErrors
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.BuildConfig
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.navigation.NetworkErrorRoute
import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError

@Composable
fun ErrorGetDataButton(
    navigate: (route: Any) -> Unit,
    networkErrorsList: List<NetworkError>
) {
    val density = LocalDensity.current

    // Only show error button for developers (debug builds)
    AnimatedVisibility(
        // modifier = modifier,
        visible = BuildConfig.DEBUG && networkErrorsList.isNotEmpty(),
        enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
        exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
    ) {

        OutlinedButton(
            border = BorderStroke(width = 1.dp, color = MaterialTheme.colorScheme.error),
            onClick = { navigate(NetworkErrorRoute) }
        ) {
            Icon(
                imageVector = Icons.TwoTone.RunningWithErrors,
                contentDescription = stringResource(id = R.string.sync_title),
                modifier = Modifier.size(AssistChipDefaults.IconSize),
                tint = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.width(9.dp))
            Text(
                text = stringResource(id = R.string.networkError) + " "+ networkErrorsList.size.toString(),
                color = MaterialTheme.colorScheme.error
            )
        }

    }
}