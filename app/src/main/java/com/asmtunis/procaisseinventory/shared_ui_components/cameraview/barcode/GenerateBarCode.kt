package com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode

import android.graphics.Bitmap
import android.graphics.Color
import android.util.Log
import androidx.core.graphics.createBitmap
import androidx.core.graphics.set
import com.google.zxing.BarcodeFormat
import com.google.zxing.MultiFormatWriter
import com.google.zxing.WriterException


fun generateBarCode(barcod: BareCode): Bitmap {
    val width = 1400
    val height = 350
    val bitmap = createBitmap(width, height)
    val codeWriter = MultiFormatWriter()

    try {
        // Handle ITF even-length requirement
        var valueToEncode = barcod.value
        if (barcod.barecodeformat == BarcodeFormat.ITF) {
            if (valueToEncode.length % 2 != 0) {
                // Pad with a leading zero for odd-length ITF
                valueToEncode = "0$valueToEncode"
            }
        }

        val bitMatrix = codeWriter.encode(
            valueToEncode, // Use modified value
            barcod.barecodeformat,
            width,
            height
        )

        for (x in 0 until width) {
            for (y in 0 until height) {
                val color = if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
                bitmap[x, y] = color
            }
        }
    } catch (e: WriterException) {
    }

    return bitmap
}

fun getBarcodeFormat(barcodeFormat: Int): BarcodeFormat {

    return when (barcodeFormat) {
        1 -> BarcodeFormat.CODE_128
        2 -> BarcodeFormat.CODE_39
        4 -> BarcodeFormat.CODE_93
        8 -> BarcodeFormat.CODABAR
        16 -> BarcodeFormat.DATA_MATRIX
        32 -> BarcodeFormat.EAN_13
        64 -> BarcodeFormat.EAN_8
        128 -> BarcodeFormat.ITF
        256 -> BarcodeFormat.QR_CODE
        512 -> BarcodeFormat.UPC_A
        1024 -> BarcodeFormat.UPC_E
        //2048->BarcodeFormat.PDF417
        4096 -> BarcodeFormat.AZTEC
        else -> {
            BarcodeFormat.CODE_128
        }
    }


}



fun getBarcodeFormat(barcodeString: String): BarcodeFormat {
    return when {
        barcodeString.matches(Regex("[0-9]{12}")) -> BarcodeFormat.UPC_A // 12 digits
        barcodeString.matches(Regex("[0-9]{13}")) -> BarcodeFormat.EAN_13 // 13 digits
        barcodeString.matches(Regex("[0-9]{8}")) -> BarcodeFormat.EAN_8 // 8 digits
        barcodeString.matches(Regex("[0-9]{6,8}")) -> BarcodeFormat.UPC_E // 6-8 digits, typically for UPC-E
        barcodeString.matches(Regex("[0-9]{6,14}")) -> BarcodeFormat.ITF // ITF, typically between 6 and 14 digits
        barcodeString.matches(Regex("[A-Z0-9]+")) -> BarcodeFormat.CODE_39 // Code 39, alphanumeric characters
        barcodeString.matches(Regex("[0-9A-Z\\-.*$/+%]+")) -> BarcodeFormat.CODE_128 // Code 128
        barcodeString.matches(Regex("[A-Z0-9]+")) -> BarcodeFormat.CODE_93 // Code 93, alphanumeric characters
        barcodeString.matches(Regex("[0-9]+")) -> BarcodeFormat.CODABAR // Codabar, numeric characters
        barcodeString.matches(Regex("[0-9A-Z]+")) -> BarcodeFormat.DATA_MATRIX // Data Matrix, alphanumeric characters
        barcodeString.matches(Regex("[A-Z0-9]+")) -> BarcodeFormat.QR_CODE // QR Code, alphanumeric characters
        barcodeString.matches(Regex("[A-Z0-9]+")) -> BarcodeFormat.AZTEC // Aztec, alphanumeric characters
        else -> BarcodeFormat.CODE_128 // Unknown format
    }
}
