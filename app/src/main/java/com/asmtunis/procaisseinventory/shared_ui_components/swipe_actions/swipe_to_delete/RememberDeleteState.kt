package com.asmtunis.procaisseinventory.shared_ui_components.swipe_actions.swipe_to_delete
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SwipeToDismissBoxState
import androidx.compose.material3.SwipeToDismissBoxValue
import androidx.compose.material3.rememberSwipeToDismissBoxState
import androidx.compose.runtime.Composable

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun rememberDeleteState(
    onDelete: () -> Unit
): SwipeToDismissBoxState {

    return rememberSwipeToDismissBoxState(
        confirmValueChange = {
            if (it == SwipeToDismissBoxValue.EndToStart || it == SwipeToDismissBoxValue.StartToEnd) {
                onDelete()
                // Return false to prevent immediate state commitment
                // The state will be managed by the confirmation dialog
                false
            } else false
        },
        positionalThreshold = {
           it - 0.1f
        },
        // positionalThreshold = SwipeToDismissBoxDefaults.positionalThreshold
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun rememberDeleteState(
    onDelete: () -> Unit,
    onSwipeStateChange: (SwipeToDismissBoxState) -> Unit
): SwipeToDismissBoxState {

    val state = rememberSwipeToDismissBoxState(
        confirmValueChange = {
            if (it == SwipeToDismissBoxValue.EndToStart || it == SwipeToDismissBoxValue.StartToEnd) {
                onDelete()
                // Return false to prevent immediate state commitment
                // The state will be managed by the confirmation dialog
                false
            } else false
        },
        positionalThreshold = {
           it - 0.1f
        },
        // positionalThreshold = SwipeToDismissBoxDefaults.positionalThreshold
    )

    // Register the state for tracking
    onSwipeStateChange(state)

    return state
}





