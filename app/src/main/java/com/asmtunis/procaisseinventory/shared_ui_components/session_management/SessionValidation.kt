package com.asmtunis.procaisseinventory.shared_ui_components.session_management

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse

/**
 * Component that validates session state and shows appropriate messages
 * Prevents actions when session is closed or unavailable
 */
@Composable
fun SessionValidationWrapper(
    currentSession: SessionCaisse,
    content: @Composable () -> Unit
) {
    val isSessionOpen = currentSession.sCIdSCaisse.isNotEmpty() && currentSession.sCClotCaisse == 0
    val isSessionClosed = currentSession.sCIdSCaisse.isNotEmpty() && currentSession.sCClotCaisse == 1
    val hasNoSession = currentSession.sCIdSCaisse.isEmpty()

    when {
        isSessionOpen -> {
            // Session is open, show content
            content()
        }
        
        isSessionClosed -> {
            // Session is closed, show warning
            SessionClosedWarning()
        }
        
        hasNoSession -> {
            // No session available, show warning
            NoSessionWarning()
        }
    }
}

@Composable
private fun SessionClosedWarning() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "Session fermée",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.error,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "La session de caisse est fermée. Vous devez ouvrir une nouvelle session pour effectuer des opérations.",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun NoSessionWarning() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "Aucune session",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Aucune session de caisse n'est disponible. Vous devez ouvrir une session pour effectuer des opérations.",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Utility function to check if actions are allowed
 */
fun canPerformActions(sessionCaisse: SessionCaisse): Boolean {
    return sessionCaisse.sCIdSCaisse.isNotEmpty() && sessionCaisse.sCClotCaisse == 0
}

/**
 * Utility function to get session status message
 */
fun getSessionStatusMessage(sessionCaisse: SessionCaisse): String {
    return when {
        sessionCaisse.sCIdSCaisse.isEmpty() -> "Aucune session"
        sessionCaisse.sCClotCaisse == 1 -> "Session fermée"
        sessionCaisse.sCClotCaisse == 0 -> "Session ouverte"
        else -> "État inconnu"
    }
}

/**
 * Component that shows session status in a compact format
 */
@Composable
fun SessionStatusIndicator(
    currentSession: SessionCaisse,
    modifier: Modifier = Modifier
) {
    val isSessionOpen = currentSession.sCIdSCaisse.isNotEmpty() && currentSession.sCClotCaisse == 0
    val statusMessage = getSessionStatusMessage(currentSession)
    
    val backgroundColor = when {
        isSessionOpen -> MaterialTheme.colorScheme.primaryContainer
        currentSession.sCClotCaisse == 1 -> MaterialTheme.colorScheme.errorContainer
        else -> MaterialTheme.colorScheme.surfaceVariant
    }
    
    val textColor = when {
        isSessionOpen -> MaterialTheme.colorScheme.onPrimaryContainer
        currentSession.sCClotCaisse == 1 -> MaterialTheme.colorScheme.onErrorContainer
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }

    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp),
        color = backgroundColor
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = if (isSessionOpen) Color.Green else Color.Red,
                        shape = RoundedCornerShape(4.dp)
                    )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = statusMessage,
                style = MaterialTheme.typography.bodySmall,
                color = textColor,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
