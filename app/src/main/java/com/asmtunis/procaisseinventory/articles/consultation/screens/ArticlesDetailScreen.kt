@file:OptIn(ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.articles.consultation.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Addchart
import androidx.compose.material.icons.filled.PriceCheck
import androidx.compose.material.icons.filled.ProductionQuantityLimits
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material.icons.filled.RememberMe
import androidx.compose.material.icons.twotone.AddCircleOutline
import androidx.compose.material.icons.twotone.DeleteOutline
import androidx.compose.material.icons.twotone.Equalizer
import androidx.compose.material.icons.twotone.Print
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import com.asmtunis.procaisseinventory.core.authorization.StationAccessControl
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ArticleTextValidationViewModel
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.OPERATEUR_PATRIMOINE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringPlural
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens.TicketRayonItem
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens.TicketRayonViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column.ThreeColumnTable
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.edit_text.EditTextField
import com.simapps.ui_kit.utils.getCurrentTime
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ArticlesDetailScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    networkViewModel: NetworkViewModel,
    articlesViewModel: ArticlesViewModel,
    ticketRayonViewModel: TicketRayonViewModel,
    articleTxtValidViewModel: ArticleTextValidationViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    mainViewModel: MainViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    sunmiPrintManager: SunmiPrintManager
) {

    val context = LocalContext.current
    val scrollState = rememberScrollState()
    val article = articlesViewModel.currentArticle

    val listUnite = mainViewModel.uniteList
    val from = articlesViewModel.from

    val unite = listUnite.firstOrNull { it.uNICode == article.uNITEARTICLECodeUnite }?.uNIDesignation?: " code Unité: ${article.uNITEARTICLECodeUnite}"

    val scope = rememberCoroutineScope()
    val density = LocalDensity.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    val showAlertDialog = articlesViewModel.showAlertDialog
    val printParams = dataViewModel.printData

    val proInventoryAuthorizationList = getProInventoryDataViewModel.authorizationList
    val proCaisseAuthorizationList = getProCaisseDataViewModel.authorizationList

    val haveTicketRayonAuthorisation = proInventoryAuthorizationList.any { it.AutoCodeAu == AuthorizationValuesProInventory.TICKET_RAYON_DRAWER_ITEM }


    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val utilisateur = mainViewModel.utilisateur
    val isAdmin = utilisateur.typeUser.lowercase().contains("admin")

    // Check if user should have station-based access control
    val shouldFilterByUserStation = StationAccessControl.shouldFilterStockByUserStation(
        utilisateur = utilisateur,
        autorisationList = proCaisseAuthorizationList
    )

    val typeUtilisateur = utilisateur.typeUser
    val isOperateurPatrimoine = typeUtilisateur == OPERATEUR_PATRIMOINE


    val stationStockArtWithStation = articlesViewModel.stationStockArticleWithStationList


    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)




     val qteStation = article.sARTQte.toString() // for proinventory
    //  val qteStation = article.aRTQteStock
  //   val qteStation = stationStockArtWithStation.firstOrNull { it.stationStockArticle?.sARTCodeSatation == utilisateur.Station }?.stationStockArticle?.sARTQteStation?: article.sARTQte.toString()


    LaunchedEffect(key1 = Unit) {
        if(printViewModel.proccedPrinting && printViewModel.deviceAddress.isNotEmpty()) {

            // Use PrintFunctions to handle all printing methods consistently
            PrintFunctions.print(
                context = context,
                toaster = toaster,
                printParams = printParams,
                navigate = { navigate(it) },
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                sunmiPrintManager = sunmiPrintManager,
                toPrintBT = {
                    printViewModel.printTicketRayon(
                        context = context,
                        article = article,
                        printParams = printParams
                    )
                },
                toPrintWifi = {
                    // TODO: Add WiFi printing for Ticket Rayon
                },
                toPrintSunmi = {
                    // TODO: Add Sunmi printing for Ticket Rayon
                }
            )
        }

        // Load stock data based on user permissions
        if (shouldFilterByUserStation && utilisateur.Station.isNotEmpty()) {
            // User is restricted to their station - show only their station's stock
            articlesViewModel.getStationStockArticleWithStationListForUserStation(
                codeArticle = article.aRTCode,
                userStation = utilisateur.Station
            )
        } else {
            // User has full access - show all stations' stock
            articlesViewModel.getStationStockArticleWithStationList(codeArticle = article.aRTCode)
        }
    }



    Scaffold(
        topBar = {
            AppBar(
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = article.aRTDesignation,
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                actions = {
                    if(!article.isSync) {
                        IconButton(
                            onClick = {
                                articlesViewModel.onShowAlertDialogChange(true)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.DeleteOutline,
                                contentDescription = stringResource(id = R.string.icn_search_clear_content_description)
                            )
                        }
                    }

                }
            )
        }

    ) { padding ->
        if(articlesViewModel.showStocksByStation) {
            ModalBottomSheet(
                sheetState = sheetState,
                onDismissRequest = {
                    scope.launch { sheetState.hide() }
                    articlesViewModel.onShowStocksByStationChange(false)
                },
            ){
                Spacer(modifier = Modifier.height(12.dp))
                ThreeColumnTable(
                    article = article,
                    stationStockArtWithStation = stationStockArtWithStation
                )



                Spacer(modifier = Modifier.height(50.dp))

            }
        }

        CustomAlertDialogue(
            title = stringResource(id = R.string.delete_confirmation_msg),
            msg = "",
            openDialog = showAlertDialog,
            setDialogueVisibility = {
                articlesViewModel.onShowAlertDialogChange(it)
            },
            customAction = {
                articlesViewModel.onShowAlertDialogChange(false)
                articlesViewModel.deleteByArtCode(article)
                popBackStack()


            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non),
            negatifAction  = {
                articlesViewModel.onShowAlertDialogChange(false)
            }
        )

        if (printViewModel.openPrintInfoDialogue) {
            BluetoothInfoDialogue(
                printResult = printViewModel.printResult,
                onOpenPrintInfoDialogueChange = {
                    printViewModel.onOpenPrintInfoDialogueChange(it)
                },
            )
        }

        if (ticketRayonViewModel.showBottomSheet) {
            ModalBottomSheet(
                sheetState = sheetState,
                onDismissRequest = {
                    scope.launch { sheetState.hide() }
                    ticketRayonViewModel.bottomSheetVisibility(false)
                },
            ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth()
                ) {

                    Spacer(Modifier.size(16.dp))
                    IconButton(
                        modifier = Modifier.fillMaxWidth(),
                        onClick = {
                            val ticktRayon = TicketRayon(
                                aRTCode = article.aRTCode,
                                aRTDesignation = article.aRTDesignation,
                                ddm = getCurrentTime(),
                                artSync = "false",
                                timestamp = 0
                            )
                            ticktRayon.isSync = false
                            ticktRayon.status = ItemStatus.INSERTED.status
                            ticketRayonViewModel.saveTicketRayon(ticktRayon)

                            ticketRayonViewModel.bottomSheetVisibility(false)

                            showToast(
                                context = context,
                                toaster = toaster,
                                message = article.aRTDesignation + " (${article.aRTCode})"
                                        + "\n"+ context.resources.getString(R.string.ticket_rayon_add_succ),
                                type =  ToastType.Success,
                            )

                        }) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Start
                        ) {
                            Icon(
                                modifier = Modifier.size(36.dp),
                                imageVector = Icons.TwoTone.AddCircleOutline,
                                contentDescription = context.resources.getString(R.string.add)
                            )
                            Spacer(modifier = Modifier.width(width = 8.dp))
                            Text(context.resources.getString(R.string.add))
                        }
                    }
                    Spacer(Modifier.size(12.dp))
                    IconButton(
                        modifier = Modifier.fillMaxWidth(),
                        onClick = {

                            // Use PrintFunctions to handle all printing methods consistently
                            PrintFunctions.print(
                                context = context,
                                toaster = toaster,
                                printParams = printParams,
                                navigate = { navigate(it) },
                                printViewModel = printViewModel,
                                bluetoothVM = bluetoothVM,
                                sunmiPrintManager = sunmiPrintManager,
                                toPrintBT = {
                                    printViewModel.printTicketRayon(
                                        context = context,
                                        article = article,
                                        printParams = printParams
                                    )
                                },
                                toPrintWifi = {
                                    // TODO: Add WiFi printing for Ticket Rayon
                                },
                                toPrintSunmi = {
                                    // TODO: Add Sunmi printing for Ticket Rayon
                                }
                            )

                            ticketRayonViewModel.bottomSheetVisibility(false)
                        }
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Start) {
                            Icon(
                                modifier = Modifier.size(36.dp),
                                imageVector = Icons.TwoTone.Print,
                                contentDescription = stringResource(R.string.print)
                            )
                            Spacer(modifier = Modifier.width(width = 8.dp))
                            Text(stringResource(R.string.print))
                        }

                    }

                    Spacer(Modifier.size(20.dp))
                }
            }
        }



        Column(
            modifier = Modifier
                .padding(padding)
                .verticalScroll(scrollState)
                // .fillMaxSize()
                .fillMaxWidth()
                .wrapContentHeight()
                .wrapContentSize(Alignment.Center),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            EditTextField(
                text = article.aRTDesignation,
                label =stringResource(R.string.designation),
                onValueChange = {},
                readOnly = true,
                enabled = true,
                leadingIcon = Icons.Default.Addchart,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )




    EditTextField(
        text = article.aRTCodeBar,
        label = stringResource(R.string.bar_code),
        onValueChange = {},
        readOnly = true,
        enabled = true,
        leadingIcon = Icons.Default.QrCodeScanner,
        keyboardType = KeyboardType.Text,
        imeAction = ImeAction.Next
    )






                EditTextField(
                    text = article.aRTCode,
                    label = stringResource(R.string.code_title),
                    onValueChange = {},
                    readOnly = true,
                    enabled = true,
                    leadingIcon = Icons.Default.RememberMe,
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Next
                )


            if (!isOperateurPatrimoine) {
                EditTextField(
                    text =StringUtils.convertStringToPriceFormat(article.aRTPrixUnitaireHT),
                    label = stringResource(R.string.purchase_price_title),
                    onValueChange = {},
                    readOnly = true,
                    enabled = true,
                    leadingIcon = Icons.Default.PriceCheck,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )


                EditTextField(
                    text =StringUtils.convertStringToPriceFormat((stringToDouble(article.aRTPrixUnitaireHT) * (1+ (article.aRTTVA/100))).toString()),
                    label = stringResource(R.string.price_achat_dt),
                    onValueChange = {},
                    readOnly = true,
                    enabled = true,
                    leadingIcon = Icons.Default.PriceCheck,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )

                if (stringToDouble(article.tauxSolde)>0.0 && article.tauxSolde != null)
                    EditTextField(
                        text = removeTrailingZeroInDouble(article.tauxSolde?: "0.0")+ " %",
                        label = stringResource(R.string.taux_sold),
                        onValueChange = {},
                        readOnly = true,
                        enabled = true,
                        leadingIcon = Icons.Default.ProductionQuantityLimits,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next
                    )


                EditTextField(
                  //  text = StringUtils.convertStringToPriceFormat(article.artPrixPublique),
                    text = StringUtils.convertStringToPriceFormat(articlesViewModel.getPrice(article)),
                    label = stringResource(R.string.price_title) + " (${articlesViewModel.selectedPriceCategory})",
                    onValueChange = {},
                    readOnly = true,
                    enabled = true,
                    leadingIcon = Icons.Default.PriceCheck,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )
            }
            else {
                EditTextField(
                    text = article.fAMLib?: "N/A",
                    label = stringResource(R.string.famille_title),
                    onValueChange = {},
                    readOnly = true,
                    enabled = true,
                    leadingIcon = Icons.Default.ProductionQuantityLimits,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )

                EditTextField(
                    text = article.mARDesignation?: "N/A",
                    label = stringResource(R.string.marque_title),
                    onValueChange = {},
                    readOnly = true,
                    enabled = true,
                    leadingIcon = Icons.Default.ProductionQuantityLimits,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )
            }




            if (!isOperateurPatrimoine)
                EditTextField(
                    text = removeTrailingZeroInDouble(qteStation).ifEmpty { "0" }+ " $unite",
                    label = stringResource(R.string.qte_per_station),
                    onValueChange = {},
                    readOnly = true,
                    enabled = true,
                    leadingIcon = Icons.Default.ProductionQuantityLimits,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next,

                    )



            if (from == Globals.PRO_INVENTORY)
                EditTextField(
                text = removeTrailingZeroInDouble(article.aRTQteStock).ifEmpty { "0" } + " $unite",
                label = stringResource(R.string.real_quantity_title),
                onValueChange = {},
                readOnly = true,
                enabled = true,
                leadingIcon = Icons.Default.ProductionQuantityLimits,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Next
            )


          Spacer(modifier = Modifier.height(16.dp))

            if(isAdmin && from == Globals.PRO_INVENTORY) {

                OutlinedCard(
                    modifier = Modifier.fillMaxWidth().padding(start = 12.dp, end = 12.dp),
                    shape = RoundedCornerShape(10.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 4.dp
                    )
                ) {
                    ItemDetail(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = if (shouldFilterByUserStation) {
                            stringResource(R.string.qte_in_selected_station, utilisateur.Station)
                        } else {
                            stringResource(R.string.qte_par_station)
                        },
                        dataText = if (shouldFilterByUserStation) {
                            // Show only user's station stock
                            val userStationStock = stationStockArtWithStation.firstOrNull()
                            val qtyText = userStationStock?.stationStockArticle?.sARTQteStation ?: "0"
                            "$qtyText ${article.uNITEARTICLECodeUnite ?: ""}"
                        } else {
                            // Show all stations count
                            stringPlural(
                                nbr = stationStockArtWithStation.map { it.station }.size,
                                single = stringResource(id = R.string.station),
                                plural = stringResource(id = R.string.stations)
                            )
                        },
                        icon = Icons.TwoTone.Equalizer,
                        onClick = {
                            articlesViewModel.onShowStocksByStationChange(true)
                        }
                    )
                }
            }



            Spacer(modifier = Modifier.height(16.dp))





            if (from == Globals.PRO_INVENTORY) {
                TicketRayonItem(
                    article = article,
                    ticketRayon = ticketRayonViewModel.selectedTicketRayon,
                    setBottomSheetVisibility = {
                        ticketRayonViewModel.bottomSheetVisibility(it)
                    },
                    setSelectedTicketRayon = {
                        ticketRayonViewModel.onSelectedTicketRayonChange(it)
                    },
                    haveTicketRayonAuthorisation = haveTicketRayonAuthorisation,
                )
            }


            Spacer(modifier = Modifier.height(16.dp))



        }
    }

}













