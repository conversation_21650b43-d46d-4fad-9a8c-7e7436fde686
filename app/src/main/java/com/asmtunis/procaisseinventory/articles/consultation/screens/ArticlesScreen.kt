@file:OptIn(ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.articles.consultation.screens

import NavDrawer
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridState
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Add
import androidx.compose.material.icons.twotone.HideImage
import androidx.compose.material.icons.twotone.ShoppingCart
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.getQuantity
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ArticleTextValidationViewModel
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE_MOBILITY
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory.ADD_ARTICLE
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ArticleType
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddArticlesRoute
import com.asmtunis.procaisseinventory.core.navigation.ArticlesDetailRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.BareCodeScannerIcon
import com.asmtunis.procaisseinventory.shared_ui_components.DataBasePaginationLoadState
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyVerticalStaggeredGrid
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import com.simapps.ui_kit.ModifiersUtils.roundedCornerShape
import kotlinx.coroutines.launch
import java.util.Locale
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu


@Composable
fun ArticlesScreen(
    navigate: (route: Any) -> Unit,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    settingViewModel: SettingViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    articlesViewModel: ArticlesViewModel,
    articleTxtValidViewModel: ArticleTextValidationViewModel,
    barCodeViewModel: BarCodeViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    mainViewModel: MainViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,

    from: String
    //  searchViewModel: SearchViewModel
) {
    val uiWindowState = settingViewModel.uiWindowState

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val utilisateur = mainViewModel.utilisateur
    val typeUtilisateur = utilisateur.typeUser
    val isConnected = networkViewModel.isConnected
    val parametrage = mainViewModel.parametrage

    val articlesListState = articlesViewModel.articlesListState
    val listOrder = articlesListState.listOrder
    val canAddArticle = getProInventoryDataViewModel.authorizationList.any { it.AutoCodeAu == ADD_ARTICLE } || getProCaisseDataViewModel.authorizationList.any { it.AutoCodeAu == AuthorizationValuesProCaisse.ADD_ARTICLE}

    val articlesState = getSharedDataViewModel.articlesState
    val articlesPaginationState = getSharedDataViewModel.articlesPaginationState
    val uniteList = mainViewModel.uniteList
   // val listStationStockArticl = mainViewModel.listStationStockArticl.filter { it.sARTCodeSatation == utilisateur.Station }

    val listFilter = articlesListState.filter
    val articleList: LazyPagingItems<Article>? = articlesListState.lists?.collectAsLazyPagingItems()

     val barCodeInfo = barCodeViewModel.barCodeInfo
    val searchTextState = articlesViewModel.searchTextState
    val isOperateurPatrimoine = typeUtilisateur == Globals.OPERATEUR_PATRIMOINE

    val filterList = if(isOperateurPatrimoine)
        context.resources.getStringArray(R.array.patimoine_filter)
    else context.resources.getStringArray(R.array.article_filter)

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val marqueList = mainViewModel.marqueList
    val showSearchView = articlesViewModel.showSearchView

    val listState = rememberLazyStaggeredGridState()

    // Price category functionality (obligatory)
    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList
    val haveChoosePriceCategoriAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.CHOOSE_PRICE_CATEGERI }

   val listUnite = mainViewModel.uniteList
   val listFamille = mainViewModel.listFamille

   val isVisible = floatingBtnIsVisible(
        listeSize = articleList?.itemCount?: 0,
        canScrollForward = listState.canScrollForward
    )

//    LaunchedEffect(
//        key1 = searchTextState.text,
//       // key2 = articlesListState,
//        key2 = articleList,
//        key3 = listFilter
//    ) {
//        Log.d("xxxxfilterArticles", "articleList "+ articleList.size)
//        articlesViewModel.filterArticles(articlesListState = articlesListState, stationId = "")
//    }
//    LaunchedEffect(key1 = articlesViewModel.stockArticleStock) {
//        articlesViewModel.filterArticles(articlesListState = articlesListState, stationId = "")
//    }


        LaunchedEffect(key1 = Unit) {
        android.util.Log.d("ArticlesScreen", "📦 Loading product data for articles screen...")
        mainViewModel.loadProductData()
        articlesViewModel.filterArticles(articlesListState = articlesListState)
    }


    LaunchedEffect(key1 = barCodeInfo) {
        if(barCodeInfo.value.isEmpty()) return@LaunchedEffect
        articlesViewModel.onEvent(event = ListEvent.ListSearch(ListSearch.SecondSearch()))
        articlesViewModel.onSearchValueChange(TextFieldValue(barCodeInfo.value))
    }



    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        mainViewModel = mainViewModel,
        navDrawerViewmodel = navigationDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            snackbarHost = { SnackbarHost(snackbarHostState) },
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    titleVisibilty = !showSearchView && searchTextState.text.isEmpty(),
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    onNavigationClick = {
                        // Navigate to shortcuts screen instead of opening drawer
                        navigate(com.asmtunis.procaisseinventory.core.navigation.HomePageRoute)
                    },
                    title = stringResource(
                        id = if (from == PRO_CAISSE_MOBILITY) navigationDrawerViewModel.proCaisseSelectedMenu.title
                        else navigationDrawerViewModel.proInventorySelectedMenu.title
                    ),
                    //  showNavIcom = clientViewModel.searchTextState.text.isBlank(),
                    actions = {
                        if((articleList?.itemCount?:0) >0 || searchTextState.text.isNotEmpty()) {
                            SearchSectionComposable(
                                label = context.getString(R.string.filter_by,
                                    when (listFilter) {
                                        is ListSearch.FirstSearch -> filterList[0]
                                        is ListSearch.SecondSearch -> filterList[1]
                                        else -> filterList[2]} + "  (" + articleList?.itemCount + ")"


                                ),
                                searchVisibility  = showSearchView || searchTextState.text.isNotEmpty(),
                                searchTextState = searchTextState,
                                onSearchValueChange = {
                                    articlesViewModel.onSearchValueChange(TextFieldValue(it))

                                    if(it == "")   {
                                        /** this bloc to handle search visibility when custom search by client*/
                                        barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                                    }
                                },
                                onShowSearchViewChange = {
                                    articlesViewModel.onShowSearchViewChange(it)
                                    if(!it) {
                                        /** this bloc to handle search visibility when custom search by client*/
                                        //  articlesViewModel.onSearchValueChange(TextFieldValue(""))
                                        barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                                    }
                                },
                                onShowCustomFilterChange = {
                                    articlesViewModel.onShowCustomFilterChange(it)
                                }
                            )
                        }


                    }
                )
            },
            //    containerColor = colorResource(id = R.color.black),
            floatingActionButton = {
                val density = LocalDensity.current

                AnimatedVisibility(
                    visible = isVisible,
                    enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
                    exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
                ) {
                    Column(
                     verticalArrangement = Arrangement.Center,
                     horizontalAlignment  = Alignment.CenterHorizontally
                    ) {
                        if(barCodeViewModel.haveCameraDevice) {
                            FloatingActionButton(
                                onClick = {}
                            ) {
                                BareCodeScannerIcon(
                                    haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                                    toaster = toaster,
                                    onClick = {
                                        openBareCodeScanner(
                                            navigate = { navigate(it) },
                                            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                                        )
                                    }
                                )
                            }
                        }

                   Spacer(modifier = Modifier.height(12.dp))

                    if(canAddArticle) {
                        FloatingActionButton(
                            onClick = {
                                articlesViewModel.onCurrentArticleChange(Article())
                                articleTxtValidViewModel.resetVariable()
                                articlesViewModel.onModifyChange(true)
                                navigate(AddArticlesRoute)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.Add,
                                contentDescription = stringResource(id = R.string.add_Article_button)
                            )
                        }

                        Spacer(modifier = Modifier.height(12.dp))
                    }

                        SnapScrollingButton(
                            isScrollInProgress = listState.isScrollInProgress,
                            isVisible = listState.firstVisibleItemIndex > 15 && isVisible,
                            density = density,
                            animateScrollToItem = { listState.animateScrollToItem(index = it) }
                        )
                    }
                }
            }
        ) { padding ->
                if (articlesViewModel.showCustomFilter) {
                    FilterContainer(
                        filterList = filterList,
                        listFilter = listFilter,
                        listOrder = listOrder,
                        orderList = filterList,
                        onShowCustomFilterChange = { articlesViewModel.onShowCustomFilterChange(false) },
                        onEvent = { articlesViewModel.onEvent(event = it) },
                        customFilterContent = {
                            FilterSectionComposable(
                                title = stringResource(R.string.filter_by_famille),
                                currentFilterLable = articlesListState.filterByFamille,
                                onAllEvent = {
                                    articlesViewModel.onEvent(event = ListEvent.FirstCustomFilter(""))
                                },
                                onEvent = {
                                    articlesViewModel.onEvent(
                                     event = ListEvent.FirstCustomFilter(listFamille[it].fAMLib ?: ""))
                                },
                                filterCount = listFamille.size,
                                customFilterCode = { listFamille[it].fAMLib ?: "" },
                                filterLabel = { listFamille[it].fAMLib ?: listFamille[it].fAMDesgCourte }
                            )

                            FilterSectionComposable(
                                title = stringResource(R.string.filter_by_marque),
                                currentFilterLable = articlesListState.filterByMarque,
                                onAllEvent = { articlesViewModel.onEvent(event = ListEvent.SecondCustomFilter("")) },
                                onEvent = { articlesViewModel.onEvent(event = ListEvent.SecondCustomFilter(marqueList[it].mARDesignation)) },
                                filterCount = marqueList.size,
                                customFilterCode = { marqueList[it].mARDesignation },
                                filterLabel = { marqueList[it].mARDesignation }
                            )


                        }
                    )
                }



            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {

                DataBasePaginationLoadState(lazyPagingItems = articleList)


                if((articlesState.loading || articlesPaginationState.loading)) {
                    LottieAnim(lotti = R.raw.loading, size = 250.dp)
                }
                else{
                    if (!isOperateurPatrimoine || searchTextState.text.isNotEmpty()) {
                        StockFilterSegmentedButton(
                            onfilterByStockChange = { articlesViewModel.onfilterByStockChange(it) },
                            stockArticleStock = articlesViewModel.stockArticleStock
                        )
                    }

                    // Obligatory price category selection (like SelectArticleCalcul)
                    if(haveChoosePriceCategoriAuthorisation && (articleList?.itemCount?:0) > 0) {
                        GenericDropdownMenu(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp, vertical = 8.dp),
                            designation = articlesViewModel.selectedPriceCategory,
                            errorValue = null,
                            itemExpanded = articlesViewModel.showPriceCategory,
                            selectedItem = articlesViewModel.selectedPriceCategory,
                            label = stringResource(R.string.unit_price_field_title),
                            readOnly = true,
                            showTrailingIcon = true,
                            itemList = articlesViewModel.priceCategoryList,
                            getItemDesignation = { it },
                            onClick = {
                                articlesViewModel.onSelectedPriceCategoryChange(it)
                                articlesViewModel.setShowPriceCategoryChange(false)
                            },
                            onItemExpandedChange = {
                                articlesViewModel.setShowPriceCategoryChange(it)
                            },
                            lottieAnimEmpty = {
                                LottieAnim(lotti = R.raw.emptystate)
                            },
                            lottieAnimError = {
                                LottieAnim(lotti = R.raw.connection_error, size = it)
                            }
                        )
                    }



                    if ((articleList?.itemCount?: 0) > 0) {
                        ProductsList(
                            isRefreshing = false,
                            isConnected = isConnected,
                            listUnite = listUnite,
                            getSharedDataViewModel = getSharedDataViewModel,
                            filteredArticles = articleList,
                            listState = listState,
                            isOperateurPatrimoine = isOperateurPatrimoine,
                            parametrage = parametrage,
                            baseConfig = selectedBaseconfig,
                            utilisateur = utilisateur,
                            articlesViewModel = articlesViewModel,
                            onItemClick = {
                                articlesViewModel.onCurrentArticleChange(it)
                                articlesViewModel.onModifyChange(false)
                                articleTxtValidViewModel.resetVariable()

                                articlesViewModel.onFromChange(from)
                                navigate(ArticlesDetailRoute)
                            }
                        )
                    } else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                }

            }
        }
    }
}

@Composable
fun ProductsList(
    getSharedDataViewModel: GetSharedDataViewModel,
    isRefreshing: Boolean,
    isConnected: Boolean,
    listUnite: List<Unite>,
    isOperateurPatrimoine: Boolean,
    filteredArticles: LazyPagingItems<Article>?,
    listState: LazyStaggeredGridState,
    parametrage: Parametrages,
    baseConfig: BaseConfig,
    utilisateur: Utilisateur,
    articlesViewModel: ArticlesViewModel,
    onItemClick: (Article) -> Unit = {},
) {
    val isProMode = parametrage.proModeM


    /*LazyVerticalGrid(
        columns = GridCells.Adaptive(minSize = 128.dp),
                state = listState,
    ) {*/

if(filteredArticles == null) return
    else
    PullToRefreshLazyVerticalStaggeredGrid(

        items = filteredArticles.itemSnapshotList,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !filteredArticles.itemSnapshotList.any { it?.isSync != true } && isConnected,
        onRefresh = { getSharedDataViewModel.getArticl(baseConfig = baseConfig, utilisateur = utilisateur) },
        key = {index ->   filteredArticles.itemSnapshotList.getOrNull(index)?.aRTCode ?: index },
        content = {index ->
            val article = filteredArticles[index]?: Article()
            val unite = listUnite.firstOrNull { it.uNICode == article.uNITEARTICLECodeUnite }?.uNIDesignation?: article.uNITEARTICLECodeUnite

           // val unite = article[index].unite?.uNIDesignation?: article.uNITEARTICLECodeUnite

           val qteStation = getQuantity(article = article)



          //  val unite = uniteList.firstOrNull { it.uNICode == article.uNITEARTICLECodeUnite }?.uNIDesignation ?: article.uNITEARTICLECodeUnite
            OutlinedCard(
                modifier = Modifier.clickable { onItemClick(article) },
                shape = roundedCornerShape(),
                border = BorderStroke(width = 1.dp, color = if(qteStation> 0 || isOperateurPatrimoine) LocalContentColor.current else MaterialTheme.colorScheme.error)
                ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .padding(12.dp)
                ) {
                    Text(
                        textAlign = TextAlign.Center,
                        text = article.aRTDesignation + if (isProMode == 1 && article.taiTaille.isNotEmpty())" (${article.taiTaille})"  else "" ,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold,
                        maxLines = 2,
                    )

                    Box(
                        modifier = Modifier.size(100.dp),
                        contentAlignment = Alignment.Center
                    ){
                        if (!article.isSync) {
                            LottieAnim(
                                lotti = R.raw.connection_error,
                                size = 80.dp,
                                onClick = { onItemClick(article) }
                            )
                        } else Icon(
                            imageVector = if((article.typeProduit?.lowercase(Locale.ROOT)?:"") == ArticleType.PATRIMOINE.value.lowercase(Locale.ROOT)) Icons.TwoTone.HideImage else Icons.TwoTone.ShoppingCart,
                            contentDescription = "",
                            //tint = if(qteStation> 0) LocalContentColor.current else MaterialTheme.colorScheme.error,

                            modifier = Modifier.size(80.dp)
                        )
                        if(isProMode == 1 && article.cOUDesignation!= null) {
                            OutlinedCard() {
                                Text(
                                    modifier = Modifier.padding(start = 6.dp, end = 6.dp, top = 3.dp, bottom = 3.dp),
                                    text = article.cOUDesignation?:"N/A"
                                )
                            }

                        }

                    }



                    if(article.typeProduit?.lowercase() != ArticleType.PATRIMOINE.value)  {
                        Text(
                            textAlign = TextAlign.Center,
                            text = removeTrailingZeroInDouble(qteStation.toString()).ifEmpty { "N/A" } +" " + unite,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.SemiBold,
                            maxLines = 1
                        )

                        Text(
                            textAlign = TextAlign.Center,
                            text = StringUtils.convertStringToPriceFormat(articlesViewModel.getPrice(article)),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                    else {
                        Text(
                            textAlign = TextAlign.Center,
                            //text = filteredArticles[index].fAMLib?:"N/A",
                            text = article.aRTCodeBar,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.SemiBold,
                            //   maxLines = 1
                        )

                        /*Text(
                            textAlign = TextAlign.Center,
                            text = filteredArticles[index].mARDesignation?:"N/A",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.SemiBold,
                        )*/
                    }


                }
            }
        }
    )
}




