package com.asmtunis.procaisseinventory.articles.consultation.view_model

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.ArticlesListState
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ValidationArticleEvent
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.article.ArticleFormState
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.famille.FamilleFormState
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCodeBar
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.enum_classes.StockArticle
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticleWithStation
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ArticlesViewModel @Inject constructor(
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb
) : ViewModel() {

    init {
        getTypePrixList()
    }

    var showStocksByStation: Boolean by mutableStateOf(false)
        private set

    fun onShowStocksByStationChange(value: Boolean) {
        showStocksByStation = value
    }


    var showAlertDialog: Boolean by mutableStateOf(false)
        private set

    fun onShowAlertDialogChange(value: Boolean) {
        showAlertDialog = value
    }


    var currentArticle: Article by mutableStateOf(Article())
        private set

    fun onCurrentArticleChange(value: Article) {
        currentArticle = value
    }


    var showBareCodeBottomSheet by mutableStateOf(false)
        private set

    fun bottomSheetBareCodeVisibility(value: Boolean) {
        showBareCodeBottomSheet = value
    }

    var addNewFamilleIsVisible: Boolean by mutableStateOf(false)
        private set

    fun onAddNewFamilleVisibilityChange(value: Boolean) {
        addNewFamilleIsVisible = value
    }


    var familleGeneratedCode: String by mutableStateOf("")
        private set

    fun generteNewFamilleCode(prefix: String) {

        viewModelScope.launch {


            proInventoryLocalDb.famille.getNewCode(prefix).collectLatest {
                familleGeneratedCode = prefix + it
            }


        }

    }


    fun saveStationStockArticle(stationStockArticle: StationStockArticle) {
        viewModelScope.launch(ioDispatcher) {
            proInventoryLocalDb.stationsArticle.upsert(stationStockArticle)
        }
    }

    fun saveArticleCodeBar(articleCodeBar: ArticleCodeBar) {
        viewModelScope.launch(ioDispatcher) {
            proCaisseLocalDb.articlesBarCode.upsert(articleCodeBar)
        }
    }


    fun deleteByArtCode(article: Article) {
        viewModelScope.launch(ioDispatcher) {
            proCaisseLocalDb.articles.deleteByArtCode(article.aRTCode)
        }
    }

    fun saveFamille(famille: Famille) {
        viewModelScope.launch(ioDispatcher) {
            proInventoryLocalDb.famille.upsert(famille)
        }
    }


    var typeProduitExpanded: Boolean by mutableStateOf(false)
        private set

    fun onTypeProduitExpandedChange(value: Boolean) {
        typeProduitExpanded = value
    }


    var familleExpanded: Boolean by mutableStateOf(false)
        private set

    fun onFamilleExpandedChange(value: Boolean) {
        familleExpanded = value
    }

    var stationExpanded: Boolean by mutableStateOf(false)
        private set

    fun onStationExpandedChange(value: Boolean) {
        stationExpanded = value
    }

    var marqueExpanded: Boolean by mutableStateOf(false)
        private set

    fun onMarqueExpandedChange(value: Boolean) {
        marqueExpanded = value
    }

    var marqueFilter: String by mutableStateOf("")
        private set

    fun onMarqueFilterChange(value: String) {
        marqueFilter = value
    }

    var typePrixExpanded: Boolean by mutableStateOf(false)
        private set

    fun onTypePrixExpandedChange(value: Boolean) {
        typePrixExpanded = value
    }


    var tvaExpanded: Boolean by mutableStateOf(false)
        private set

    fun onTvaExpandedChange(value: Boolean) {
        tvaExpanded = value
    }

    var uniteExpanded: Boolean by mutableStateOf(false)
        private set

    fun onUniteExpandedChange(value: Boolean) {
        uniteExpanded = value
    }

    //   var isFiltering: Boolean by mutableStateOf(false)
    //   private set

    //TODO CHANGE INT TO ENUM CLASS
    var stockArticleStock: String by mutableStateOf(StockArticle.PAS_FILTER_STOCK.filter)
        private set

    fun onfilterByStockChange(value: String) {
        stockArticleStock = value
        filterArticles(articlesListState = articlesListState)
    }

    var articlesListState: ArticlesListState by mutableStateOf(ArticlesListState())
        private set
    var showSearchView: Boolean by mutableStateOf(false)
        private set

    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }


    var showCustomFilter: Boolean by mutableStateOf(false)
        private set

    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }


    var modify: Boolean by mutableStateOf(false)
        private set

    fun onModifyChange(value: Boolean) {
        modify = value
    }


    var from by mutableStateOf("")
        private set

    fun onFromChange(value: String) {
        from = value
    }


    var stationStockArticleWithStationList: List<StationStockArticleWithStation> by mutableStateOf(
        emptyList()
    )
        private set

    fun getStationStockArticleWithStationList(codeArticle: String) {
        viewModelScope.launch {
            proInventoryLocalDb.stationsArticle.getStationListByProduct(codeArticle).collectLatest {
                if (it == null) return@collectLatest

                stationStockArticleWithStationList = it

            }
        }
    }

    fun getStationStockArticleWithStationListForUserStation(codeArticle: String, userStation: String) {
        viewModelScope.launch {
            proInventoryLocalDb.stationsArticle.getStationListByProductForUserStation(codeArticle, userStation).collectLatest {
                if (it == null) return@collectLatest

                stationStockArticleWithStationList = it

            }
        }
    }


    var typePrixList: List<TypePrixUnitaireHT> by mutableStateOf(emptyList())
        private set

    private fun getTypePrixList() {
        viewModelScope.launch {
            proInventoryLocalDb.typePrix.getAll().collectLatest {
                typePrixList = it
            }
        }
    }

    // Price category selection functionality
    val priceCategoryList = listOf("Prix Publique", "Prix Gros 1", "Prix Gros 2", "Prix Gros 3")

    var selectedPriceCategory by mutableStateOf(priceCategoryList.first())
        private set

    fun onSelectedPriceCategoryChange(value: String) {
        selectedPriceCategory = value
    }

    var showPriceCategory by mutableStateOf(false)
        private set

    fun setShowPriceCategoryChange(value: Boolean) {
        showPriceCategory = value
    }

    fun getPrice(article: Article): String =
        when (selectedPriceCategory) {
            priceCategoryList.first() -> article.pvttc.toString()
            priceCategoryList[1] -> article.prixGros1
            priceCategoryList[2] -> article.prixGros2
            priceCategoryList[3] -> article.prixGros3
            else -> article.pvttc.toString()
        }


    // val textState = remember { mutableStateOf(TextFieldValue("")) }
    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set

    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value

       filterArticles(articlesListState = articlesListState)
    }

    // var searchList = mutableStateListOf<String>()

    var searchValue by mutableStateOf("")
        private set

    fun onsearchValue(product: String) {
        searchValue = product
    }

    var searchViewVisibilityStates by mutableStateOf(false)
        private set

    fun onsearchViewVisibilityStatesChanged(bottomNavigationBar: Boolean) {
        searchViewVisibilityStates = bottomNavigationBar
    }

    //private var getArticlesJob: Job? = null

    fun onEvent(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (articlesListState.listOrder::class == event.listOrder::class &&
                    articlesListState.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }

                // filterArticls(event.listOrder)
                articlesListState = articlesListState.copy(
                    listOrder = event.listOrder
                )
                filterArticles(articlesListState = articlesListState)
            }

            is ListEvent.Delete -> {
                TODO()
            }

            is ListEvent.Restore -> {
                TODO()
            }
            /* is ListEvent.ToggleOrderSection -> {
                 state = state.copy(
                     isOrderSectionVisible = !state.isOrderSectionVisible
                 )
             }*/

            is ListEvent.ListSearch -> {
                articlesListState = articlesListState.copy(
                    filter = event.listSearch
                )
                filterArticles(articlesListState = articlesListState)
            }

            is ListEvent.FirstCustomFilter -> {
                articlesListState = articlesListState.copy(
                    filterByFamille = event.firstFilter
                )

                filterArticles(articlesListState = articlesListState)
            }

            is ListEvent.SecondCustomFilter -> {
                articlesListState = articlesListState.copy(
                    filterByMarque = event.secondFiter
                )

                filterArticles(articlesListState = articlesListState)
            }

            is ListEvent.ThirdCustomFilter -> TODO()
        }
    }


    // private var currentFilterJob: Job? = null //Consider using a single job and cancelling it to avoid multiple simultaneous filtering operations
    private var filterJob: Job? = null //Encapsulate the job in a variable

    fun filterArticles(articlesListState: ArticlesListState) {
        val searchedText = searchTextState.text
        val listFilter = articlesListState.filter
        val filterByFamille = articlesListState.filterByFamille
        val filterByMarque = articlesListState.filterByMarque
        val sortBy = when (articlesListState.listOrder) {
            is ListOrder.Title -> "ART_Designation"
            is ListOrder.Date -> "ddm"
            is ListOrder.Third -> "pvttc"
        }
        val isAsc = if (articlesListState.listOrder.orderType is OrderType.Ascending) 1 else 2 // Use 1 and 2 directly

        // Cancel the previous job if it's still running.  This prevents race conditions and unnecessary work.
        filterJob?.cancel()
        filterJob = viewModelScope.launch(ioDispatcher) {
            try {
                val articleFlow = when {
                    searchedText.isEmpty() -> proCaisseLocalDb.articles.getAllFiltred(
                        sortBy = sortBy,
                        stock = stockArticleStock,
                        isAsc = isAsc,
                        filterByFamille = filterByFamille,
                        filterByMarque = filterByMarque
                    )

                    listFilter is ListSearch.FirstSearch -> proCaisseLocalDb.articles.filterByName(
                        searchedText,
                        sortBy = sortBy,
                        stock = stockArticleStock,
                        isAsc = isAsc,
                        filterByFamille = filterByFamille,
                        filterByMarque = filterByMarque
                    )

                    listFilter is ListSearch.SecondSearch -> proCaisseLocalDb.articles.filterByBarCode(
                        searchedText,
                        sortBy = sortBy,
                        stock = stockArticleStock,
                        isAsc = isAsc,
                        filterByFamille = filterByFamille,
                        filterByMarque = filterByMarque
                    )

                    listFilter is ListSearch.ThirdSearch -> proCaisseLocalDb.articles.filterByPrice(
                        searchedText,
                        sortBy = sortBy,
                        stock = stockArticleStock,
                        isAsc = isAsc,
                        filterByFamille = filterByFamille,
                        filterByMarque = filterByMarque
                    )

                    else -> throw IllegalStateException("Unexpected ListSearch type: ${listFilter::class.simpleName}")
                }


                    setArticleList(articleFlow)


            } finally {
                // Ensure isLoadingFromDB is set to false even if an exception occurs
                filterJob = null  //Important to set it to null after finishing
            }
        }
    }


    private fun setArticleList(articleList: Flow<PagingData<Article>>?) {
        filterJob = null
        articlesListState = articlesListState.copy(lists = articleList) //Directly replace the list

    }


    //    fun filterArticlesPerStation(stationOrigineCode: String, articleListState: ArticlesListState) {
//
//
//        val filteredList = articleListState.lists.filter { articleList->
//            articleList.stationStockArticle.any { statStock->
//                statStock.sARTCodeSatation == stationOrigineCode &&
//                        when (stockArticleStock) {
//                            StockArticle.EN_STOCK.filter -> {
//                                stringToDouble(statStock.sARTQteStation) > 0.0
//                            }
//                            StockArticle.HORS_STOCK.filter -> {
//                                stringToDouble(statStock.sARTQteStation) <= 0.0
//                            }
//                            else -> {
//                                stringToDouble(statStock.sARTQteStation) <= 0.0 || stringToDouble(statStock.sARTQteStation) > 0.0
//                            }
//                        }
//
//            }
//        }
//
//        setArticleList(articleList = filteredList)
//    }

    private fun saveFamille(
        context: Context,
        toaster: ToasterState,
        marqueGeneratedCode: String,
        input: FamilleFormState
    ) {
        showToast(
            context = context,
            toaster = toaster,
            message = String.format(
                context.resources.getString(R.string.add_place_holder),
                marqueGeneratedCode
            ) + "\n" + context.resources.getString(
                R.string.famille_success
            ),
            type = ToastType.Success,
        )
        onAddNewFamilleVisibilityChange(false)
        val famille = Famille(
            fAMCode = familleGeneratedCode,
            fAMLib = input.designationFamille,
            fAMUser = Globals.ADMIN,
            fAMStation = input.station.sTATCode,
            fAMIsTaktile = "0",
            fAMImage = null,
            fAMCouleur = null,
            fAMDesgCourte = input.designationFamilleCourte,
            fAMNumOrdre = input.ordre.toInt(),
            fAMExport = "0",
            fAMDDm = getCurrentDateTime(),
            fAMAlphabetique = null,
            photoPath = null,
            sousFAMCode = null,
            numAppel = null,
            dDmM = getCurrentDateTime()
        )



        famille.isSync = false
        famille.status = ItemStatus.INSERTED.status

        saveFamille(famille)
    }


    private fun saveStationStockArticle(
        userStation: String,
        input: ArticleFormState
    ) {
        val stationStockArticle = StationStockArticle(
            sARTCodeSatation = userStation,
            sARTCodeArt = input.barCode,
            sARTQteStation = input.qteStock,
            sARTUser = "",
            sARTDDm = getCurrentDateTime(),
            sARTQteDeclaree = "",
            sARTQTEmin = "",
            sARTQTEmax = "",
            sARTStation = "",
            sARTExport = "",
            regularisation = ""
        )

        saveStationStockArticle(stationStockArticle)
    }

    private fun saveArticleCodeBare(
        input: ArticleFormState
    ) {
        val articleCodeBare = ArticleCodeBar(
            parentCodeBar = input.barCode,
            filsCodeBar = input.barCode
        )
        articleCodeBare.isSync = false
        articleCodeBare.status = ItemStatus.INSERTED.status
        saveArticleCodeBar(articleCodeBare)
    }


    private fun saveArticle(
        context: Context,
        toaster: ToasterState,
        input: ArticleFormState,

        userStation: String
    ) {

        showToast(
            context = context,
            toaster = toaster,
            message = context.resources.getString(R.string.add_article_success_place_holder, ""),
            type = ToastType.Success,
        )
        /**
         *         article.setType_operation(!fromInv ? "art" : "inv");
         */

        val article = Article(
            aRTMarque = input.marque.mARCode,
            mARDesignation = input.marque.mARDesignation,
            fAMLib = input.famille.fAMLib,
            aRTFamille = input.famille.fAMCode,
            fAMCode = input.famille.fAMCode,
            aRTTypePrixUnitaireHTVA = input.typePrix.type_PrixUnitaireHT,
            // colorCode = 0,
            //  prixGros1 = "",
            // prixGros2 = "",
            // prixGros3 = "",
            aRTCode = input.barCode,
            aRTCodeBar = input.barCode,
            aRTExport = null,
            aRTQTEmax = null,
            aRTQTEmin = null,
            aRTQteDeclaree = null,
            //filsCodeBar = "",
            aRTDesignation = input.designation,
            aRTPrixUnitaireHT = input.prixHT,
            photoPath = "",
            aRTTVA = stringToDouble(input.tva.tVACode),
            aRTQteStock = input.qteStock.ifEmpty { "0" },
            // aRTQteStock = input.qteTTStations,
            sARTQte = stringToDouble(input.qteStock),
            aRTEPrixTTC = input.prixTTC,
            // artPrixPublique = input.prixTTC,
            pvttc = stringToDouble(input.prixTTC),
            prixSolde = input.prixTTC, // todo see if enableuser to enter prix solde
            tauxSolde = "",
            sARTCodeSatation = userStation,
            // uNITEARTICLEQtePiece = 1, // todo add uNITEARTICLEQtePiece input test to modify this value
            uNITEARTICLECodeUnite = input.unite.uNICode, // todoooooooo
            artMaxTRemise = "",
            aRTStation = userStation,
            typeProduit = input.typeProduit.ifEmpty { "article" },
            typeOperation = "art", // article.setType_operation(!fromInv ? "art" : "inv");
            ddm = getCurrentDateTime(),
            aRTDateCr = getCurrentDateTime(),
            aRTCouleur = ""
        )

        article.isSync = false
        article.status = ItemStatus.INSERTED.status

        viewModelScope.launch(ioDispatcher) {
            proCaisseLocalDb.articles.upsert(article)
        }
    }


    fun handleAddArticleEvents(
        popBackStack: () -> Unit,
        context: Context,
        toaster: ToasterState,
        marqueGeneratedCode: String,
        validationAddArticleEvents: ValidationArticleEvent,
        addNewMarque: (String) -> Unit,
        utilisateur: Utilisateur,

        ) {
        viewModelScope.launch {

            when (validationAddArticleEvents) {
                is ValidationArticleEvent.Article -> {

                    val userStation = utilisateur.Station
                    val input = validationAddArticleEvents.addNewtArticle
                    saveArticle(
                        context = context,
                        toaster = toaster,
                        input = input,
                        userStation = userStation
                    )

                    saveArticleCodeBare(
                        input = input
                    )

                    saveStationStockArticle(
                        input = input,
                        userStation = userStation

                    )
                    onModifyChange(false)
                    popBackStack()
                }

                is ValidationArticleEvent.Famille -> {


                    val input = validationAddArticleEvents.addNewtFamille
                    saveFamille(
                        context = context,
                        toaster = toaster,
                        marqueGeneratedCode = marqueGeneratedCode,
                        input = input
                    )

                }

                is ValidationArticleEvent.Marque -> {


                    val input = validationAddArticleEvents.addNewtMarque
                    addNewMarque(input.designationMarque)

                    showToast(
                        context = context,
                        toaster = toaster,
                        message = String.format(
                            context.resources.getString(R.string.add_place_holder),
                            input.designationMarque
                        ) + "\n" + context.resources.getString(R.string.cd_addMarque_succ),
                        type = ToastType.Success,
                    )

                }
            }

        }
    }
}


