package com.asmtunis.procaisseinventory.articles.data.article.local.repository.articles

import androidx.paging.Pager
import androidx.paging.PagingData
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.article.local.dao.ArticleDAO
import com.asmtunis.procaisseinventory.core.PagingConfigRoom
import kotlinx.coroutines.flow.Flow


class ArticlesLocalRepositoryImpl(
    private val articleDAO: ArticleDAO
) : ArticlesLocalRepository {

    override fun upsert(value: Article)  = articleDAO.insert(value)

    override fun upsertAll(value: List<Article>)  = articleDAO.insertAll(value)
    override fun getByArtCode(value: String) = articleDAO.getOneByCode(value)

    override fun deleteAll() = articleDAO.deleteAll()
    override fun deleteByArtCode(aRTCode: String) = articleDAO.deleteByArtCode(aRTCode = aRTCode)

    override fun getAll(): Flow<List<Article>> = articleDAO.all
    override fun getNotSync(): Flow<List<Article>> = articleDAO.nonSync

    override fun allCount(): Flow<Int> = articleDAO.allCount
    override fun updateSyncArticle(artCode: String) = articleDAO.updateSyncArticle(artCode)
    override fun updateArtQteStock(newQteAllStations: String,newQteStation : String, codeArticle: String) =
        articleDAO.updateArtQteStock(newQteAllStations,newQteStation, codeArticle)



    override fun filterByName(
        filterString: String,
        sortBy: String?,
        stock : String?,
        isAsc: Int?,
        filterByFamille: String,
        filterByMarque: String
    ): Flow<PagingData<Article>> {
        return Pager(
            config = PagingConfigRoom.pagingConfig,
            pagingSourceFactory = {
                articleDAO.filterByName(
                    filterString = filterString,
                    sortBy = sortBy,
                    stock = stock,
                    isAsc = isAsc,
                    filterByFamille = filterByFamille,
                    filterByMarque = filterByMarque
                )
            }
        ).flow
    }

    override fun filterByBarCode(
        filterString: String,
        sortBy: String?,
        stock : String?,
        isAsc: Int?,
        filterByFamille: String,
        filterByMarque: String
    ): Flow<PagingData<Article>>{
        return Pager(
            config = PagingConfigRoom.pagingConfig,
            pagingSourceFactory = {
                articleDAO.filterByBarCode(
                    filterString = filterString,
                    sortBy = sortBy,
                    stock = stock,
                    isAsc = isAsc,
                    filterByFamille = filterByFamille,
                    filterByMarque = filterByMarque
                )
            }
        ).flow
    }


    override fun filterByPrice(
        filterString: String,
        sortBy: String?,
        stock : String?,
        isAsc: Int?,
        filterByFamille: String,
        filterByMarque: String
    ): Flow<PagingData<Article>>{
        return Pager(
            config = PagingConfigRoom.pagingConfig,
            pagingSourceFactory = {
                articleDAO.filterByPrice(
                    filterString = filterString,
                    sortBy = sortBy,
                    stock = stock,
                    isAsc = isAsc,
                    filterByFamille = filterByFamille,
                    filterByMarque = filterByMarque
                )
            }
        ).flow
    }



    override fun getAllFiltred(
        sortBy: String?,
        stock : String?,
        isAsc: Int?,
        filterByFamille: String,
        filterByMarque: String
    ): Flow<PagingData<Article>> {
        return Pager(
            config = PagingConfigRoom.pagingConfig,
            pagingSourceFactory = {
                articleDAO.getAllFiltred(
                    sortBy = sortBy,
                    stock = stock,
                    isAsc = isAsc,
                    filterByFamille = filterByFamille,
                    filterByMarque = filterByMarque
                )
            }
        ).flow
    }


}