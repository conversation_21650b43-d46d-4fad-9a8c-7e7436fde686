package com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.NOT_FOUND_LOCALLY
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork // Ajout de l'import
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.CODE_VERIFICATION_INVENTAIRE_EXIST
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControlInventaireResponse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.io.IOException
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class SelectPatrimoineViewModel
    @Inject
    constructor(
        @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
        @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
        @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
        @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
        private val proInventoryLocalDb: ProInventoryLocalDb,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        private val proCaisseRemote: ProCaisseRemote,
        private val listenNetwork: ListenNetwork, // Injection de ListenNetwork
    ) : ViewModel() {
    var isConnected by mutableStateOf(true) // État pour suivre la connectivité
        private set

    init { // Bloc d'initialisation pour observer la connectivité
        viewModelScope.launch {
            listenNetwork.isConnected.distinctUntilChanged().collect { networkStatus ->
                isConnected = networkStatus
            }
        }
    }
    var backUpSelectedPatrimoine by mutableStateOf(SelectedPatrimoine())
        private set
    fun onBackUpSelectedPatrimoineChange(value: SelectedPatrimoine) {
        backUpSelectedPatrimoine = value
    }


    var showFilterLine by mutableStateOf(false)
        private set
    fun onShowFilterLineChange(value: Boolean) {
        showFilterLine = value
    }


    var fiterValue by mutableStateOf("")
        private set

    fun onFilterValueChange(value: String) {
        fiterValue = value
    }


    var marqueFilter: String by mutableStateOf("")
        private set

    fun onMarqueFilterChange(value: String) {
        marqueFilter = value
    }

    var marqueTxt: String by mutableStateOf("")
        private set

    fun onMarqueTextChange(value: String) {
        marqueTxt = value
    }


    var invPatByNumSerie: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set



    var keepTypedNumSerie: Boolean by mutableStateOf(false)
        private set

    fun onKeepTypedNumSerieChange(value: Boolean) {
        keepTypedNumSerie = value
    }

    var showSetNumeSerie: Boolean by mutableStateOf(false)
        private set

    fun onShowSetNumeSerieChange(value: Boolean) {
        if(value) resetPatrimoineVerificationState()

        showSetNumeSerie = value
    }

    var selectedPatrimoine by mutableStateOf(SelectedPatrimoine())
        private set

    fun setSelectedPat(value: SelectedPatrimoine) {
        selectedPatrimoine = value
    }

    // Property to store the last assigned serial number for returning from affectation
    var lastAssignedNumSerie by mutableStateOf("")
        private set

    // Method to update the last assigned serial number
    fun updateLastAssignedNumSerie(numSerie: String) {
        lastAssignedNumSerie = numSerie
    }


    var typePiece: String by mutableStateOf("")
        private set

    fun onTypePieceChange(value: String) {
        typePiece = value
    }

    var selectedPatrimoineList = mutableStateListOf<SelectedPatrimoine>()
        private set

    fun addItemToSelectedPatrimoineList(selectedPatrimoine: SelectedPatrimoine, deleteSelectedItem: Boolean = false) {
//TODO SEE if directly replace pat if exist (like current case) or show a message, ...

        val lg = selectedPatrimoineList.filter { it.numSerie == selectedPatrimoine.numSerie }
        if (lg.isNotEmpty()) {
            selectedPatrimoineList.replaceAll { if (it.numSerie == lg.first().numSerie) selectedPatrimoine else it }
        } else {
            selectedPatrimoineList.add(selectedPatrimoine)
        }

    }
    fun clearSelectedPatrimoineList() {
        selectedPatrimoineList.clear()
    }

    fun resetSelectedPatrimoineArticles() {
        selectedPatrimoineList.clear()
        selectedPatrimoine = SelectedPatrimoine()
    }

    fun setConsultationSelectedPatrimoineList(
        article: Article?,
        numSerie: String = "",
        quantity: Double = 0.0,
        imageList: List<ImagePieceJoint> = emptyList(),
        note: String,
        marque: Marque = Marque()
    ) {
        selectedPatrimoineList.add(
            SelectedPatrimoine(
                articleCode = article?.aRTCode?: "N/A",
                numSerie = numSerie,
                quantity = quantity,
                imageList = imageList,
                note = note,
                marqueCode = marque.mARCode
            ),
        )
    }

    fun deleteSelectedPatrimoine(selectedPat: SelectedPatrimoine) {
        selectedPatrimoineList.removeIf { it.numSerie == selectedPat.numSerie }
    }
    fun deleteItemToSelectedPatrimoineList(
        value: SelectedPatrimoine,
        isUpdate: Boolean = false,
        selectedInvPatrimoine: BonCommande = BonCommande()
    ) {
        deleteSelectedPatrimoine(selectedPat = value)

        setSelectedPat(SelectedPatrimoine())

        if(isUpdate) {
            viewModelScope.launch(dispatcherIO) {
                proCaisseLocalDb.ligneBonCommande.deleteByLgDevNumBon(code = selectedInvPatrimoine.devCodeM)
                proCaisseLocalDb.inventairePieceJoint.deleteByDevNumNotSync(devNum = selectedInvPatrimoine.devCodeM)
            }

        }
    }

    fun deleteImage(images: ImagePieceJoint) {
        // selectedPatrimoineList.removeIf { it.imageList. }

        val initialImageList = selectedPatrimoine.imageList

        val imagesList = initialImageList.filter { it.imgUrl != images.imgUrl }

        selectedPatrimoine =
            selectedPatrimoine.copy(
                imageList = imagesList,
            )

        addItemToSelectedPatrimoineList(selectedPatrimoine)
    }

    var patrimoineVerificationState: RemoteResponseState<ControlInventaireResponse> by mutableStateOf(RemoteResponseState())
        private set

    fun resetPatrimoineVerificationState() {
        patrimoineVerificationState = RemoteResponseState()
    }

    // Ancienne logique de patrimoineVerification, maintenant privée et renommée
    private fun executeOnlinePatrimoineVerification(
        baseConfig: BaseConfig,
        controlPatrimoine: ControleInventaire
    ) {
        // verifyPatLocaly(controlPatrimoine = controlPatrimoine) // Tel que commenté dans votre version
        try {
            viewModelScope.launch {
                val verificationPatrimoine =
                    async {
                        val baseConfigObj =
                            GenericObject(
                                baseConfig,
                                Json.encodeToJsonElement(controlPatrimoine),
                            )
                        proCaisseRemote.inventairePatrimoine.controlInventaire(Json.encodeToString(baseConfigObj))
                    }
                verificationPatrimoine.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            if (result.data!!.code == "10200" || result.data.code == CODE_VERIFICATION_INVENTAIRE_EXIST) {
                                if (controlPatrimoine.DEV_info3 != TypePatrimoine.AFFECTATION.typePat) {
                                    verifyLocaly(controlPatrimoine = controlPatrimoine)
                                    proCaisseLocalDb.invePatrimoine.getByNumSerie(controlPatrimoine.LG_DEV_NumSerie).collect { resultData ->
                                        invPatByNumSerie = resultData ?: emptyMap()
                                        patrimoineVerificationState =
                                            if (invPatByNumSerie.isEmpty()) {
                                                RemoteResponseState(
                                                    data = null,
                                                    loading = false,
                                                    error = NOT_FOUND_LOCALLY,
                                                    message = TypePatrimoine.INVENTAIRE.typePat,
                                                )
                                            } else {
                                                RemoteResponseState(
                                                    data = result.data,
                                                    loading = false,
                                                    error = null,
                                                )
                                            }
                                    }
                                } else {
                                    patrimoineVerificationState =
                                        RemoteResponseState(
                                            data = result.data,
                                            loading = false,
                                            error = null,
                                        )
                                }
                            } else {
                                patrimoineVerificationState =
                                    RemoteResponseState(
                                        data = result.data,
                                        loading = false,
                                        message = result.data.code,
                                        error = result.data.message,
                                    )
                            }
                        }
                        is DataResult.Loading -> patrimoineVerificationState = RemoteResponseState(data = null, loading = true, error = null)
                        is DataResult.Error -> {
                            verifyLocaly(controlPatrimoine = controlPatrimoine) // Fallback en cas d'erreur de l'appel distant
                        }
                        else -> {}
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        } catch (e: IOException) {
            // Conformément à votre code, pas de fallback direct à verifyLocaly ici pour une IOException générale
            // Si un fallback est souhaité ici aussi, il faudrait ajouter: verifyLocaly(controlPatrimoine = controlPatrimoine)
        }
    }

    // Nouvelle fonction publique patrimoineVerification qui dispatche en fonction de la connectivité
    fun patrimoineVerification(
        baseConfig: BaseConfig,
        controlPatrimoine: ControleInventaire
    ) {
        if (isConnected) {
            executeOnlinePatrimoineVerification(baseConfig, controlPatrimoine)
        } else {
            verifyLocaly(controlPatrimoine = controlPatrimoine)
        }
    }

    private fun verifyLocaly(controlPatrimoine: ControleInventaire) {
        viewModelScope.launch {
            var shouldVerifyPat = true
            if (controlPatrimoine.DEV_info3 == TypePatrimoine.AFFECTATION.typePat) {
                shouldVerifyPat = verifyCodeBareBatimentLocaly(controlPatrimoine = controlPatrimoine)
            }

            if (shouldVerifyPat) {
                verifyPatLocaly(controlPatrimoine = controlPatrimoine)
            } else {
            }
        }
    }

    private suspend fun verifyCodeBareBatimentLocaly(controlPatrimoine: ControleInventaire): Boolean {
        var isEmpty = true
        val listImmobilisation = proCaisseLocalDb.immobilisation.getAllZoneConsomationByImoCB(controlPatrimoine.LG_DEV_NumSerie).firstOrNull()

        if (listImmobilisation.isNullOrEmpty()) {
        } else {
            isEmpty = false
            val firstImmo = listImmobilisation.first() // Accéder après la vérification de nullité/vide
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "Déjà affecté au bâtiment '${firstImmo.cLINomPren}' (Local vérification)"
                )
        }
        return isEmpty
    }

    private suspend fun verifyPatLocaly(controlPatrimoine: ControleInventaire) {
        val numSerie = controlPatrimoine.LG_DEV_NumSerie
        val devInfo3 = controlPatrimoine.DEV_info3.lowercase(Locale.ROOT)
        val cltCode = controlPatrimoine.DEV_CodeClient

        val inventoryDataFromDb = proCaisseLocalDb.invePatrimoine.getByNumSerie(numSerie).firstOrNull()
        invPatByNumSerie = inventoryDataFromDb ?: emptyMap()

        if (invPatByNumSerie.isEmpty()) {
            if (devInfo3 == TypePatrimoine.AFFECTATION.typePat) {
                verifyAffectationLoccaly(invPatByNumSerie)
            } else {
                patrimoineVerificationState = RemoteResponseState(
                    data = null, loading = false, message = devInfo3, error = NOT_FOUND_LOCALLY
                )
            }
        } else {
            when (devInfo3) {
                TypePatrimoine.AFFECTATION.typePat -> {
                    verifyAffectationLoccaly(invPatByNumSerie)
                }
                TypePatrimoine.SORTIE.typePat -> {
                    verifyDepOutLoccaly(invPatByNumSeri = invPatByNumSerie, cLICode = cltCode)
                }
                TypePatrimoine.ENTREE.typePat -> {
                    verifyDepInLoccaly(invPatByNumSeri = invPatByNumSerie, cLICode = cltCode)
                }
                TypePatrimoine.INVENTAIRE.typePat -> {
                    verifyInventaireLoccaly(invPatByNumSeri = invPatByNumSerie, cLICode = cltCode)
                }
                else -> {
                    patrimoineVerificationState = RemoteResponseState(error = "Type d'opération inconnu: $devInfo3")
                }
            }
        }
    }

    /**
     * Helper function to get the best available location description
     * Prioritizes descriptive names over codes
     */
    private suspend fun getLocationDisplayName(clientCode: String?): String {
        if (clientCode.isNullOrEmpty()) return "N/A"

        return try {
            // Get all immobilisation data and find the one with matching client code
            val immobilisationList = proCaisseLocalDb.immobilisation.getAll().firstOrNull()
            val immobilisation = immobilisationList?.find { it.cLICode == clientCode }

            if (immobilisation != null) {
                val nomPren = immobilisation.cLINomPren

                // Check if cLINomPren looks like a code (contains only alphanumeric and special chars like /, numbers)
                val isCodeLike = nomPren.matches(Regex("^[A-Z0-9/]+$")) ||
                                nomPren.matches(Regex("^[A-Z]+\\d+/\\d+$"))

                // If it looks like a code, try to find a better description
                if (isCodeLike) {
                    // For now, return the code but in the future we could look for better descriptions
                    // from parent immobilisation records or other sources
                    nomPren
                } else {
                    // Use the descriptive name
                    nomPren
                }
            } else {
                // Fallback to client code if immobilisation not found
                clientCode
            }
        } catch (e: Exception) {
            clientCode
        }
    }

    private fun verifyAffectationLoccaly(invPatByNumSeri: Map<BonCommande, List<LigneBonCommande>>) {

        // First check if article is already affected (like backend logic)
        if (invPatByNumSeri.isNotEmpty()) {
            val clientName = invPatByNumSeri.entries.firstOrNull()?.key?.dEVClientName ?: "N/A"
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "Article est deja affecte à '$clientName' (Local vérification)"
                )
            return
        }

        // Second check: Verify if scanned numSerie is a building barcode (like backend logic)
        // This matches the backend logic: $verifCodeBarClient = $connexion->table('client')->where('Clt_ImoCB', '=', $LG_DEV_NumSerie)->first();
        viewModelScope.launch {
            val listImmobilisation = proCaisseLocalDb.immobilisation.getAllZoneConsomationByImoCB(selectedPatrimoine.numSerie).firstOrNull()

            if (!listImmobilisation.isNullOrEmpty()) {
                val firstImmo = listImmobilisation.first()
                patrimoineVerificationState =
                    RemoteResponseState(
                        data = null,
                        loading = false,
                        error = "NumSerie est dejà affecté à l'emplacement '${firstImmo.cLINomPren}' (Local vérification)"
                    )
                return@launch
            }

            // If no conflicts found, allow the affectation
            patrimoineVerificationState =
                RemoteResponseState(
                    data = ControlInventaireResponse(
                        message = "Success Local vérification (Affectation)",
                        code = "10200"
                    ),
                    loading = false,
                    error = null
                )
        }

    }

    private fun verifyDepOutLoccaly(
        invPatByNumSeri: Map<BonCommande, List<LigneBonCommande>>,
        cLICode: String,
    ) {
        val invPat = invPatByNumSeri.entries.first().key

        if (invPat.dEV_info3 == null) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = NOT_FOUND_LOCALLY,
                )
            return
        }

        // Backend logic: Check if last operation was affectation/entree/inventaire
        // If last operation is NOT one of these, return error
        if (invPat.dEV_info3 != TypePatrimoine.AFFECTATION.typePat &&
            invPat.dEV_info3 != TypePatrimoine.ENTREE.typePat &&
            invPat.dEV_info3 != TypePatrimoine.INVENTAIRE.typePat) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "dernière type enregistrement different de (affectation/entree) (Local vérification)"
                )
            return
        }

        // Check if article belongs to the same client
        if (invPat.dEVCodeClient != cLICode) {
            patrimoineVerificationState = RemoteResponseState(
                data = null,
                loading = false,
                error = "Déjà affecté à '" + invPat.dEVClientName + "' (Local vérification)"
            )
        } else {
            patrimoineVerificationState =
                RemoteResponseState(
                    data =
                        ControlInventaireResponse(
                            message = "Success Local vérification (Sortie)",
                            code = "10200",
                        ),
                    loading = false,
                    error = null,
                )
        }
    }

    private fun verifyDepInLoccaly(
        invPatByNumSeri: Map<BonCommande, List<LigneBonCommande>>,
        cLICode: String,
    ) {
        val invPat = invPatByNumSeri.entries.first().key

        if (invPat.dEV_info3 == null) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = NOT_FOUND_LOCALLY,
                )
            return
        }

        // Backend logic: Check if last operation was sortie
        // If last operation is NOT sortie, return error
        if (invPat.dEV_info3 != TypePatrimoine.SORTIE.typePat) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "Dernier opération: " + invPat.dEV_info3 + ", Zone consomation '" + invPat.dEVClientName + "' (Local vérification)"
                )
            return
        }

        // If last operation was sortie, allow the entree operation
        patrimoineVerificationState =
            RemoteResponseState(
                data =
                    ControlInventaireResponse(
                        message = "Success Local vérification (Entree)",
                        code = "10200",
                    ),
                loading = false,
                error = null,
            )
    }

    private fun verifyInventaireLoccaly(
        invPatByNumSeri: Map<BonCommande, List<LigneBonCommande>>,
        cLICode: String,
    ) {
        val invPat = invPatByNumSeri.entries.first().key

        if (invPat.dEV_info3 == null) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = NOT_FOUND_LOCALLY, // result.message
                )
            return
        }
        // Backend logic: For inventaire, check if article is affected to different building/client
        // The backend checks if last_patrimoine !== $DEV_CodeClient && !empty($last_patrimoine)
        if (invPat.dEVCodeClient != cLICode && !invPat.dEVCodeClient.isNullOrEmpty()) {
            // Get the proper location name from immobilisation data
            viewModelScope.launch {
                val locationName = getLocationDisplayName(invPat.dEVCodeClient)
                patrimoineVerificationState =
                    RemoteResponseState(
                        data = null,
                        loading = false,
                        message = CODE_VERIFICATION_INVENTAIRE_EXIST,
                        error = "Article est affecte a autre batiment '$locationName' (Local vérification)",
                    )
            }
        } else {
            // Article is in the same building/client or not affected, allow inventaire
            patrimoineVerificationState =
                RemoteResponseState(
                    data =
                        ControlInventaireResponse(
                            message = "Success Local vérification (Inventaire)",
                            code = "10200",
                        ),
                    loading = false,
                    error = null,
                )
        }
    }
}

