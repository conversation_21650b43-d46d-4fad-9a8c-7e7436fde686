package com.asmtunis.procaisseinventory.articles.data.article.local.repository.articles

import androidx.paging.PagingData
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import kotlinx.coroutines.flow.Flow


interface ArticlesLocalRepository {

    fun upsert(value: Article)

    fun upsertAll(value: List<Article>)

    fun getByArtCode(value: String) : Flow<Article?>


    fun deleteAll()

    fun deleteByArtCode(aRTCode: String)

    fun getAll(): Flow<List<Article>>
    fun getNotSync(): Flow<List<Article>>
    fun allCount(): Flow<Int>

    fun updateSyncArticle(artCode : String)
    fun updateArtQteStock(newQteAllStations: String,newQteStation : String, codeArticle: String)

    fun filterByName(filterString : String, sortBy: String?, stock : String?, isAsc : Int?, filterByFamille: String, filterByMarque: String): Flow<PagingData<Article>>
    fun filterByBarCode(filterString : String, sortBy: String?, stock : String?, isAsc : Int?, filterByFamille: String, filterByMarque: String): Flow<PagingData<Article>>
    fun filterByPrice(filterString : String, sortBy: String?, stock : String?, isAsc : Int?, filterByFamille: String, filterByMarque: String): Flow<PagingData<Article>>


    fun getAllFiltred(sortBy: String?, stock : String?, isAsc : Int?, filterByFamille: String, filterByMarque: String): Flow<PagingData<Article>>

}