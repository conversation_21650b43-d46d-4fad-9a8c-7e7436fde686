package com.asmtunis.procaisseinventory.articles

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite

object ArticleOpeartions {

    fun getQuantity(article: Article): Double  {
        /**
         * pro inv vs pro caisse
         * TO verify when use stringToDouble(article.aRTQteStock) or  val qteStation =  article.sARTQte
         *
         * case old app it use sARTQte in procaisse mobility
         *
         */
        val qteStation = stringToDouble(article.aRTQteStock)
        //   val qteStation =  article.sARTQte


        //  val qteStation = stringToDouble(listStationStockArticl.firstOrNull{it.sARTCodeArt == filteredArticles[index].aRTCode }?.sARTQteStation)

        return qteStation
    }
    fun updateStationArticleQte(
        stationStockArticle: StationStockArticle,
        codeStation: String,
        opeartion: String = Globals.ADD,
        quatity: Double,
        aRTCode: String,
        updateQtePerStation: (
            newQteStation: String,
            newSartQteDeclare: String,
            codeArticle: String,
            codeStation: String
        ) -> Unit
    ) {

        val sARTQte = stringToDouble(stationStockArticle.sARTQteStation)
        val sartQteDeclare = stringToDouble(stationStockArticle.sARTQteDeclaree)

        val newQteStation = if (opeartion == Globals.ADD) quatity + sARTQte
        else sARTQte - quatity


        val newSartQteDeclare = if (opeartion == Globals.ADD) quatity + sartQteDeclare
        else sartQteDeclare - quatity

        updateQtePerStation(
            newQteStation.toString(),
            newSartQteDeclare.toString(),
            aRTCode,
            codeStation
        )


    }


    fun updateStationDestinationArticleQte(
        opeartion: String = Globals.ADD,
        sTATCode: String,
        quatity: Double,
        aRTCode: String,
        stationDestinationStockArticle: StationStockArticle,
        insertStationStockArticle: (stationStockArticl: StationStockArticle)-> Unit,
        updateQtePerStation:(newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        stationStockArticle: StationStockArticle
    ) {

        var qtePerStationDest = 0.0
        if (stationDestinationStockArticle != StationStockArticle()) {
            qtePerStationDest = stringToDouble(stationDestinationStockArticle.sARTQteStation)
        } else {
             insertStationStockArticle(stationStockArticle)
        }


        val newQtePerStation = if (opeartion == Globals.ADD) quatity + qtePerStationDest
        else qtePerStationDest - quatity

        val  sARTQteDeclaree = stringToDouble(stationDestinationStockArticle.sARTQteDeclaree)
        val newSartQteDeclare = if (opeartion == Globals.ADD) quatity + sARTQteDeclaree

        else sARTQteDeclaree - quatity
        updateQtePerStation(
            newQtePerStation.toString(),
            newSartQteDeclare.toString(),
           aRTCode,
            sTATCode
        )
    }


    fun updateArticleQty(
        operation: String = Globals.ADD,
        quantity: Double,
        article: Article,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {
        val aRTQteStock = stringToDouble(article.aRTQteStock)
        val newQteAllStations = if (operation == Globals.ADD) aRTQteStock + quantity
                else aRTQteStock - quantity


        val newQteStation = if (operation == Globals.ADD) aRTQteStock+ quantity else aRTQteStock - quantity

        updateArtQteStock(
            newQteAllStations.toString(),
            newQteStation.toString(),
            article.aRTCode
        )
    }









    fun selectedArticle(
        listSelectedArticleInventory: List<SelectedArticle>,
        article: Article
    ): SelectedArticle {
        return listSelectedArticleInventory.firstOrNull { it.article.aRTCode == article.aRTCode }?: SelectedArticle()
    }

    fun addItemToSelectedArticleListAndUpdateQty(
        article: Article,
        listUnite: List<Unite>,
        stationStockArticle: StationStockArticle,
        stockArticleAllStation: String,// todo see if put stockArticleAllStation in qteStationFromDB when is bon transfert
        controlQuantity : Boolean,
        currentSelectedArticle: SelectedArticle,
        tva : Tva,
        operation: String = Globals.ADD,
        addItemToSelectedArticleInventoryList: (articleToAdd: SelectedArticle) -> Unit
    ) {

        val unite = listUnite.firstOrNull { it.uNICode == article.uNITEARTICLECodeUnite }?: Unite(uNIDesignation = article.uNITEARTICLECodeUnite, uNICode = article.uNITEARTICLECodeUnite?: "")

        val selectedArticleInventory = SelectedArticle(
            article = article,
            unite = unite,
            stationStockArticleList = stationStockArticle,
            qteStationFromDB = stationStockArticle.sARTQteStation?: "0",
            tva = tva,
            quantity = if (operation == Globals.ADD)
                (stringToDouble(currentSelectedArticle.quantity) +  getArticleQt(controlQuantity = controlQuantity,quantityInStock = stringToDouble(stationStockArticle.sARTQteStation ?: "0"))).toString()
            else (stringToDouble(currentSelectedArticle.quantity) - getArticleQt(controlQuantity = controlQuantity, quantityInStock = stringToDouble(stationStockArticle.sARTQteStation ?: "0"))).toString(),
            prixCaisse = currentSelectedArticle.prixCaisse.ifEmpty { article.pvttc.toString() },
            prixAchatHt = currentSelectedArticle.prixAchatHt.ifEmpty { article.aRTPrixUnitaireHT },
            lTPuHT = currentSelectedArticle.lTPuHT.ifEmpty { article.aRTPrixUnitaireHT }
        )

         addItemToSelectedArticleInventoryList(selectedArticleInventory)
    }
//    fun updateQteStationFromDB(
//        stationCode: String,
//        selectedArticleList: MutableList<SelectedArticle>,
//        addItemToSelectedArticleInventoryList: (SelectedArticle) -> Unit,
//
//    ) {
//        // resetSelectedInventoryArticles()
//        // Update the qteStationFromDB for articles where the station code matches
//        selectedArticleList.replaceAll { selectedArticle ->
//            val updatedQuantity = selectedArticle.stationStockArticleList
//                .firstOrNull { it.sARTCodeSatation == stationCode }
//                ?.sARTQteStation ?: "0"
//
//            selectedArticle.copy(qteStationFromDB = updatedQuantity)
//        }
//
//        // Add each updated article to the inventory list
//        selectedArticleList.forEach{ addItemToSelectedArticleInventoryList(it) }
//    }

    fun updateQteStationFromDB(
        stationCode: String,
        selectedArticleList: MutableList<SelectedArticle>,
        stationStockArticl: Map<String, StationStockArticle>,
        addItemToSelectedArticleInventoryList: (SelectedArticle) -> Unit
    ) {

        val updatedList = selectedArticleList.map { selectedArticle ->
           // val updatedQuantity = selectedArticle.stationStockArticleList.filter { it.value.sARTCodeSatation == stationCode }?.sARTQteStation ?: "0"



            val updatedQuantity = stationStockArticl[selectedArticle.article.aRTCode + stationCode]?.sARTQteStation?: "0"

            selectedArticle.copy(qteStationFromDB = updatedQuantity)
        }

        // Clear the list and reassign items to trigger recomposition
        selectedArticleList.clear()
        selectedArticleList.addAll(updatedList)

        updatedList.forEach(addItemToSelectedArticleInventoryList)
    }
    fun getArticleQt(controlQuantity: Boolean,quantityInStock: Double): Double =
        if (quantityInStock < 1 && controlQuantity) quantityInStock else 1.0




    fun getStationStockArticle(
        opeartion: String = Globals.ADD,
        codeStation: String,
        codeArticle: String,
        stationStockArticle: StationStockArticle?,
        qty: Double,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit
    ) {


        if (stationStockArticle == null) return

        updateStationArticleQte(
            opeartion = opeartion,
            quatity = qty,
            aRTCode = codeArticle,
            codeStation = codeStation,
            stationStockArticle = stationStockArticle,
            updateQtePerStation = { newQteStation, newSartQteDeclare, codeArt, codeStat ->
                updateQtePerStation(
                    newQteStation,
                    newSartQteDeclare,
                    codeArt,
                    codeStat
                )
            }
        )

    }

    fun setSelectedArticleNoCalcul(
        scannedArticle: Article,
        stationOrigineCode: String,
        selectedArticleInventoryList: List<SelectedArticle>,
        stationStockArticlMapByBarCode: Map<String, StationStockArticle>,
        setSelectedArticlInventory: (value: SelectedArticle, from: String) -> Unit
    ) {

        val selectedArt = selectedArticleInventoryList.firstOrNull { it.article.aRTCode == scannedArticle.aRTCode }
        val listStationStockArticl = stationStockArticlMapByBarCode[scannedArticle.aRTCode + stationOrigineCode]?.sARTQteStation?: "0"//article.aRTQteStock


        if(selectedArt == null) {
            val value = SelectedArticle(
                article = scannedArticle,
                quantity = "1",
                prixAchatHt = scannedArticle.pvttc.toString(),
                qteStationFromDB = listStationStockArticl
            )
            setSelectedArticlInventory(value, "selectedArt vc ")
        }else {
            setSelectedArticlInventory(selectedArt, "selectedArt ")
        }
    }

}