package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.DateRange
import androidx.compose.material.icons.twotone.PersonOutline
import androidx.compose.material.icons.twotone.Print
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.core.utils.StringUtils.getClientName
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.roundToThreeDecimals
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model.BonRetourViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.TotalPriceView
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column.FiveColumnTable
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail


@Composable
fun BonRetourDetailPane(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    bonRetourViewModel: BonRetourViewModel,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    printViewModel: PrintViewModel,
    wifiPrintVM: WifiPrintViewModel,
    sunmiPrintManager: SunmiPrintManager,
    bluetoothVM: BluetoothViewModel,
    settingViewModel: SettingViewModel
) {

    val context = LocalContext.current
    val selectedBonRetour = bonRetourViewModel.selectedBonRetour
    val listLigneBonRetour = bonRetourViewModel.selectedListLgBonRetour


    val selectedArticleList = selectArtMobilityVM.selectedArticleList
    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val selectedBonRetourWithClient = bonRetourViewModel.selectedBonRetourWithClient
    val selectedListLgBonRetourWithArticle = bonRetourViewModel.selectedListLgBonRetourWithArticle

    val showPriceWithDiscount = stringToDouble(selectedBonRetourWithClient.bonRetour?.bORMntRemise)!=0.0

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData

    val utilisateur = mainViewModel.utilisateur
    val tvaList = mainViewModel.tvaList

    val firstTimeConnected = printViewModel.firstTimeConnected

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

//    LaunchedEffect(key1 = selectedBonRetour.bORNumero){
//        selectArtMobilityVM.resetSelectedMobilityArticles()
//            for (i in listLigneBonRetour.indices) {
//                val article = articleMapByBarCode[listLigneBonRetour[i].lIGBonEntreeCodeArt] ?: break
//                val qty = listLigneBonRetour[i].lIGBonEntreeQte
//              //  val mntDiscount = (article.pvttc - abs(stringToDouble(listLigneBonRetour[i].lIGBonEntreePUTTC) * stringToDouble(qty) ))
//
//                val mntDiscount = calculateDiscountAmount(amount = article.pvttc, discount = stringToDouble(listLigneBonRetour[i].lIGBonEntreeRemise))
//
//
//                val prixCaisse = stringToDouble(listLigneBonRetour[i].lIGBonEntreeMntTTC)/ stringToDouble(qty)
//
//                val selectedArticle = SelectedArticle(
//                    article = article,
//                    prixVente = article.pvttc.toString(),
//                    quantity = qty,
//                    prixCaisse = convertDoubleToDoubleFormat(prixCaisse),
//                    discount = listLigneBonRetour[i].lIGBonEntreeRemise,
//                    mntDiscount = mntDiscount.toString(),
//                    lTMtTTC = convertStringToDoubleFormat(listLigneBonRetour[i].lIGBonEntreeMntTTC),
//                    lTMtBrutHT = listLigneBonRetour[i].lIGPVENTEHT?: "0.0",
//                    tva = Tva(),
//                    mntTva = ""
//                )
//                selectArtMobilityVM.setConsultationSelectedArticleMobilityList(selectedArticle = selectedArticle)
//
//
//            }
//    }





    LaunchedEffect(key1 = firstTimeConnected){
        if(!firstTimeConnected) return@LaunchedEffect
        PrintFunctions.print(
            navigate = { navigate(it) },
            context = context,
            toaster = toaster,
            printParams = printParams,
            printViewModel = printViewModel,
            bluetoothVM = bluetoothVM,
            sunmiPrintManager = sunmiPrintManager,
            toPrintBT = {
                printViewModel.printBonRetour(
                    context = context,
                    bonRetourWithClient = selectedBonRetourWithClient,
                    lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
            },
            toPrintSunmi = {
                sunmiPrintManager.printBonRetour(
                    context = context,
                    bonRetourWithClient = selectedBonRetourWithClient,
                    lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
            }
        )

        printViewModel.onFirstTimeConnectedChange(firstConnect = false)
    }

        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = { popBackStack() },
                    navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                    title = selectedBonRetour.bORNumero,
                    actions = {
                        IconButton(
                            onClick = {

                                // Direct printing without note functionality
                                when {
                                    printParams.useSunmiPrinter -> {
                                        sunmiPrintManager.printBonRetour(
                                            context = context,
                                            bonRetourWithClient = selectedBonRetourWithClient,
                                            lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                                            utilisateur = utilisateur,
                                            printParams = printParams
                                        )
                                    }
                                    printParams.printViaWifi -> {
                                        wifiPrintVM.createBonRetourPDFFile(
                                            context = context,
                                            bonRetourWithClient = selectedBonRetourWithClient,
                                            lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                                            listener = {}
                                        )
                                    }
                                    else -> {
                                        // Bluetooth printing
                                        printViewModel.printBonRetour(
                                            context = context,
                                            bonRetourWithClient = selectedBonRetourWithClient,
                                            lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                                            utilisateur = utilisateur,
                                            printParams = printParams
                                        )
                                    }
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.Print,
                                contentDescription = stringResource(id = R.string.icn_search_clear_content_description)
                            )
                        }
                    }
                )
            }
        ) { padding ->

            if(printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }

            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {
                Spacer(modifier = Modifier.height(12.dp))

                ItemDetail(
                    modifier = Modifier.fillMaxWidth(0.85f),
                    title = stringResource(id = R.string.client_field_title),
                    dataText = getClientName(cltName = selectedBonRetour.bORNomfrs, cltCode = selectedBonRetour.bORCodefrs),
                    icon = Icons.TwoTone.PersonOutline,
                )
                Spacer(modifier = Modifier.height(9.dp))
                ItemDetail(
                        modifier = Modifier.fillMaxWidth(0.85f),
                        title = stringResource(id = R.string.date),
                        dataText = selectedBonRetour.bORDateFormatted?: "",
                        icon = Icons.TwoTone.DateRange
                        )
                Spacer(modifier = Modifier.height(9.dp))
                FiveColumnTable(
                    selectedListArticle = selectedArticleList,
                    onPress = { selectArtMobilityVM.setSelectedArticl(article = it.article, tvaList = tvaList) },
                    onLongPress = {},
                    onTap = { mainViewModel.setAddNewProductDialogueVisibility(true) },
                    firstColumn = { item->
                        articleMapByBarCode[item.article.aRTCode]?.aRTDesignation?:
                        context.resources.getString(R.string.article_introvable, " ("+ item.article.aRTCode+")")
                    },
                    secondColumn = { removeTrailingZeroInDouble(it.prixVente) },
                    thirdColumn = { removeTrailingZeroInDouble(it.quantity) },
                    forthColumn = { removeTrailingZeroInDouble(it.lTMtTTC) },
                    infoText = { TableTextUtils.infoText(selectedArticle = it) }
                )

                Spacer(modifier = Modifier.weight(1f))

                TotalPriceView(
                    totalPrice = roundToThreeDecimals(stringToDouble(selectedBonRetour.bORMntTTC)).toString(), // use + (addition) because bORMntTTC is negative
                    totalPriceAfterDiscount = roundToThreeDecimals(stringToDouble(selectedBonRetour.bORMntTTC) -  stringToDouble(selectedBonRetour.bORMntRemise)).toString(),
                    showPriceWithDiscount = showPriceWithDiscount
                )

                Spacer(modifier = Modifier.height(18.dp))
            }
    }
}