package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.screens

import android.Manifest
import android.app.Activity
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.twotone.Send
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.twotone.Edit
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.MainImageTiketRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcFormEvent
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcTextValidationViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AskPermission
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.image_pager.HorizontalImagePager
import com.asmtunis.procaisseinventory.shared_ui_components.image_pager.VerticalImagePager
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField

@OptIn(ExperimentalPermissionsApi::class,)
@Composable
fun NewProductDetailScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    cameraViewModel: CameraViewModel,
    mainViewModel: MainViewModel,
    networkViewModel: NetworkViewModel,
    vcTextValidationViewModel: VcTextValidationViewModel = hiltViewModel(),
    proCaisseViewModels: ProCaisseViewModels
) {
    val vcViewModel = proCaisseViewModels.veilleConcurentielViewModel
    val newProdViewModel = proCaisseViewModels.newProdViewModel

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val codeM = mainViewModel.codeM
    val imageCodeM = mainViewModel.imageCodeM

    val validationAddVcEvents = vcTextValidationViewModel.validationAddVcEvents

    val listImgeUri = cameraViewModel.listImgeUri

    val concurentList = mainViewModel.listConcurentVC
    val stateAddNewVc = vcTextValidationViewModel.stateAddNewVc
    val context = LocalContext.current

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val utilisateur = mainViewModel.utilisateur
    val concurrent = stateAddNewVc.concurrent
    val typeCommunication = stateAddNewVc.typeCommunication

    val modify = newProdViewModel.modify

    val imageUriList = cameraViewModel.imageUriList
    rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) {
        } else {
        }
    }
    // val state = rememberScrollState()
    // LaunchedEffect(Unit) { state.animateScrollTo(100) } // Smoothly scroll 100px on first composition

    val scrollState = rememberScrollState()

    val typeCommunicationVCList = vcViewModel.typeCommunicationVCList
    val newProductWithImage = newProdViewModel.selectedNewProduct
    val newProduct = newProductWithImage.newProductVC?: NewProductVC()
    val images = newProductWithImage.imageList?: emptyList()





    LaunchedEffect(key1 = validationAddVcEvents) {
        newProdViewModel.handleAddVisiteEvents(
            validationAddVcEvents = validationAddVcEvents,
            popBackStack = { popBackStack() },
            codeM = codeM,
            utilisateur = utilisateur,
            saveImageList = {
                    vcViewModel.saveImageList(
                        context = context,
                        userID = utilisateur.codeUt,
                        imageCodeM = imageCodeM,
                        vcCodeM = codeM,
                        imageUriList = imageUriList,
                    )
            }
        )
    }
  
  
    LaunchedEffect(key1 = Unit) {

        if(modify) return@LaunchedEffect
        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.produitChanged(newProduct.produitLanP))
        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.prixChanged(newProduct.prixLanP.toString()))
        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.tauxChanged(newProduct.tauxPromo.toString()))
        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.noteChanged(newProduct.noteOp?: ""))


    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.fournisseurChanged(concurentList.firstOrNull { it.codeconcurrent == newProduct.codeConcur }?: ConcurrentVC()))

    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.typeCommunicationChanged(typeCommunicationVCList.firstOrNull { it.codeTypeCom == newProduct.codeTypeCom }?: TypeCommunicationVC()))





    }




    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title =

                if (newProduct.status == ItemStatus.DELETED.status) {
                    stringResource(id = R.string.visite_deleted, newProduct.codeVCLanP)

                } else
                    if (newProduct.codeVCLanP != "") {
                        if (!modify) {
                            newProduct.codeVCLanP
                        } else stringResource(id = R.string.modification, newProduct.codeVCLanP)
                    } else stringResource(id = R.string.newprod),
            )
        },
        //    containerColor = colorResource(id = R.color.black),
        floatingActionButton = {
            FloatingActionButton(onClick = {
                if (newProduct.status != ItemStatus.DELETED.status) {
                    if (!modify) {
                        newProdViewModel.onModifyChange(true)
                    } else {
                        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.SubmitAddNewProductVc)
                    }
                } else {
                    showToast(
                        context = context,
                        toaster = toaster,
                        message = context.resources.getString(R.string.deja_supprimer),
                        type =  ToastType.Info,
                    )
                }

            }) {
                Icon(
                    imageVector = if (modify) Icons.AutoMirrored.TwoTone.Send else Icons.TwoTone.Edit,
                    contentDescription = stringResource(id = R.string.cd_addVisite_button)
                )
            }
        }
    ) { padding ->

        Column(
            modifier = Modifier
                .padding(padding)
                .verticalScroll(scrollState)
                // .fillMaxSize()
                .fillMaxWidth()
                .wrapContentHeight()
             //   .background(colorResource(id = R.color.white))
                .wrapContentSize(Alignment.Center),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            EditTextField(
                text = stateAddNewVc.produit,
                errorValue = stateAddNewVc.produitError?.asString(),
                label = stringResource(R.string.product),
                onValueChange = {
                    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.produitChanged(it))
                },
                readOnly = !modify,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )



            Row(
                modifier = Modifier.fillMaxWidth(0.95f),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                EditTextField(
                    modifier = Modifier.fillMaxWidth(0.45f),
                    text = stateAddNewVc.prix,
                    errorValue = stateAddNewVc.prixError?.asString(),
                    label = stringResource(R.string.prix),
                    onValueChange = {
                        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.prixChanged(it))
                    },
                    readOnly = !modify,
                    enabled = true,
                    leadingIcon = Icons.Default.DateRange,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )

                EditTextField(
                    modifier = Modifier
                        .fillMaxWidth(),
                    text = stateAddNewVc.taux,
                    errorValue = stateAddNewVc.tauxError?.asString(),
                    label = stringResource(R.string.taux),
                    onValueChange = {
                        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.tauxChanged(it))
                    },
                    readOnly = !modify,
                    enabled = true,
                    leadingIcon = Icons.Default.Home,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )


            }


            EditTextField(
                text = stateAddNewVc.note,
                errorValue = stateAddNewVc.noteError?.asString(),
                label = stringResource(R.string.note_field),
                onValueChange = {
                    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.noteChanged(it))
                },
                readOnly = !modify,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )

            Spacer(modifier = Modifier.height(9.dp))

            GenericDropdownMenu(
                modifier = Modifier.fillMaxWidth(0.95f),
                designation = typeCommunication.typeCommunication,
                errorValue = stateAddNewVc.typeCommunicationError?.asString(),
                label = stringResource(R.string.type_communication),
                readOnly = modify,
                itemList = typeCommunicationVCList,
                itemExpanded = vcViewModel.typeCommunicationExpanded,
                getItemTrailing = { it.codeTypeCom },
                getItemDesignation = { it.typeCommunication },
                selectedItem = typeCommunication,
                onClick = {
                    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.typeCommunicationChanged(it))
                    vcViewModel.onTypeCommunicationExpandedChange(false)
                },
                onItemExpandedChange = {
                    vcViewModel.onTypeCommunicationExpandedChange(it)
                },
                lottieAnimEmpty = {
                    LottieAnim(lotti = R.raw.emptystate)
                },
                lottieAnimError = {
                    LottieAnim(lotti = R.raw.connection_error, size = it)
                }
            )


            Spacer(modifier = Modifier.height(9.dp))

            GenericDropdownMenu(
                modifier = Modifier.fillMaxWidth(0.95f),
                designation =  concurrent.concurrent,
                errorValue = stateAddNewVc.concurrentError?.asString(),
                label = stringResource(R.string.concurrent),
                readOnly = modify,
                itemExpanded = vcViewModel.fournisseurExpanded,
                itemList =  concurentList,
                selectedItem = concurrent,
                getItemDesignation = { it.concurrent },
                getItemTrailing = { it.codeconcurrent },
                onClick = {
                    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.fournisseurChanged(it))
                    vcViewModel.onFournisseurExpandedChange(false)
                },
                onItemExpandedChange = {
                    vcViewModel.onFournisseurExpandedChange(it)
                },
                lottieAnimEmpty = {
                    LottieAnim(lotti = R.raw.emptystate)
                },
                lottieAnimError = {
                    LottieAnim(lotti = R.raw.connection_error, size = it)
                }
            )


            Spacer(modifier = Modifier.height(12.dp))
            HorizontalImagePager(
                onClicks = {

                    cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
            },
                canModify = modify,
                cameraViewModel = cameraViewModel,

                imageList = listImgeUri,
                onDeleteClick = {
                    vcViewModel.onImageDeleted(it)
                    if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                }
            )

            if(cameraViewModel.openVerticalalImagePagerDialog && listImgeUri.isNotEmpty()){
                VerticalImagePager(
                    canModify = modify,
                    onDismissRequest = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(false)
                    },
                    imageList  = listImgeUri,
                    onDeleteClick = {
                        vcViewModel.onImageDeleted(it)
                        if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    }
                )
            }
            /*HorizontalPagerIndicator(
                pagerState = pagerState,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(16.dp),
            )*/










            if (modify) {
                AskPermission(
                    permission = listOf(
                        Manifest.permission.CAMERA
                    ),
                    permissionNotAvailableContent = { permissionState ->
                        Column(
                            modifier = Modifier
                                //  .background(colorResource(id = R.color.black))
                                .fillMaxSize(),
                            // .padding(padding),
                            verticalArrangement = Arrangement.Center,
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            LottieAnim(lotti = R.raw.emptystate)
                            Spacer(modifier = Modifier.height(16.dp))

                            val textToShow = if (permissionState.shouldShowRationale) {
                                stringResource(R.string.access_camera_request_permession)
                            } else {
                                stringResource(R.string.camera_not_available)
                            }
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(textToShow)
                            Spacer(modifier = Modifier.height(8.dp))
                            Button(onClick = { permissionState.launchMultiplePermissionRequest() }) {
                                Text( stringResource(R.string.request_camera_auth))
                            }
                        }
                    },
                    content = {
                        Spacer(modifier = Modifier.height(12.dp))
                        Button(
                            modifier = Modifier.wrapContentWidth(),
                            onClick = {
                                cameraViewModel.onNumChange(value = imageCodeM)
                                navigate(MainImageTiketRoute)

                            }
                        ) {

                            Text(text = if (cameraViewModel.imageUriList.isNotEmpty()) context.getString(R.string.prendreautrephotos)
                            else context.getString(R.string.prendrephotos))
                        }
                        Spacer(modifier = Modifier.height(16.dp))

                    }
                )
            }
        }
    }
}


//@Composable
//fun <T> GenericDropdownMenu(
//    modifier: Modifier = Modifier.fillMaxWidth(0.45f),
//    label: String,
//    getItemImageUrl: (T) -> String = { "" },
//    designation: String,
//    fiterValue: String = "",
//    onFilterValueChange: (String) -> Unit = {},
//    showFilter: Boolean = false,
//    showFilterIcon: Boolean = false,
//    isSync:(T) -> Boolean = {true},
//    errorValue: String? = null,
//    itemList: List<T>,
//    itemExpanded: Boolean,
//    readOnly: Boolean = true,
//    selectedItem: T,
//    getItemDesignation: (T) -> String, // Function to get item designation
//    getItemTrailing: (T) -> String = { "" }, // Function to get item designation
//    getItemSyncStatus: (T) -> Boolean = { true }, // Function to get item designation
//    onClick: (T) -> Unit = {},
//    onItemExpandedChange: (Boolean) -> Unit = {},
//    lottieAnimEmpty: @Composable () -> Unit,
//    lottieAnimError: @Composable (Dp) -> Unit
//) {
//
//    Box(
//        modifier = modifier.height(IntrinsicSize.Min),
//        contentAlignment = Alignment.Center
//    ) {
//
////        Text(
////            text = designation,
////            modifier = Modifier.clickable {
////            if(!readOnly) {
////                onFilterValueChange("")
////                onItemExpandedChange(true)
////            }
////        })
//
//        Row(Modifier.fillMaxWidth(),
//            horizontalArrangement = Arrangement.Center,
//        verticalAlignment = Alignment.CenterVertically
//            ) {
//            EditTextField(
//                modifier = Modifier.customWidth(if(readOnly) 0.8f else 0.95f),
//
//                text = designation,
//                errorValue = errorValue,
//                label = label,
//                onValueChange = {
//                },
//                readOnly = true,
//                enabled = true,
//                showLeadingIcon = false,
//                showTrailingIcon = false,
//                leadingIcon = Icons.AutoMirrored.Filled.List,
//                keyboardType = KeyboardType.Number,
//                imeAction = ImeAction.Next
//            )
//            AnimatedVisibility(
//                visible = readOnly,
//                enter = fadeIn() + slideInVertically(),
//                exit = fadeOut() + slideOutVertically()
//            ) {
//                Icon(
//                    modifier = Modifier
//                        //.padding(start = 12.dp)
//                        .size(35.dp)
//                        .clickable {
//                               onFilterValueChange("")
//                                onItemExpandedChange(true)
//                        },
//                    tint = MaterialTheme.colorScheme.error,
//                    imageVector= if(itemExpanded) Icons.TwoTone.ExpandLess else Icons.TwoTone.ExpandMore,
//                    contentDescription = "Delete Image Number"
//                )
//            }
//
//        }
//
//
//
//
//
//        DropdownMenu(
//         //   modifier = Modifier.fillMaxHeight(0.70f),
//            expanded = itemExpanded,
//            onDismissRequest = { onItemExpandedChange(false) }
//        ) {
//            if(showFilter) {
//                SearchView(
//                    modifier = Modifier.fillMaxWidth()
//                        .align(Alignment.CenterHorizontally)
//                        .padding(12.dp),
//                    label = "Filter $label",
//                    errorValue = errorValue,
//                    onClearClick = {
//                        onFilterValueChange("")
//                    },
//                    onShowSearchViewChange = {},
//                    onSearchValueChange = { query ->
//                        onFilterValueChange(query)
//
//                    },
//                    showLeadingIcon = false,
//                    showFilterIcon = showFilterIcon,
//                    state = fiterValue,
//                    onShowCustomFilterChange = {}
//                )
//            }
//
//            if(itemList.isEmpty()) {
//
//                //    LottieAnim(lotti = R.raw.emptystate)
//                lottieAnimEmpty()
//                return@DropdownMenu
//            }
//            itemList.forEachIndexed { index, item ->
//                if (index > 0) {
//                    HorizontalDivider(modifier = Modifier.padding(start = 9.dp, end = 9.dp))
//                }
//                DropdownMenuItem(
//                    text = { Text(getItemDesignation(item)) },
//                    onClick = {
//                        onItemExpandedChange(false)
//                        onClick(item)
//                    },
//                    leadingIcon = {
//                        if (selectedItem == item) {
//                            Icon(Icons.Outlined.Check, contentDescription = null)
//                        }
//
//                        if(!isSync(item)) {
//                            //  lottieAnimError(lotti = R.raw.connection_error, size = 25.dp)
//                            lottieAnimError(25.dp)
//                        }
//                    },
//                    trailingIcon = {
//                        if(getItemTrailing(item).isNotEmpty())
//                            Text(getItemTrailing(item), textAlign = TextAlign.Center, color = if (!getItemSyncStatus(item)) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary)
//
//                    }
//                )
//
//                if(getItemImageUrl(item).isNotEmpty()) {
//                    Box(modifier = Modifier.fillMaxWidth().height(85.dp),
//                        contentAlignment = Alignment.Center) {
//                        AppImage(
//                            imageUrl = getItemImageUrl(item),
//                            contentDescription = "logo",
//                            modifier = Modifier
//                                // .clip(RoundedCornerShape(10.dp))
//                                .padding(start = 3.dp, bottom = 3.dp, top = 3.dp)
//                                .clip(RectangleShape)
//                                .size(80.dp)
//                                .border(2.dp, Color.Black, RectangleShape)
//
//                        )
//                    }
//
//                }
//            }
//
//
//        }
//    }
//
//}
//@Composable
//fun EditTextField(
//    modifier: Modifier = Modifier.fillMaxWidth(0.95f),
//    suffix: String = "",
//    requestFocus: Boolean = false,
//    showLeadingIcon : Boolean = false,
//    showTrailingIcon : Boolean = false,
//    text: String,
//    errorValue: String? = null,
//    label: String,
//    leadingIcon: ImageVector = Icons.Default.Home,
//    imeAction: ImeAction = ImeAction.Next,
//    enabled: Boolean = true,
//    readOnly: Boolean = false,
//    onValueChange: (String) -> Unit,
//    keyboardType: KeyboardType = KeyboardType.Text,
//    onKeyboardActions: () -> Unit = { },
//    onClick: () -> Unit = { },
//
//
//    ) {
//    val isError = !errorValue.isNullOrEmpty()
//
//
//    val keyboardController = LocalSoftwareKeyboardController.current
//
//    // LaunchedEffect prevents endless focus request
//
//    val focusRequester = remember { FocusRequester() }
//    LaunchedEffect(focusRequester) {
//        if(!requestFocus) return@LaunchedEffect
//        focusRequester.requestFocus()
//        delay(100) // Make sure you have delay here
//        keyboardController?.show()
//
//    }
//
//    val interactionSource = remember { MutableInteractionSource() }
//
//    LaunchedEffect(interactionSource) {
//        interactionSource.interactions.collect { interaction ->
//            if (interaction is PressInteraction.Press) {
//                // Your click action here
//
//                onClick()
//
//            }
//        }
//    }
//
//
//    OutlinedTextField(
//        modifier = modifier.focusRequester(focusRequester)
//            //todo when isError change the keyboard hide! potential solution but cursur not fixed
//            .onFocusChanged { focusState ->
//                if (focusState.isFocused && isError) {
//                    keyboardController?.show()
//                }
//            },
//        interactionSource = interactionSource,
//        value = text,
//        onValueChange = {
//            onValueChange(it)
//        },
//        readOnly = readOnly,
//        enabled = enabled,
//        maxLines = 2,
//        label = {
//            Text(
//                text =label,
//                maxLines = 2,
//                fontSize = 12.sp
//            )
//        },
//
//        trailingIcon = if(showTrailingIcon) {
//            {
//                AnimatedVisibility(
//                    visible = text.isNotEmpty() && enabled,
//                    enter = fadeIn(),
//                    exit = fadeOut()
//                ) {
//
//
//                    IconButton(onClick = { onValueChange("") }) {
//                        Icon(
//                            imageVector = Icons.TwoTone.Clear,
//                            contentDescription = "clear"
//                        )
//                    }
//                }
//            }
//
//        } else null ,
//
//        suffix = if(suffix.isNotEmpty()) {
//            {
//                Text(text = suffix)
//            }
//
//        } else null,
//        supportingText = if(errorValue != null) {
//            {
//
//                Text(
//                    text = errorValue,
//                    color = MaterialTheme.colorScheme.error,
//                    fontSize = MaterialTheme.typography.labelSmall.fontSize,
//
//                    )
//            }
//        }
//        else null,
//        shape = MaterialTheme.shapes.medium,
//        singleLine = true,
//        isError = isError, //todo when isError change the keyboard hide!
//        /*leadingIcon = {
//           if(showLeadingIcon) Icon(
//                leadingIcon,
//                contentDescription = null,
//                //tint = Color.Black
//            )
//        },*/
//        keyboardOptions = KeyboardOptions(
//            autoCorrect = true,
//            keyboardType = keyboardType,
//            imeAction = imeAction
//        ),
//        keyboardActions =
//        if(
//            imeAction == ImeAction.Done ||
//            imeAction == ImeAction.Search ||
//            imeAction == ImeAction.Send ||
//            imeAction == ImeAction.Go
//        ) {
//            KeyboardActions {
//
//                keyboardController?.hide()
//                onKeyboardActions()
//            }
//
//        }
//        else  KeyboardActions.Default,
//    )
//
//
//
//
//
//}










