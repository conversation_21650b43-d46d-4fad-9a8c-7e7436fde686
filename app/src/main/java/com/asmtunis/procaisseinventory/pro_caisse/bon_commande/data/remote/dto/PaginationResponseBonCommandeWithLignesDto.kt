package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.dto

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonIgnoreUnknownKeys

/**
 * Pagination response data class for BonCommande with nested lignes_devis
 * This is used only for API response parsing and not stored in database
 */
@OptIn(ExperimentalSerializationApi::class)
@Serializable
@JsonIgnoreUnknownKeys
data class PaginationResponseBonCommandeWithLignes(
    @SerialName("data")
    val data: List<BonCommandeWithLignes>?,
    @SerialName("count")
    val count: Int = 0,

    // Optional pagination fields that may or may not be present
    @SerialName("current_page")
    val currentPage: Int? = null,
    @SerialName("first_page_url")
    val firstPageUrl: String? = null,
    @SerialName("from")
    val from: Int? = null,
    @SerialName("last_page")
    val lastPage: Int? = null,
    @SerialName("last_page_url")
    val lastPageUrl: String? = null,
    @SerialName("next_page_url")
    val nextPageUrl: String? = null,
    @SerialName("path")
    val path: String? = null,
    @SerialName("per_page")
    val perPage: String? = null,
    @SerialName("prev_page_url")
    val prevPageUrl: String? = null,
    @SerialName("to")
    val to: Int? = null,
    @SerialName("total")
    val total: String? = null
) {
    /**
     * Helper property to get total as string with fallback
     */
    val totalAsString: String
        get() = total ?: count.toString()
}
