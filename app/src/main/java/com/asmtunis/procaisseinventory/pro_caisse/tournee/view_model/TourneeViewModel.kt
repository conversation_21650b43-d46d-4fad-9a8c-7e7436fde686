package com.asmtunis.procaisseinventory.pro_caisse.tournee.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMissionWithClient
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMissionWithEtat
import com.google.maps.android.compose.MapType
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
    class TourneeViewModel @Inject constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        savedStateHandle : SavedStateHandle
    ) : ViewModel() {

    var selectedDate  by  mutableStateOf("")
        private set

    fun onSelectedDateChange(value: String){
        selectedDate = value
    }

    var selectedOrdMissionWithLigneAndCltValidCoordinates  =  mutableStateListOf(LigneOrdreMissionWithClient())
        private set

    var selectedOrdMissionWithLigneAndCltNoValidCoordinates  =  mutableStateListOf(LigneOrdreMissionWithClient())
        private set

    var etatOrdreMission  =  mutableStateListOf(EtatOrdreMission())
        private set

    init {
        getOrdreMissionByDate()
        getEtatOrdreMissionList()
        selectedOrdMissionWithLigneAndCltValidCoordinates.clear()
        selectedOrdMissionWithLigneAndCltNoValidCoordinates.clear()
    }



var mapTypeList = listOf(MapType.HYBRID, MapType.NONE, MapType.NORMAL, MapType.TERRAIN, MapType.SATELLITE)
    var mapType: MapType by  mutableStateOf(MapType.HYBRID)
        private set

    fun onMapTypeChange(value: MapType){
        mapType = value
    }


    var showMapType  by  mutableStateOf(false)
    private set

   fun onShowMapTypeChange(value: Boolean){
       showMapType = value
    }









    var ordreMissionWithLines:Map<OrdreMissionWithEtat, List<LigneOrdreMissionWithClient>>  by  mutableStateOf(emptyMap())
        private set

    var ordreMissionWithEtat  =  mutableStateListOf(OrdreMissionWithEtat())
        private set

    var ligneOrdreMission  =  mutableStateListOf(LigneOrdreMissionWithClient())
        private set
     private fun getOrdreMissionByDate(){
        viewModelScope.launch {
            proCaisseLocalDb.ordreMission.getAll().collect {
         //   proCaisseLocalDb.ordreMission.getBydate(selectedDate).collect {
                ordreMissionWithLines = emptyMap()
                ordreMissionWithEtat.clear()
                ligneOrdreMission.clear()
                if(it.isNullOrEmpty()) return@collect

                ordreMissionWithLines = it.filter { (it.key.ordreMission?.oRDDate?.substring(0, 10)?: "") == selectedDate }



                it.forEach { (key, value) ->
                    run {
                        ordreMissionWithEtat.add(key)
                        ligneOrdreMission.addAll(value)
                    }
                }
            }
        }
    }


    private fun getEtatOrdreMissionList(){
        viewModelScope.launch {
            proCaisseLocalDb.etatOrdreMission.getAll().collect {
                etatOrdreMission.clear()
          if(it==null) return@collect

                etatOrdreMission.addAll(it)
            }
        }
    }


    var showAllCltOnCarte  by  mutableStateOf(false)
        private set
    fun setShowAllCltOnCarteVisibility(value: Boolean){
        showAllCltOnCarte = value
    }


    var showEtatOrdreMission  by  mutableStateOf(false)
        private set
     fun setVisibilityEtatOrdreMission(value: Boolean){
         showEtatOrdreMission = value
    }



    var showListOrdreMission  by  mutableStateOf(false)
        private set
    fun setVisibilityListEtatOrdreMission(value: Boolean){
        showListOrdreMission = value
    }


    var showListCltWithNoAdress  by  mutableStateOf(false)
        private set
    fun setVisibilityListCltWithNoAdress(value: Boolean){
        showListCltWithNoAdress = value
    }

    var onMarkerLongClicked  by  mutableStateOf(false)
        private set

    fun onMarkerLongClickedChange(value: Boolean){
        onMarkerLongClicked = value
    }

    var onMarkerClicked  by  mutableStateOf(false)
        private set

    fun onMarkerClickedChange(value: Boolean){
        onMarkerClicked = value
    }

    var selectedlgOrdMissionWithClt  by  mutableStateOf(LigneOrdreMissionWithClient())
        private set

    fun onSelectedlgOrdMissionWithClt(value: LigneOrdreMissionWithClient){
        selectedlgOrdMissionWithClt = value

        onSelectedEtatChange("")
    }



    var etatRemarque  by  mutableStateOf("")
        private set

    fun onEtatRemarqueChange(value: String){
        etatRemarque = value
    }


    var clientSearchText  by  mutableStateOf("")
        private set

    fun onClientSearchTextChange(value: String){
        clientSearchText = value
    }

    var selectedEtat  by  mutableStateOf("")
        private set

    fun onSelectedEtatChange(value: String){
        selectedEtat = value
    }

    var selectedOrdreMissionWithEtat  by  mutableStateOf(OrdreMissionWithEtat())
        private set
  /*  var selectedLgOrdMissionWithCoordClient  =  mutableStateListOf(LigneOrdreMissionWithClient())
        private set

    var selectedLgOrdMissionWithNoCoordClient  =  mutableStateListOf(LigneOrdreMissionWithClient())
        private set*/
    fun setSelectedOrdMission(value: Map<OrdreMissionWithEtat, List<LigneOrdreMissionWithClient>>){
       selectedOrdMissionWithLigneAndCltValidCoordinates.clear()
        selectedOrdMissionWithLigneAndCltNoValidCoordinates.clear()

       selectedOrdreMissionWithEtat = OrdreMissionWithEtat()

        value.forEach { (key, value) ->
            run {
                selectedOrdreMissionWithEtat = key

                for (ligneOrdreMission in value) {
                    /**
                     * TO USE WHEN WE TEST ON CLT LAT / LONG ///// TO SEE WITH AHMED
                     */
                 /*  if(ligneOrdreMission.client!=null) {
                          if(ligneOrdreMission.client!!.cltLatitude!=null &&
                              ligneOrdreMission.client!!.cltLongitude!=null &&
                              ligneOrdreMission.client!!.cltLatitude!= 0.0 &&
                              ligneOrdreMission.client!!.cltLongitude!= 0.0)
                              selectedLgOrdMissionWithCoordClient.add(ligneOrdreMission)
                          else
                              selectedLgOrdMissionWithNoCoordClient.add(ligneOrdreMission)
                      }
                      else
                          selectedLgOrdMissionWithNoCoordClient.add(ligneOrdreMission)
*/

                    if(
                        ligneOrdreMission.ligneOrdreMission.lIGOR_Latitude!= 0.0 &&
                        ligneOrdreMission.ligneOrdreMission.lIGOR_Longitude!= 0.0
                        ){
                        selectedOrdMissionWithLigneAndCltValidCoordinates.add(ligneOrdreMission)
                    }

                    else {

                        if(ligneOrdreMission.client == null) ligneOrdreMission.client = Client(
                            cLINomPren = "N/A",
                            cLICode = ligneOrdreMission.ligneOrdreMission.lIGORClt,
                            cLIType = "N/A",
                            cLIAdresse = "N/A",
                            cLISolde = "N/A")
                        selectedOrdMissionWithLigneAndCltNoValidCoordinates.add(ligneOrdreMission)
                    }



                }

            }
        }
    }


     fun updateLigneOrdreMission(
        ligneOrdreMission: LigneOrdreMission,
        remarque : String,
        etatOrdreMission: String,
        latitude: Double,
        longitude: Double
    ) {


        viewModelScope.launch(dispatcher) {
            var lgOrdreMission = ligneOrdreMission

            lgOrdreMission = lgOrdreMission.copy(//ask What if ET000002 is not TERMINE !!!
                lIGOR_Latitude =  if (etatOrdreMission == "ET000002") latitude else ligneOrdreMission.lIGOR_Latitude,
                lIGOR_Longitude = if (etatOrdreMission == "ET000002") longitude else ligneOrdreMission.lIGOR_Longitude,
                lIGOR_Date = getCurrentDateTime(),
                lIGORNote = remarque,
                lIGOREtat = etatOrdreMission
            )


            lgOrdreMission.isSync = false


            proCaisseLocalDb.ligneOrdreMission.upsert(lgOrdreMission)
        }

    }


}
