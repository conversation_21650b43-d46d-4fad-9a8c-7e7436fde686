package com.asmtunis.procaisseinventory.pro_caisse.tournee.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.ChangeLigneOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMission
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
class SyncTourneeViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proCaisseRemote: ProCaisseRemote,
    private val listenNetwork: ListenNetwork,
    savedStateHandle : SavedStateHandle
) : ViewModel() {
    var lgOrdMissionNotSync: List<LigneOrdreMission> by mutableStateOf(emptyList())
        private set


    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    init {
    getLgOrdMissionNotSync()
}

    private fun getLgOrdMissionNotSync(){
        viewModelScope.launch {
            val lgOrdMissionNotSyncFlow =  proCaisseLocalDb.ligneOrdreMission.notSync().distinctUntilChanged()


            combine(networkFlow, lgOrdMissionNotSyncFlow, autoSyncFlow) { isConnected, lgOrdMissionNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                lgOrdMissionNotSyncList?.ifEmpty { emptyList() }?: emptyList()
            }.collect {

                if (it.isEmpty()) {
                    lgOrdMissionNotSync = emptyList()
                    return@collect
                }
                lgOrdMissionNotSync = it
                if(connected && autoSyncState) syncLgOrdreMission()
            }







        }
    }


    var batchUpdateLigneOrdreMissionState: RemoteResponseState<List<ChangeLigneOrdreMission>>  by mutableStateOf(RemoteResponseState())
        private set

    var notSyncBatchUpdateLigneOrdreMissionObj : String by mutableStateOf("")
        private set
    fun resetUpdateLgOedMissionSatate() {
        batchUpdateLigneOrdreMissionState = RemoteResponseState(data = null, loading = false, error = null)

    }

    fun syncLgOrdreMission() {
        viewModelScope.launch(dispatcher) {


            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                // Json.decodeFromString(proCaisseLocalDb.dataStore.getString("Seleceted_BASE_CONFIG")),
                Json.encodeToJsonElement(lgOrdMissionNotSync)
            )
            notSyncBatchUpdateLigneOrdreMissionObj = Json.encodeToString(baseConfigObj)
/**
 * Change response from bool to state of each line
 */
            proCaisseRemote.ordreMission.batchUpdateLigneOrdreMission(notSyncBatchUpdateLigneOrdreMissionObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        var msg: String? = null
                        for (lgOrdreMission in result.data!!) {
                            if(lgOrdreMission.code == 10200){
                                proCaisseLocalDb.ligneOrdreMission.updateLgOrdreMissionNotSync(lgOrdreMission.oRDCode)

                                if(lgOrdreMission.message == "THE MISSION ORDER IS COMPLETED"){
                                    proCaisseLocalDb.ordreMission.updateOrdreMissionEtat(oRDCode = lgOrdreMission.oRDCode, codeEtat = "ET000002")

                                    msg = lgOrdreMission.message
                                }

                            }
                        }
                        /**
                         * TO DO ADD LIST ERROR TO REFER TO EACH LIGNE ERROR
                         */
                        batchUpdateLigneOrdreMissionState = RemoteResponseState(data = result.data, loading = false, error = msg)
                    }

                    is DataResult.Loading -> {
                        batchUpdateLigneOrdreMissionState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        batchUpdateLigneOrdreMissionState = RemoteResponseState(data = null, loading = false, error = result.message)
                    }

                    else -> {
                        batchUpdateLigneOrdreMissionState = RemoteResponseState(data = null, loading = false, error = "Unknow Error")

                    }
                }
            }.flowOn(dispatcher).launchIn(this)
        }
    }

}