package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.respository

import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.dao.ReglementCaisseDAO
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.domaine.ReglementWithChequeAndTicketResto
import kotlinx.coroutines.flow.Flow

class ReglementCaisseLocalRepositoryImpl(
    private val reglementCaisseDAO: ReglementCaisseDAO,
) : ReglementCaisseLocalRepository {
    override fun getBySession(station: String, caisse: String): Flow<List<ReglementCaisse>?> = reglementCaisseDAO.getBySession(station, caisse)

    override fun getByStation(station: String): Flow<List<ReglementCaisse>?> = reglementCaisseDAO.getByStation(station)

    override fun upsert(value: ReglementCaisse) = reglementCaisseDAO.insert(value)

    override fun upsertAll(value: List<ReglementCaisse>) = reglementCaisseDAO.insertAll(value)

    override fun updateRegCodeAndState(
        regCode: String,
        regCodeM: String,
    ) = reglementCaisseDAO.updateRegCodeAndState(regCode, regCodeM)

    override fun deleteAll() = reglementCaisseDAO.deleteAll()

    override fun deleteByCode(
        code: String,
        exercice: String,
        idCaisse: String,
    ) = reglementCaisseDAO.deleteByCode(code = code, exercice = exercice, idCaisse = idCaisse)

    override fun getAll(): Flow<List<ReglementCaisse>> = reglementCaisseDAO.all

    override fun getbyRegRemarque(
        session: String,
        station: String,
        regRemarque: String,
    ): Flow<String?> =
        reglementCaisseDAO.getbyRegRemarque(
            session = session,
            station = station,
            regRemarque = regRemarque,
        )

    override fun getMntEspece(
        station: String,
        caisse: String,
    ): Flow<String?> = reglementCaisseDAO.getMntEspece(station = station, caisse = caisse)

    override fun getMntCheque(
        station: String,
        caisse: String,
    ): Flow<String?> = reglementCaisseDAO.getMntCheque(station = station, caisse = caisse)

    override fun getMntTraite(
        station: String,
        caisse: String,
    ): Flow<String?> = reglementCaisseDAO.getMntTraite(station = station, caisse = caisse)

    override fun getReglementByClient(codeClt: String): Flow<List<ReglementCaisse>> = reglementCaisseDAO.getReglementByClient(codeClt)

    override fun getAllNotSynced(): Flow<List<ReglementWithChequeAndTicketResto>?> = reglementCaisseDAO.getAllNotSynced()

    override fun getAllFiltred(
        isAsc: Int,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        sortBy: String,
        station: String,
    ): Flow<List<ReglementCaisseWithTicketAndClient>> =
        reglementCaisseDAO.getAllFiltred(
            isAsc = isAsc,
            filterByTypePayment = filterByTypePayment,
            filterByTypeReglement = filterByTypeReglement,
            sort_by = sortBy,
            station = station,
        )

    override fun filterByClient(
        searchString: String,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        sortBy: String?,
        isAsc: Int?,
        station: String,
    ): Flow<List<ReglementCaisseWithTicketAndClient>> =
        reglementCaisseDAO.filterByClient(
            searchString = searchString,
            filterByTypePayment = filterByTypePayment,
            filterByTypeReglement = filterByTypeReglement,
            sort_by = sortBy,
            isAsc = isAsc,
            station = station,
        )

    override fun filterByRegNumTicket(
        searchString: String,
        sortBy: String,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        isAsc: Int,
        station: String,
    ): Flow<List<ReglementCaisseWithTicketAndClient>> =
        reglementCaisseDAO.filterByRegNumTicket(
            searchString = searchString,
            sort_by = sortBy,
            filterByTypePayment = filterByTypePayment,
            filterByTypeReglement = filterByTypeReglement,
            isAsc = isAsc,
            station = station,
        )

    override fun filterByRegCode(
        searchString: String,
        sortBy: String,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        isAsc: Int,
        station: String,
    ): Flow<List<ReglementCaisseWithTicketAndClient>> =
        reglementCaisseDAO.filterByRegCode(
            searchString = searchString,
            sort_by = sortBy,
            filterByTypePayment = filterByTypePayment,
            filterByTypeReglement = filterByTypeReglement,
            isAsc = isAsc,
            station = station,
        )
}
