@file:OptIn(SavedStateHandleSaveableApi::class, SavedStateHandleSaveableApi::class,
    SavedStateHandleSaveableApi::class
)

package com.asmtunis.procaisseinventory.pro_caisse.dashboard

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.domaine.ReglementPayments
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
    class DashboardScreenViewModel @Inject constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        savedStateHandle : SavedStateHandle
    ) : ViewModel() {



    @OptIn(SavedStateHandleSaveableApi::class)
    var showDetailVente by savedStateHandle.saveable { mutableStateOf(false) }
        private set

    fun onShowDetailVenteChange(value: Boolean) {
         showDetailVente = value
    }


    var reglementPayments by mutableStateOf(ReglementPayments())


      fun onCAChange(idSCaisse: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getCA(idSCaisse = idSCaisse).collectLatest { ca ->
                reglementPayments = reglementPayments.copy(ca = ca)
            }
        }
    }

    fun onCAChangeByStation(station: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getCAByStation(station = station).collectLatest { ca ->
                reglementPayments = reglementPayments.copy(ca = ca)
            }
        }
    }




    fun onBlNbrChange(idSCaisse: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getTicketNumber(idSCaisse = idSCaisse).collectLatest { nbrBl ->
                reglementPayments = reglementPayments.copy(nbrBl = nbrBl)
            }
        }
    }

    fun onBlNbrChangeByStation(station: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getTicketNumberByStation(station = station).collectLatest { nbrBl ->
                reglementPayments = reglementPayments.copy(nbrBl = nbrBl)
            }
        }
    }



      fun onListNTopClientsChange(idSCaisse: String?, number: Int) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.bonLivraison.getNTopClientsBySCaisse(idSCaisse = idSCaisse, number = number).collectLatest { listClients ->
                reglementPayments = reglementPayments.copy(listNTopClients = listClients)
            }
        }
    }



    fun getReglementListBySession(station: String, idSCaisse: String, regRemarque: String) {
        viewModelScope.launch {
            proCaisseLocalDb.reglementCaisse.getBySession(station = station, caisse = idSCaisse).collectLatest { list->
             val listReglementBySession = list?: emptyList()

                val mntEspece = listReglementBySession.sumOf { it.rEGCMntEspece?: 0.0 }
                val mntCheque = listReglementBySession.sumOf { it.rEGCMntCheque?: 0.0 }
                val mntTraite = listReglementBySession.sumOf { it.rEGCMntTraite?: 0.0 }
                val mntReglement = listReglementBySession.filter { it.rEGCRemarque == regRemarque }.sumOf { it.rEGCMontant?: 0.0 }


                reglementPayments = reglementPayments.copy(
                    mntEspece = mntEspece,
                    mntCheque = mntCheque,
                    mntTraite = mntTraite,
                    mntReglement = mntReglement
                )

            }
        }
    }

    fun getReglementListByStation(station: String, regRemarque: String) {
        viewModelScope.launch {
            proCaisseLocalDb.reglementCaisse.getByStation(station = station).collectLatest { list->
             val listReglementByStation = list?: emptyList()

                val mntEspece = listReglementByStation.sumOf { it.rEGCMntEspece?: 0.0 }
                val mntCheque = listReglementByStation.sumOf { it.rEGCMntCheque?: 0.0 }
                val mntTraite = listReglementByStation.sumOf { it.rEGCMntTraite?: 0.0 }
                val mntReglement = listReglementByStation.filter { it.rEGCRemarque == regRemarque }.sumOf { it.rEGCMontant?: 0.0 }


                reglementPayments = reglementPayments.copy(
                    mntEspece = mntEspece,
                    mntCheque = mntCheque,
                    mntTraite = mntTraite,
                    mntReglement = mntReglement
                )

            }
        }
    }


    fun onMntCreditChange(idSCaisse: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getMntCredit(caisse = idSCaisse).collectLatest { mntCredit ->
                reglementPayments = reglementPayments.copy(mntCredit = mntCredit?: "0")
            }
        }
    }

    fun onMntCreditChangeByStation(station: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getMntCreditByStation(station = station).collectLatest { mntCredit ->
                reglementPayments = reglementPayments.copy(mntCredit = mntCredit?: "0")
            }
        }
    }

    // Debug method to check database content
    fun checkDatabaseContent() {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getAll().collectLatest { allTicketsMap ->

                if (allTicketsMap.isEmpty()) {
                } else {
                    allTicketsMap.entries.take(5).forEach { (ticketWithFacture, ligneTickets) ->
                    }
                    if (allTicketsMap.size > 5) {
                    }
                }
            }
        }
    }

}
