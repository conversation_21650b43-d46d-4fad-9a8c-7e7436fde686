package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.image.repository


import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import kotlinx.coroutines.flow.Flow


interface ImageVCLocalRepository {
    fun upsertAll(value: List<ImagePieceJoint>)
    fun deleteAll()
    fun deleteByCodeTypeVc(codeTypeVc: String)
    fun setDeleted(codeIMG: String)
    fun getAll(): Flow<List<ImagePieceJoint>>
    fun getNotSynced(): Flow<List<ImagePieceJoint>>
    fun getImageByCode(code : String): Flow<List<ImagePieceJoint>>
    fun setImagesSynced(code : String)

}