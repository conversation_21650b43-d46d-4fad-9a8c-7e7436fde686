package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.enum_classes.DevEtat
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertDoubleToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.EXERCICE_KEY
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.dao.BonCommandeDAOWrapper
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.filter.BonCommandeFilterListState
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class BonCommandeViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val bonCommandeDAOWrapper: BonCommandeDAOWrapper,
    private val dataStoreRepository: DataStoreRepository
) : ViewModel() {




    var showSearchView: Boolean by mutableStateOf(false)
        private set
    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }

    var showCustomFilter: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }

    var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }


    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }





    var selectedLigneBonCommande: LigneBonCommande by mutableStateOf(LigneBonCommande())
        private set
    fun onSelectedLigneBonCommandeChange(value: LigneBonCommande) {
        selectedLigneBonCommande = value
    }

    var selectedBonCommandeWithLignes by mutableStateOf(emptyMap<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>())
        private set
    var selectedListLgBonCommande = mutableStateListOf<LigneBonCommande>()
        private set
    var selectedBonCommandeWithClient: BonCommandeWithClient by mutableStateOf(BonCommandeWithClient())
        private set

    var bonCommandeNote: String by mutableStateOf("")
        private set

    var selectedListLgBonCommandeWithArticle = mutableStateListOf<LigneBonCommandeWithArticle>()
        private set
    fun onSelectedBonCommandeChange(value: Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>) {
        selectedBonCommandeWithLignes = value
        value.forEach { (key, value) ->
            run {
                selectedBonCommandeWithClient = key
                selectedListLgBonCommande.addAll(value.map { it.ligneBonCommande?: LigneBonCommande() })
                selectedListLgBonCommandeWithArticle.addAll(value)
            }
        }

    }




    fun restBonCommande() {
        selectedListLgBonCommandeWithArticle.clear()
        selectedBonCommandeWithLignes = emptyMap()
        selectedListLgBonCommande.clear()
        selectedBonCommandeWithClient = BonCommandeWithClient()
    }









    var bonCommandeListstate: BonCommandeFilterListState by mutableStateOf(BonCommandeFilterListState())
        private set






    /*  var bonTransfertPrefix: Prefixe by mutableStateOf(Prefixe())
         private set
       fun getBonCommandePrefix(utilisateur: Utilisateur) {
          viewModelScope.launch {
              proCaisseLocalDb.prefix.getOneById("bon_transfert").collect {
                  if(it!=null){
                      bonTransfertPrefix = it
  
                      if(it.pREPrefixe!=null) generateBonEntreeNum(it.pREPrefixe!!,utilisateur)
                  }
  
              }
          }
      }
  
      var bonTransfertGeneratedNum: String by mutableStateOf("")
          private set
    fun generateBonEntreeNum(prefix : String,utilisateur: Utilisateur) {
          val stationUtilisateur = utilisateur.Station
          val userID = utilisateur.Code_Ut
          viewModelScope.launch {
              proCaisseLocalDb.bonCommande.getNewCode(prefix).collect {
                  bonTransfertGeneratedNum =prefix + it
              }
          }
      }*/


    fun updateBonCommandeStatus(status: String, bonCommandeM: String){
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.bonCommande.updateStatus(status = status, bonCommandeM = bonCommandeM, devDdm = getCurrentDateTime())
        }
    }


    fun saveBonCommande(bonCommande: BonCommande) {
        viewModelScope.launch(dispatcher) {

            if (selectedBonCommandeWithClient.bonCommande?.devCodeM?.isNotEmpty() == true) {
                /**
                 * delete bc if modify
                 */
                proCaisseLocalDb.bonCommande.deleteByCodeM(codeM = bonCommande.devCodeM)
            }
            proCaisseLocalDb.bonCommande.upsert(bonCommande)
        }
    }

    fun saveListLigneBonCommande(lgbonCommande: List<LigneBonCommande>) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.ligneBonCommande.upsertAll(lgbonCommande)
        }
    }

    fun saveLigneBonCommande(lgbonCommande: LigneBonCommande) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.ligneBonCommande.upsert(lgbonCommande)
        }
    }

    fun deleteBcLgbyCodeMAndArtCode(codeM: String, artCode: String) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.ligneBonCommande.deletebyCodeMAndArtCode(codeM = codeM, artCode = artCode)
        }
    }

    fun deleteBonCommandeAndItsLines(
        listStationStockArticl: Map<String, StationStockArticle>,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {
        viewModelScope.launch(dispatcher) {
            val bonCommande = selectedBonCommandeWithClient.bonCommande ?: return@launch

            val codeM = bonCommande.devCodeM


            val listLgBc = selectedListLgBonCommandeWithArticle

            for (lgBc in listLgBc) {

                val article = lgBc.article?: return@launch

                val ligneBonCommande = lgBc.ligneBonCommande
                val qty = stringToDouble(ligneBonCommande?.lGDEVQte)
                ArticleOpeartions.updateArticleQty(
                    quantity = qty,
                    article = article,
                    updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                        updateArtQteStock(
                            newQteAllStations,
                            newQteStation,
                            codeArticle
                        )
                    }
                )
              //  val stationStockArticle = listStationStockArticl.firstOrNull { it.sARTCodeSatation == bonCommande.dEVStation && it.sARTCodeArt == ligneBonCommande?.lGDEVCodeArt }
                val stationStockArticle =  listStationStockArticl[ligneBonCommande?.lGDEVCodeArt + bonCommande.dEVStation]

                ArticleOpeartions.getStationStockArticle(
                    codeStation = bonCommande.dEVStation!!,
                    codeArticle = ligneBonCommande?.lGDEVCodeArt!!,
                    stationStockArticle = stationStockArticle,
                    qty = qty,
                    updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                        updateQtePerStation(
                            newQteStation,
                            newSartQteDeclare,
                            codeArticle,
                            codeStation
                        )
                    }
                )


            }


            proCaisseLocalDb.ligneBonCommande.deleteByCodeM(codeM = codeM)
            proCaisseLocalDb.bonCommande.deleteByCodeM(codeM = codeM)

        }
    }



 /*   fun deleteBonCommandeAndItsLines(
        codeM: String,
        listArticle: List<Article>,
        clientList: List<Client>,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {
        viewModelScope.launch(dispatcher) {
            val ticket = selectedBonLivraison.ticket ?: return@launch

            val codeM = ticket.tIKNumTicketM
            val exercice = ticket.tIKExerc
            val numTicket = ticket.tIKNumTicket
            val idCaisse = ticket.tIKIdSCaisse

            val listLgBl = selectedListLgBonLivraison

            for (lgBl in listLgBl) {
                val article =
                    listArticle.firstOrNull { it.aRTCode == lgBl.lTCodArt || it.aRTCode == lgBl.codeBarFils }
                        ?: return@launch


                val qty = stringToDouble(lgBl.lTQte)
                ArticleOpeartions.updateArticleQty(
                    quantity = qty,
                    article = article,
                    updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                        updateArtQteStock(
                            newQteAllStations,
                            newQteStation,
                            codeArticle
                        )
                    }
                )

                getStationStockArticle(
                    codeStation = ticket.tIKStation,
                    codeArticle = lgBl.lTCodArt,
                    qty = qty,
                    updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                        updateQtePerStation(
                            newQteStation,
                            newSartQteDeclare,
                            codeArticle,
                            codeStation
                        )
                    }
                )


            }

            val client = clientList.firstOrNull { it.cLICode == ticket.tIKCodClt }

            proCaisseLocalDb.ligneBonCommande.deleteByCodeM(codeM = codeM)
            proCaisseLocalDb.bonCommande.deleteByCodeM(codeM = codeM)



            //  delay(500)
        }
    }*/

    fun deleteLgBonCommande(codeM: String) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.ligneBonCommande.deleteByCodeM(codeM = codeM)
        }
    }


    fun onEvent(event: ListEvent, utilisateur: Utilisateur) {
        when (event) {
            is ListEvent.Order -> {
                if (bonCommandeListstate.listOrder::class == event.listOrder::class &&
                    bonCommandeListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                bonCommandeListstate = bonCommandeListstate.copy(
                    listOrder = event.listOrder
                )
                filterBonCommande(bonCommandeListstate, utilisateur = utilisateur)
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()

            is ListEvent.ListSearch -> {
                bonCommandeListstate = bonCommandeListstate.copy(
                    search = event.listSearch
                )

                filterBonCommande(bonCommandeListstate, utilisateur = utilisateur)
            }

            is ListEvent.FirstCustomFilter -> {
              //TODO IF WE ADD FILTERS
            }

            is ListEvent.SecondCustomFilter -> {
                //TODO IF WE ADD FILTERS
            }
            is ListEvent.ThirdCustomFilter -> TODO()

        }

    }
    var isLoadingFromLocalDb: Boolean by mutableStateOf(false)
        private set
    fun filterBonCommande(bonCommandeFilterListState: BonCommandeFilterListState, utilisateur: Utilisateur) {
        isLoadingFromLocalDb = true
        val searchedText = searchTextState.text
        val isAsc = if (bonCommandeFilterListState.listOrder.orderType is OrderType.Ascending) 1 else 2
        val sortBy = when (bonCommandeFilterListState.listOrder) {
            is ListOrder.Title -> "BON_LIV_Num"
            is ListOrder.Date -> "DDmM"
            is ListOrder.Third -> "DEV_MntTTC"
        }

        viewModelScope.launch {
            val resultFlow = when {
                searchedText.isEmpty() -> proCaisseLocalDb.bonCommande.getAllFiltred(
                    isAsc = isAsc,
                    sortBy = sortBy,
                    station = utilisateur.Station
                )
                bonCommandeFilterListState.search is ListSearch.FirstSearch -> proCaisseLocalDb.bonCommande.filterByBonCommandeNum(
                    searchString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    station = utilisateur.Station
                )
                bonCommandeFilterListState.search is ListSearch.SecondSearch -> proCaisseLocalDb.bonCommande.filterByNomClient(
                    searchString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    station = utilisateur.Station
                )
                bonCommandeFilterListState.search is ListSearch.ThirdSearch -> proCaisseLocalDb.bonCommande.filterByCodeClient(
                    searchString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    station = utilisateur.Station
                )
                else -> null
            }

            resultFlow?.collect { result ->
                setBonCommandetList(result)
            }
        }
    }

    private fun setBonCommandetList(bonCommande: Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>) {
        isLoadingFromLocalDb = false
        bonCommandeListstate = bonCommandeListstate.copy(lists = bonCommande)
    }





    fun saveNewBonCommande(
        bonCommandeCodeM: String,
        proCaisseAuthorization: List<Authorization>,
        selectedClient: Client,
        utilisateur: Utilisateur,
        totPriceWithoutDicount: Double,
        selectedArticleList: List<SelectedArticle>,
        totalDiscount: String,
        totalPriceAfterDiscountChange: String,
        noteText: String = "",
        enableNotes: Boolean = false
    ) {
        if (selectedBonCommandeWithClient.bonCommande?.devCodeM?.isNotEmpty() == true) {
            /**
             * delete all line if modify bc (can also make  a list of deleted lines to delete only that list instead of deleting all lines)
             * */
            deleteLgBonCommande(codeM = bonCommandeCodeM)
        }



        val haveBLAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.BL }
        val haveBlToBcAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.BC_TO_BL }

        // Get exercice from DataStore (like old app pattern)
        val exerciceCode = runBlocking {
            dataStoreRepository.getString(EXERCICE_KEY, default = "2024").first() ?: "2024"
        }

        //  val mntTTC = Calculations.totalPriceTTC(listArt = selectArtMobilityVM.selectedArticleMobilityList)
        val bonCommande = BonCommande(
            devCodeM = bonCommandeCodeM,
            dEVNum = bonCommandeCodeM,
            dEVExerc = exerciceCode,
            dEVDate = getCurrentDateTime(),
            dEVCodeClient = selectedClient.cLICode,
            dEVStationOrigine = utilisateur.Station,
            dEVEtat = DevEtat.BCC_CLIENT.etat,
            dEVStation = utilisateur.Station,
            dEVUser = utilisateur.codeUt,
            // Fix: Calculate total HT amount (unit HT price × quantity) for each line
            dEVMntht = selectedArticleList.sumOf {
                stringToDouble(it.lTPuHT) * stringToDouble(it.quantity)
            }.toString(),
            dEVMntNetHt = selectedArticleList.sumOf { stringToDouble(it.lTMtNetHT) }.toString(),
            dEVMntTva = selectedArticleList.sumOf { stringToDouble(it.mntTva) }.toString(),
            dEVMntTTC = totPriceWithoutDicount.toString(),
            dEVTauxRemise = totalDiscount,
            dEVRemise = (totPriceWithoutDicount - stringToDouble(totalPriceAfterDiscountChange)).toString(),
            dEVRegler = "",
            dEVExport = "",
            dEVDDm = "empty",//getCurrentDateTime(), //TODO IF update getCurrentDateTime() else empty
            dEVExoNum = "",
            dEVExoVal = "",
            dEVTimbre = "0.0",//TODO VERIFY IF SET THIS FIELD
            dEVExonoration = "0",
            dEVChauffeur = "",
            dEVVehicule = "",
            dEVObservation = if (enableNotes && noteText.isNotEmpty()) noteText else "",
            dEVClient = selectedClient.cLINomPren,
            dEVClientName = selectedClient.cLINomPren,
            dEVMntFodec = "0.0",
            dEVMntDC = "0.0",
            dEVEtatBon = "1",
            bONLIVNum = "",
            bONLIVExerc = exerciceCode,
            code = "",
            msg = "",
            dEV_info3 = ""
        )

        bonCommande.status =
            if (haveBLAuthorisation && haveBlToBcAuthorisation) ItemStatus.WAITING.status else ItemStatus.INSERTED.status
        bonCommande.isSync = false



        saveBonCommande(bonCommande = bonCommande)

        // Note handling logic based on enableNotes setting and noteText content

        if (enableNotes && noteText.isNotEmpty()) {
        } else if (!enableNotes) {
        } else {
        }

        saveListLgBonCommande(
            bonCommandeCodeM = bonCommandeCodeM,
            exerciceCode = exerciceCode,
            selectedClient = selectedClient,
            utilisateur = utilisateur,
            haveBLAuthorisation = haveBLAuthorisation,
            haveBlToBcAuthorisation = haveBlToBcAuthorisation,
            selectedArticleList = selectedArticleList
        )
    }


    fun saveListLgBonCommande(
        bonCommandeCodeM: String,
        exerciceCode: String,
        selectedClient: Client,
        haveBLAuthorisation: Boolean,
        utilisateur: Utilisateur,
        haveBlToBcAuthorisation: Boolean,
        selectedArticleList: List<SelectedArticle>
    ) {

        for (i in selectedArticleList.indices) {
            val articl = selectedArticleList[i].article

            // Improved TVA calculation with better fallback logic
            val lTTVA = if(articl.aRTTVA == 0.0) {
                val mtTTC = stringToDouble(selectedArticleList[i].lTMtTTC)
                val mtNetHT = stringToDouble(selectedArticleList[i].lTMtNetHT)

                // Only calculate TVA from amounts if there's a meaningful difference
                if (mtNetHT > 0.0 && mtTTC > mtNetHT && (mtTTC - mtNetHT) / mtNetHT > 0.01) {
                    ((mtTTC - mtNetHT) / mtNetHT) * 100
                } else {
                    0.0 // Explicitly 0% TVA for tax-exempt items
                }
            } else {
                articl.aRTTVA
            }


            val ligneBonCommande = LigneBonCommande(
                lGDEVNumBon = bonCommandeCodeM,
                lGDEVCodeM = bonCommandeCodeM,
                lGDEVExerc = exerciceCode,
                lGDEVCodeArt = articl.aRTCode,
                lGDEVQte = selectedArticleList[i].quantity,
                lGDEVUnite = articl.uNITEARTICLECodeUnite,
                lGDEVPUHT = selectedArticleList[i].lTPuHT,
                lGDEVTva = lTTVA.toString(),
                lGDEVNetht = selectedArticleList[i].lTMtNetHT,
                lGDEVRemise = selectedArticleList[i].discount,
                lGDEVStation = selectedClient.cLIStation,
                lGDEVUser = utilisateur.codeUt,
                lGDEVNumOrdre = i + 1,
                lGDEVTarif = "",
                lGDEVQtePiece = "",
                lGDEVExport = "",
                lGDEVDDm = getCurrentDateTime(), //TODO IF update getCurrentDateTime() else empty
                lGDEVMntTTC = selectedArticleList[i].lTMtTTC,
                lGDEVMntHT = selectedArticleList[i].lTPuHT,
                // lGDEVPUTTC = selectedArticleList[i].lTPuTTC,
                lGDEVPUTTC = selectedArticleList[i].prixVente,
                lGDEVTauxFodec = "",
                lGDEVTauxDc = "",
                lGDEVMntBrutHT = selectedArticleList[i].lTMtBrutHT, // todo ask diffrence between lGDEVMntBrutHT and lGDEVMntHT
                lGDEVMntFodec = "",
                lGDEVMntDc = "",
                lGDEVMntTva = selectedArticleList[i].mntTva.toString(),
                lGDEVQteGratuite = "",
                lGDevNumSerie = "",
                codeLigne = "",
                msgLigne = "",
                selectedPriceCategory = selectedArticleList[i].selectedPriceCategory // used only in local
            )
            val qty = stringToDouble(selectedArticleList[i].quantity)

            ligneBonCommande.isSync = false
            ligneBonCommande.status = if (haveBLAuthorisation && haveBlToBcAuthorisation) ItemStatus.WAITING.status else ItemStatus.INSERTED.status

            saveLigneBonCommande(ligneBonCommande)

            // Stock is NOT deducted when creating BC
            // Stock will be deducted only when BC is converted to BL
            // This ensures BC creation doesn't affect inventory until actual delivery

        }
    }


    fun setTableList(
        ligneBCWithArticle: LigneBonCommandeWithArticle,
        getPrice:(art: Article)-> String,
        hasPromo : Boolean,
        updateSelectedArticleMobilityList: (SelectedArticle) -> Unit,

    ) {
        val lgBonCommande = ligneBCWithArticle.ligneBonCommande

        val prixCaiss = (stringToDouble(lgBonCommande?.lGDEVMntTTC) / stringToDouble(lgBonCommande?.lGDEVQte?:"1")).toString()

        val articl = ligneBCWithArticle.article?:
        Article(
            aRTCodeBar = lgBonCommande?.lGDEVCodeArt?: "N/A",
            aRTCode = lgBonCommande?.lGDEVCodeArt?: "N/A",
            pvttc = stringToDouble(prixCaiss)
        )

        val prixVent = getPrice(articl)

        val mntDiscount = stringToDouble(prixVent) - stringToDouble(lgBonCommande?.lGDEVPUTTC)


        val selectedArticle = SelectedArticle(
            article = articl,
            selectedPriceCategory = lgBonCommande?.selectedPriceCategory?.ifEmpty { Globals.PRIX_PUBLIQUE }?: Globals.PRIX_PUBLIQUE,
            quantity = lgBonCommande?.lGDEVQte?:"1",
            quantityError = null,
            prixCaisse = prixCaiss,//Prix Apré remise
            prixVente = prixVent,//Prix Avant remise
            discount = lgBonCommande?.lGDEVRemise?:"0", //taux remise
            //mntDiscount = StringUtils.stringToDouble(prixVent) - StringUtils.stringToDouble(prixCaiss),//mnt remise
            mntDiscount = mntDiscount.toString(),//mnt remise
            discountError = null,
            lTMtTTC = lgBonCommande?.lGDEVMntTTC?: "N/A",
            mtTTCError = null,
            lTMtBrutHT = lgBonCommande?.lGDEVMntBrutHT?: "N/A",
            lTPuHT = lgBonCommande?.lGDEVPUHT?: "N/A",
            lTMtNetHT = lgBonCommande?.lGDEVNetht?: "N/A",
            lTPuTTC = lgBonCommande?.lGDEVPUTTC?: "N/A"
        )
        updateSelectedArticleMobilityList(selectedArticle)
    }

    fun setSelectedArticleList(
        lgBCWithArticleList: List<LigneBonCommandeWithArticle>,
        getPrice: (Article) -> String,
        addOneItemToSelectedArticleMobilityList: (SelectedArticle) -> Unit
    ) {

        for (lgBCWithArticle in lgBCWithArticleList) {
            val ligneBonCommande = lgBCWithArticle.ligneBonCommande?: LigneBonCommande()
            val artic = lgBCWithArticle.article?:Article(aRTCodeBar = ligneBonCommande.lGDEVCodeArt, aRTCode = ligneBonCommande.lGDEVCodeArt)

                val prixVente = getPrice(artic)

                val remise = convertStringToDoubleFormat(ligneBonCommande.lGDEVRemise ?: "0")
                val prixCaisse =  stringToDouble(ligneBonCommande.lGDEVMntTTC) / stringToDouble(ligneBonCommande.lGDEVQte?: "1")
                val mntDiscount = stringToDouble(prixVente) - prixCaisse
                val seletedArt =
                    SelectedArticle(
                        article = artic,
                        selectedPriceCategory = ligneBonCommande.selectedPriceCategory.ifEmpty { Globals.PRIX_PUBLIQUE },
                        quantity = ligneBonCommande.lGDEVQte?: "1",
                        quantityError = null,
                        prixCaisse = convertDoubleToDoubleFormat(prixCaisse), // Prix Apré remise
                        prixVente = prixVente, // Prix Avant remise
                        discount = remise, // taux remise
                        mntDiscount = mntDiscount.toString(), // mnt remise
                        discountError = null,
                        lTMtTTC = convertStringToDoubleFormat(ligneBonCommande.lGDEVMntTTC ?: prixVente),
                        mtTTCError = null,
                        lTMtBrutHT = ligneBonCommande.lGDEVMntHT!!, // stringToDouble(lgBonCommande.lGDEVMntHT)
                    )

                addOneItemToSelectedArticleMobilityList(seletedArt)

        }
    }

    /**
     * Retrieve note for a BonCommande from the DEV_Observation field
     */
    fun loadNoteForBonCommande(bonCommandeCodeM: String, exercice: String) {
        viewModelScope.launch(dispatcher) {
            try {

                // Get note from the currently selected BonCommande
                val note = selectedBonCommandeWithClient.bonCommande?.dEVObservation ?: ""
                bonCommandeNote = note


            } catch (e: Exception) {
                bonCommandeNote = ""
            }
        }
    }

    }
