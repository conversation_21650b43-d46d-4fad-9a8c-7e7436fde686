package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.consultation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.SelectPatrimoineViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.navigation.AjoutInventaireBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.InventaireBatimentDetailRoute
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringPlural
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.InventaireBatimentViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.InventaireViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItemWithBadge

import com.asmtunis.procaisseinventory.shared_ui_components.DeplacementStatusBadge

@Composable
fun InvBatimentList(
    articleMap: Map<String, Article>,
    imageList: List<ImagePieceJoint>,
    invPatrimoineViewModel: InventaireViewModel,
    selectPatrimoineVM: SelectPatrimoineViewModel,
    batimentViewModel: InventaireBatimentViewModel,
    navigate: (route: Any) -> Unit,
    selectedBaseconfig: BaseConfig,
    searchTextState: TextFieldValue,
    setCodM: (String)-> Unit,
    immobilisationList: List<Immobilisation>,
    getImmobilisation: (BaseConfig) -> Unit,
    typePat: String = "",
    marqueList: List<Marque>,
    invPatrimoineList: Map<BonCommande, List<LigneBonCommande>>,
    invPatList: Map<BonCommande, List<LigneBonCommande>>,
    getClientName: (cltName: String?, cltCode: String?) -> String,
) {

    val listState = rememberLazyListState()

    if (invPatList.isNotEmpty()) {
        val listBonCommande: MutableList<BonCommande> = arrayListOf()
        val ligneBonCommande: MutableList<LigneBonCommande> = arrayListOf()

        invPatList.forEach { (key, value) ->
            run {
                listBonCommande.add(key)
                ligneBonCommande.addAll(value)
            }
        }



        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(
                items = listBonCommande,
                key = { bonCommand ->
                    bonCommand.dEVNum
                }
            ) { bonCommand ->
                // Empty firstTextRemarque since status is now shown on the left
                val firstTextRemarque = ""

                // Status badge positioned on the left side with colored text
                val statusBadge = @Composable {
                    if (typePat == TypePat.DEP_OUT.typePat || typePat == TypePat.DEP_IN.typePat) {
                        val (statusText, statusColor) = when (bonCommand.dEVEtatBon) {
                            "1", "En Instance" -> Pair(stringResource(id = R.string.en_instance), Color.Red)
                            "2", "Validée" -> Pair(stringResource(id = R.string.validee), Color.Green)
                            else -> Pair("", Color.Transparent)
                        }

                        if (statusText.isNotEmpty()) {
                            Text(
                                text = statusText,
                                color = statusColor,
                                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }

                ListItemWithBadge(
                    onItemClick = {
                        goToInventaireBatimentScreen(
                        navigate = { navigate(it) },
                        setCodM = { setCodM(it) },
                        marqueList = marqueList,
                        articleMap = articleMap,
                        ligneBonCommande = ligneBonCommande,
                        immobilisationList = immobilisationList,
                        bonCommand = bonCommand,
                        invPatList = invPatList,
                        imageList = imageList,
                        selectPatrimoineVM = selectPatrimoineVM,
                        batimentViewModel = batimentViewModel,
                        invPatrimoineViewModel = invPatrimoineViewModel,
                        )

                    },
                    firstText = bonCommand.dEVNum,
                    firstTextRemarque = firstTextRemarque, // Show status inline with document number
                    secondText = getClientName(bonCommand.dEVClientName, bonCommand.dEVCodeClient),
                    thirdText = stringPlural(
                        nbr = invPatList[bonCommand]?.size ?: 0,
                        single = stringResource(id = R.string.article_title),
                        plural = stringResource(id = R.string.article_field_title)
                    ),
                    dateText = bonCommand.dEVDDmFormatted?: "N/A",
                    isSync = bonCommand.isSync,
                    status = bonCommand.status,
                    statusBadge = statusBadge, // Ajouter le badge
                    onResetDeletedClick = {
                        // TODO
                    },
                    moreClickIsVisible = !bonCommand.isSync,
                    onMoreClick = {
                        invPatrimoineViewModel.restInvPatrimoine()
                        invPatrimoineViewModel.onSelectedInvPatrimoineChange(invPatList.filter { it.key == bonCommand })
                        invPatrimoineViewModel.onShowCustomModalBottomSheetChange(true)

                    },
                )
            }
        }


    } else {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            LottieAnim(lotti = R.raw.emptystate, size = 250.dp)

            if (searchTextState.text.isBlank() && invPatrimoineList.isEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = "Aucune donnée disponible",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

    }
}


fun goToInventaireBatimentScreen(
    navigate: (route: Any) -> Unit,
    setCodM: (String)-> Unit,
    marqueList: List<Marque>,
    articleMap: Map<String, Article>,
    ligneBonCommande: List<LigneBonCommande>,
    immobilisationList: List<Immobilisation>,
    bonCommand: BonCommande,
    invPatList: Map<BonCommande, List<LigneBonCommande>>,
    imageList: List<ImagePieceJoint>,
    selectPatrimoineVM: SelectPatrimoineViewModel,
    batimentViewModel: InventaireBatimentViewModel,
    invPatrimoineViewModel: InventaireViewModel,
) {
    invPatrimoineViewModel.restInvPatrimoine()
    val bonCommandeWithLignes = invPatList.filter { it.key == bonCommand }
    invPatrimoineViewModel.onSelectedInvPatrimoineChange(bonCommandeWithLignes)

    if (bonCommand.status == ItemStatus.WAITING.status) {
        setCodM(bonCommand.devCodeM)
        val immobilsation =
            immobilisationList.firstOrNull { it.cLICode == bonCommand.dEVCodeClient }
                ?: Immobilisation(
                    cLICode = bonCommand.dEVCodeClient ?: "N/A",
                    cLINomPren = bonCommand.dEVClient ?: "N/A"
                )
        batimentViewModel.onSelectedZoneConsomationChange(immobilsation, "5")
        selectPatrimoineVM.resetSelectedPatrimoineArticles()

        val listLigneBonCommande = bonCommandeWithLignes.values.flatten()

        for (i in listLigneBonCommande.indices) {
            val article = articleMap[ligneBonCommande[i].lGDEVCodeArt]?: Article(aRTCodeBar = ligneBonCommande[i].lGDEVCodeArt)
            val imgList = imageList.filter { it.vcNumSerie ==  ligneBonCommande[i].lGDevNumSerie }

            selectPatrimoineVM.setConsultationSelectedPatrimoineList(
                article = article,
                numSerie = listLigneBonCommande[i].lGDevNumSerie?: "N/A",
                quantity = StringUtils.stringToDouble(listLigneBonCommande[i].lGDEVQte),
                imageList = imgList,
                note = listLigneBonCommande[i].lgDEVNote?: "",
                marque = marqueList.firstOrNull { it.mARCode == listLigneBonCommande[i].lGDEVCMarq }?: Marque(mARDesignation = listLigneBonCommande[i].lGDEVCMarq ?: "N/A")
            )
        }


        navigate(AjoutInventaireBatimentRoute)
    } else navigate(InventaireBatimentDetailRoute)
}