package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.consultation

import NavDrawer
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Check
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.currentMonth
import com.asmtunis.procaisseinventory.core.Globals.currentYear
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils.getClientName
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants.IMMOBILISATION
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.simapps.ui_kit.CustomScrollableTabRow
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.launch

@OptIn(FlowPreview::class)
@Composable
fun InventaireImmobilisationScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel: MainViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    settingViewModel: SettingViewModel,
    proCaisseViewModels: ProCaisseViewModels
) {
    val batimentViewModel = proCaisseViewModels.batimentViewModel
    val invPatViewModel = proCaisseViewModels.invPatViewModel
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM

    val articleMap = mainViewModel.articleMapByBarCode
    val imageList = mainViewModel.imageList

    val syncInvPatrimoineViewModel = syncProcaisseViewModels.syncInvPatrimoineViewModel

    val uiWindowState = settingViewModel.uiWindowState

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)

    val scope = rememberCoroutineScope()

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val marqueList = mainViewModel.marqueList

    val isConnected = networkViewModel.isConnected

    val sessionCaisse = navDrawerViewModel.sessionCaisse
    val clientList = mainViewModel.clientList

    val utilisateur = mainViewModel.utilisateur
    val context = LocalContext.current

    val invPatrimoineListstate = invPatViewModel.invPatrimoineListstate
    val listOrder = invPatrimoineListstate.listOrder
    val listFilter = invPatrimoineListstate.search
    val filterList = context.resources.getStringArray(R.array.inventaire_batiment_filter)

    val invPatrimoineList = invPatrimoineListstate.lists
    val selectedInvPatrimoine = invPatViewModel.selectedInvPatrimoine
    val searchTextState = invPatViewModel.searchTextState


    val filteredDepOutList = invPatViewModel.filteredDepOutList
    val filteredDepInList = invPatViewModel.filteredDepInList
    val filteredInventaireList = invPatViewModel.filteredInventaireList
    val filteredAffectationList = invPatViewModel.filteredAffectationList


    //todo see if room query for below affect app performance
    val depOutListNotFiltred = invPatViewModel.depOutListNotFiltred
    val depInListNotFiltred = invPatViewModel.depInListNotFiltred
    val inventaireListNotFiltred = invPatViewModel.inventaireListNotFiltred
    val affectationListNotFiltred = invPatViewModel.affectationListNotFiltred


    val isRefreshing = getProCaisseDataViewModel.immobilisationState.loading

    val isLoadingFromDB = invPatViewModel.isLoadingFromDB

    val selectedZoneConsomation = batimentViewModel.selectedZoneConsomation

    // Disabled month/year filter variables - keeping for reference but not used in UI
    val selectedMonth = invPatViewModel.selectedMonth // This will be "Tous" by default
    val selectedYear = invPatViewModel.selectedYear

//    LaunchedEffect(key1 = selectedZoneConsomation) {
//        if (selectedZoneConsomation.cLINomPren.isEmpty()) return@LaunchedEffect
//
//       invPatViewModel.onSearchValueChange(TextFieldValue(selectedZoneConsomation.cLINomPren))
//        invPatViewModel.onEvent(
//            event = ListEvent.ListSearch(ListSearch.SecondSearch()),
//            utilisateur = utilisateur,
//            devEtat = IMMOBILISATION,
//        )
//    }

    LaunchedEffect(
        key1 = searchTextState.text,
        key2 = invPatrimoineListstate.search,
        key3 = selectedMonth + selectedYear + invPatViewModel.selectedFilterType // Re-enable date filter triggering
    ) {
        invPatViewModel.filterInvPatrimoine(
            from = "1",
            invePatrimoineFilterListState = invPatrimoineListstate,
            utilisateur = utilisateur,
            devEtat = IMMOBILISATION
        )
    }

   /* LaunchedEffect(key1 = Unit) {
         invPatViewModel.getInvPatList(station = utilisateur.Station, devEtat = IMMOBILISATION)
    }*/


    val tabRowItems = getTabRowImmobilisationItems(
        navigate = { navigate(it) },
        articleMap = articleMap,
        imageList = imageList,
        invPatrimoineList = invPatrimoineList,
        depOutListNotFiltred = depOutListNotFiltred,
        depInListNotFiltred = depInListNotFiltred,
        inventaireListNotFiltred = inventaireListNotFiltred,
        affectationListNotFiltred = affectationListNotFiltred,
        filteredDepOutList = filteredDepOutList,
        filteredDepInList = filteredDepInList,
        filteredInventaireList = filteredInventaireList,
        filteredAffectationList = filteredAffectationList,
        batimentViewModel = batimentViewModel,
        immobilisationList = mainViewModel.immobilisationList,
        marqueList = marqueList,
        selectPatrimoineVM = selectPatrimoineVM,
        selectedBaseconfig = selectedBaseconfig,

        setCodM = { mainViewModel.setCodM(it) },
        getImmobilisation = {
            // Offline-first: No API calls, data comes from local database only
            // All data filtering is handled by the ViewModel using local Room database
        },
        invPatViewModel = invPatViewModel,
        searchTextState = searchTextState,
        getClientName = { cltName, cltCode -> getClientName(cltName = cltName, cltCode = cltCode) },
    )

    val pagerState = rememberPagerState(
        initialPage = 0,
        initialPageOffsetFraction = 0f,
    ) {
        // provide pageCount
        tabRowItems.size
    }




    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        mainViewModel = mainViewModel,
        navDrawerViewmodel = navDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = {
                        // Navigate to shortcuts screen instead of opening drawer
                        navigate(com.asmtunis.procaisseinventory.core.navigation.HomePageRoute)
                    },
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    title = buildString {
                        append(stringResource(id = navDrawerViewModel.proCaisseSelectedMenu.title))
                        when (invPatViewModel.selectedFilterType) {
                            "jours" -> append(" - Aujourd'hui")
                            "mois" -> append(" - ${getMonthName(selectedMonth)} $selectedYear")
                            "années" -> append(" - $selectedYear")
                            "tous" -> append(" - Toutes les données")
                        }
                    },
                    titleVisibilty = !invPatViewModel.showSearchView && searchTextState.text.isEmpty(),
                    actions = {
                        SearchSectionComposable(
                            label = context.getString(
                                R.string.filter_by,
                                when (listFilter) {
                                    is ListSearch.FirstSearch -> filterList[0]
                                    is ListSearch.SecondSearch -> filterList[1]
                                    is ListSearch.ThirdSearch -> filterList[2]
                                },
                            ),
                            searchVisibility = invPatViewModel.showSearchView || searchTextState.text.isNotEmpty(),
                            searchTextState = searchTextState,
                            onSearchValueChange = {
                                invPatViewModel.onSearchValueChange(TextFieldValue(it))
                                if (it == "") {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    batimentViewModel.onSelectedZoneConsomationChange(
                                        Immobilisation(),
                                        "4"
                                    )
                                }
                            },
                            onShowSearchViewChange = {
                                invPatViewModel.onShowSearchViewChange(it)
                                /*   if (!it) {
                                       /** this bloc to handle search visibility when custom search by client*/

                                       invPatViewModel.onSearchValueChange(TextFieldValue(""))
                                      // mainViewModel.onSelectedClientChange(Client())
                                   }*/
                            },
                            onShowCustomFilterChange = {
                                invPatViewModel.onShowCustomFilterChange(it)
                            },
                        )
                    },
                )
            },
        ) { padding ->
            if (invPatViewModel.showCustomModalBottomSheet) {
                CustomModalBottomSheet(
                    remoteResponseState = when (invPatViewModel.tabsState) {
                        TypePat.AFFECTATION.typePat -> syncInvPatrimoineViewModel.responseAddAffectationBatimentState
                        TypePat.INVENTAIRE.typePat -> syncInvPatrimoineViewModel.responseAddInventaireBatimentState
                        TypePat.DEP_IN.typePat -> syncInvPatrimoineViewModel.responseAddDepInBatimentState
                        TypePat.DEP_OUT.typePat -> syncInvPatrimoineViewModel.responseAddDepOutBatimentState
                        else -> {syncInvPatrimoineViewModel.responseAddDepOutBatimentState}
                    },
                    status = selectedInvPatrimoine.status,
                    title = selectedInvPatrimoine.dEVNum,
                    showPrintIcon = false,
                    onDismissRequest = {
                        invPatViewModel.onShowCustomModalBottomSheetChange(false)
                    },
                    onDeleteRequest = {
                        invPatViewModel.deleteInventaire()
                    },
                    onValidate = {
                        invPatViewModel.setInventaireToInserted()
                    },
                    onSyncRequest = {
                        when (invPatViewModel.tabsState) {
                            TypePat.AFFECTATION.typePat -> syncInvPatrimoineViewModel.syncAffectationBatiment(selectedInvPatrimoine)
                            TypePat.INVENTAIRE.typePat -> syncInvPatrimoineViewModel.syncInventaireBatiment(selectedInvPatrimoine)
                            TypePat.DEP_IN.typePat -> syncInvPatrimoineViewModel.syncDepInBatiment(selectedInvPatrimoine)
                            TypePat.DEP_OUT.typePat -> syncInvPatrimoineViewModel.syncDepOutBatiment(selectedInvPatrimoine)
                            else -> {syncInvPatrimoineViewModel.syncDepOutBatiment(selectedInvPatrimoine)}
                        }
                    }
                )
            }

            if (invPatViewModel.showCustomFilter) {
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.inventaire_order),
                    onShowCustomFilterChange = {
                        invPatViewModel.onShowCustomFilterChange(false)
                    },
                    onEvent = {
                        invPatViewModel.onEvent(
                            event = it,
                            utilisateur = utilisateur,
                            devEtat = IMMOBILISATION,
                        )
                    },
                    customFilterContent = {
                        // Enhanced DateFilterPicker with new options
                        DateFilterPicker(
                            selectedFilterType = invPatViewModel.selectedFilterType,
                            selectedMonth = selectedMonth,
                            selectedYear = selectedYear,
                            onFilterTypeChange = { invPatViewModel.onFilterTypeChange(it) },
                            onMonthChange = { invPatViewModel.onMonthChange(it) },
                            onYearChange = { invPatViewModel.onYearChange(it) }
                        )
                    }
                )
            }

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
                    .padding(top = 12.dp),
            ) {



                CustomScrollableTabRow(
                    pagerState = pagerState,
                    tabs = { sizeList, noInteraction ->
                        tabRowItems.forEachIndexed { index, item ->
                            Tab(
                                modifier = Modifier.onSizeChanged {
                                    sizeList[index] = Pair(it.width.toFloat(), it.height.toFloat())
                                },
                                selected = pagerState.currentPage == index,
                                onClick = {
                                    invPatViewModel.setTabState(tabs = item.id, typeInv = "")

                                    scope.launch {
                                        pagerState.animateScrollToPage(index)
                                    }
                                    when (index) {
                                        0 -> {
                                            invPatViewModel.onEvent(
                                                event = ListEvent.FirstCustomFilter(TypePatrimoine.AFFECTATION.typePat),
                                                utilisateur = utilisateur,
                                                devEtat = IMMOBILISATION,
                                            )
                                        }

                                        1 -> {
                                            invPatViewModel.onEvent(
                                                event = ListEvent.FirstCustomFilter(TypePatrimoine.INVENTAIRE.typePat),
                                                utilisateur = utilisateur,
                                                devEtat = IMMOBILISATION,
                                            )
                                        }

                                        2 -> {
                                            invPatViewModel.onEvent(
                                                event = ListEvent.FirstCustomFilter(TypePatrimoine.ENTREE.typePat),
                                                utilisateur = utilisateur,
                                                devEtat = IMMOBILISATION,
                                            )
                                        }

                                        3 -> {
                                            invPatViewModel.onEvent(
                                                event = ListEvent.FirstCustomFilter(TypePatrimoine.SORTIE.typePat),
                                                utilisateur = utilisateur,
                                                devEtat = IMMOBILISATION,
                                            )
                                        }

                                        else -> { "" }
                                    }
                                    //   invPatViewModel.onSearchValueChange(TextFieldValue(""))
                                },
                                interactionSource = remember { noInteraction },
                                /* icon = {
                                     Icon(imageVector = item.icon, contentDescription = "")
                                 },*/
                                text = {
                                    Text(
                                        text = item.title,
                                        style =
                                        TextStyle(
                                            fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                                            fontSize = MaterialTheme.typography.titleMedium.fontSize,
                                        ),
                                        modifier = Modifier.align(Alignment.CenterHorizontally),
                                        // .padding(horizontal = 32.dp, vertical = 16.dp)
                                    )
                                },
                            )
                        }
                    },
                    pagerContent = {
                        HorizontalPager(
                            modifier = Modifier.fillMaxSize(),
                            state = pagerState,
                            beyondViewportPageCount = 10,
                        ) { page ->
                            if (
                                getProCaisseDataViewModel.bonCommandeState.loading ||
                                getProCaisseDataViewModel.ligneBonCommandeState.loading ||
                                getProCaisseDataViewModel.immobilisationState.loading ||
                                getProCaisseDataViewModel.deplacementOutByUserResponseState.loading ||
                                getProCaisseDataViewModel.batimentByUserState.loading ||
                                (isLoadingFromDB && invPatrimoineListstate.lists.isEmpty())
                            ) {
                                Column(
                                    modifier = Modifier.fillMaxSize(),
                                    verticalArrangement = Arrangement.Center,
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    LottieAnim(lotti = R.raw.loading, size = 250.dp)
                                }
                            } else {
                                if(isLoadingFromDB) {
                                    Column(
                                        modifier = Modifier.fillMaxSize(),
                                        verticalArrangement = Arrangement.Center,
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        LottieAnim(lotti = R.raw.loading, size = 50.dp)
                                        Spacer(modifier = Modifier.height(12.dp))
                                        tabRowItems[page].screen()
                                    }
                                }
                                else tabRowItems[page].screen()

                            }
                        }
                    },
                )
            }
        }
    }
}

val months = listOf(
    "Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
    "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre", "Tous"
)

@Composable
fun DateFilterPicker(
    selectedFilterType: String,
    selectedMonth: String,
    selectedYear: String,
    onFilterTypeChange: (String) -> Unit,
    onMonthChange: (String) -> Unit,
    onYearChange: (String) -> Unit
) {
    val currentYear = 2025 // Replace this with your logic to fetch the current year
    val years = (2022..currentYear).map { it.toString() } // Range of years
    val filterTypes = listOf("jours", "mois", "années", "tous")
    val filterTypeLabels = mapOf(
        "jours" to "Aujourd'hui",
        "mois" to "Mois",
        "années" to "Années",
        "tous" to "Tous"
    )

    var expandedFilterType by remember { mutableStateOf(false) }
    var expandedMonth by remember { mutableStateOf(false) }
    var expandedYear by remember { mutableStateOf(false) }

    Column(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = stringResource(R.string.filter_by_date),
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.outline,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(9.dp))

        // Filter Type Selection
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Box {
                OutlinedButton(
                    onClick = { expandedFilterType = true },
                    modifier = Modifier.padding(end = 16.dp),
                    content = { Text(text = filterTypeLabels[selectedFilterType] ?: "Mois") }
                )
                DropdownMenu(
                    expanded = expandedFilterType,
                    onDismissRequest = { expandedFilterType = false }
                ) {
                    filterTypes.forEach { filterType ->
                        val isSelected = selectedFilterType == filterType
                        DropdownMenuItem(
                            onClick = {
                                onFilterTypeChange(filterType)
                                expandedFilterType = false
                            },
                            text = {
                                Text(
                                    text = filterTypeLabels[filterType] ?: filterType,
                                    color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface,
                                    style = if (isSelected) MaterialTheme.typography.bodyLarge else LocalTextStyle.current
                                )
                            },
                            trailingIcon = {
                                if (isSelected) Icon(
                                    imageVector = Icons.TwoTone.Check,
                                    contentDescription = stringResource(id = R.string.cd_toggle_drawer)
                                )
                            }
                        )
                    }
                }
            }
        }

        // Show month/year selectors based on filter type (hide for "jours" and "tous")
        if (selectedFilterType != "tous" && selectedFilterType != "jours") {
            Spacer(modifier = Modifier.height(8.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                // Month Dropdown (only for mois)
                if (selectedFilterType == "mois") {
                    Box {
                        OutlinedButton(
                            onClick = { expandedMonth = true },
                            modifier = Modifier.padding(end = 16.dp),
                            content = { Text(text = getMonthName(monthNumber = selectedMonth)) }
                        )
                        DropdownMenu(
                            expanded = expandedMonth,
                            onDismissRequest = { expandedMonth = false }
                        ) {
                            months.dropLast(1).forEachIndexed { index, month -> // Remove "Tous" option
                                val isSelected = selectedMonth == (index + 1).toString().padStart(2, '0')
                                DropdownMenuItem(
                                    onClick = {
                                        onMonthChange((index + 1).toString().padStart(2, '0'))
                                        expandedMonth = false
                                    },
                                    text = {
                                        Text(
                                            text = month,
                                            color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface,
                                            style = if (isSelected) MaterialTheme.typography.bodyLarge else LocalTextStyle.current
                                        )
                                    },
                                    trailingIcon = {
                                        if (isSelected) Icon(
                                            imageVector = Icons.TwoTone.Check,
                                            contentDescription = stringResource(id = R.string.cd_toggle_drawer)
                                        )
                                    }
                                )
                            }
                        }
                    }
                }

                // Year Dropdown (for mois and années)
                if (selectedFilterType == "mois" || selectedFilterType == "années") {
                    Box {
                        OutlinedButton(
                            onClick = { expandedYear = true },
                            modifier = Modifier.padding(start = if (selectedFilterType == "années") 0.dp else 16.dp),
                            content = { Text(text = selectedYear) }
                        )
                    DropdownMenu(
                        expanded = expandedYear,
                        onDismissRequest = { expandedYear = false }
                    ) {
                        years.forEach { year ->
                            val isSelected = selectedYear == year
                            DropdownMenuItem(
                                onClick = {
                                    onYearChange(year)
                                    expandedYear = false
                                },
                                text = {
                                    Text(
                                        text = year,
                                        color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface,
                                        style = if (isSelected) MaterialTheme.typography.bodyLarge else LocalTextStyle.current
                                    )
                                },
                                trailingIcon = {
                                    if (isSelected) Icon(
                                        imageVector = Icons.TwoTone.Check,
                                        contentDescription = stringResource(id = R.string.cd_toggle_drawer)
                                    )
                                }
                            )
                        }
                    }
                }
                }
            }
        }
    }
}


fun getMonthName(monthNumber: String): String {


    return if(monthNumber == "Tous") monthNumber
    else  try {
        when (val number = monthNumber.toInt()) {
            in 1..12 -> months[number - 1]
            13 -> "Tous"
            else -> monthNumber
        }
    } catch (e: NumberFormatException) {
        "Invalid input: not a number"
    }
}




