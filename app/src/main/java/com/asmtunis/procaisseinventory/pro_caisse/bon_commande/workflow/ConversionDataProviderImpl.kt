package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.workflow

import com.asmtunis.procaisseinventory.core.sync_workmanager.InstantSyncTrigger
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.BonCommandeUtils
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import kotlinx.coroutines.flow.first

/**
 * Implementation of ConversionDataProvider that uses sync-enabled methods
 * Following the old app's pattern for automatic synchronization
 */
class ConversionDataProviderImpl(
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val instantSyncTrigger: InstantSyncTrigger
) : ConversionDataProvider {

    override suspend fun getBonCommande(bonCommandeM: String, exercice: String): BonCommande? {
        // For now, return null - this needs to be implemented based on actual DAO methods
        return null
    }

    override suspend fun getLigneBonCommande(bonCommandeM: String, exercice: String): List<LigneBonCommande> {
        // For now, return empty list - this needs to be implemented based on actual DAO methods
        return emptyList()
    }

    override suspend fun checkIfBcAlreadyConverted(bcNumber: String): Boolean {
        // For now, return false - this needs to be implemented based on actual DAO methods
        return false
    }

    override suspend fun getClient(clientCode: String): Client? {
        return proCaisseLocalDb.clients.getOneByCode(clientCode).first()
    }

    override suspend fun getMaxNumTicketForCarnet(carnetId: String): Int {
        // For now, return 0 - this needs to be implemented based on actual DAO methods
        return 0
    }

    override suspend fun getStockArticle(articleCode: String, station: String): Any? {
        // For now, return null - this needs to be implemented based on actual DAO methods
        return null
    }

    override suspend fun getArticle(articleCode: String): Article? {
        return proCaisseLocalDb.articles.getByArtCode(articleCode).first()
    }

    override suspend fun saveTicket(ticket: Ticket) {
        // Regular save without sync
        proCaisseLocalDb.bonLivraison.upsert(ticket)
    }

    override suspend fun saveLigneTickets(ligneTickets: List<LigneTicket>) {
        // Regular save without sync
        proCaisseLocalDb.ligneBonLivraison.upsertAll(ligneTickets)
    }

    override suspend fun updateBonCommandeStatus(bonCommandeM: String, status: String, devDdm: String) {
        proCaisseLocalDb.bonCommande.updateStatus(status, bonCommandeM, devDdm)
    }

    /**
     * Save ticket and lines with sync enabled - following old app pattern
     * This ensures automatic synchronization like in the old app
     */
    override suspend fun saveTicketWithSync(ticket: Ticket, ligneTickets: List<LigneTicket>) {
        // Use the sync-enabled method from BonCommandeUtils
        BonCommandeUtils.saveBLWithSync(
            ticketToSave = ticket,
            ligneTicketsToSave = ligneTickets,
            proCaisseLocalDb = proCaisseLocalDb,
            instantSyncTrigger = instantSyncTrigger
        )
    }

    // Stock management methods for BC to BL conversion
    override suspend fun getStationStockArticle(articleCode: String, station: String): StationStockArticle? {
        return proInventoryLocalDb.stationsArticle.getStationStockArticle(station, articleCode).first()
    }

    override suspend fun updateQtePerStation(newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) {
        proInventoryLocalDb.stationsArticle.updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)
    }

    override suspend fun updateArtQteStock(newQteAllStations: String, newQteStation: String, codeArticle: String) {
        proCaisseLocalDb.articles.updateArtQteStock(newQteAllStations, newQteStation, codeArticle)
    }
}
