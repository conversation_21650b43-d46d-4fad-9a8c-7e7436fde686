package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.repository

import androidx.paging.PagingSource
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.LigneBonCommandeWithImageList
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.dao.InventaireDAO
import kotlinx.coroutines.flow.Flow


class InventaireLocalRepositoryImpl(
        private val inventaireDAO: InventaireDAO
    ) : InventaireLocalRepository {
    override fun upsertAll(value: List<BonCommande>) = inventaireDAO.insertAll(value)

    override fun upsert(value: BonCommande) = inventaireDAO.insert(value)
    override fun getByNumSerie(code: String): Flow<Map<BonCommande, List<LigneBonCommande>>?>  =
        inventaireDAO.getByNumSerie(code)

    override fun getByCodeM(codeM: String): Flow<Map<BonCommande, List<LigneBonCommande>>?> = inventaireDAO.getByCodeM(codeM)

    override fun notSynced(
        typeInv: String,
        devEtat: String
    ): Flow<Map<BonCommande, List<LigneBonCommandeWithImageList>>>  = inventaireDAO.notSynced(
        typeInv = typeInv,
        devEtat = devEtat
    )

    override fun setSynced(devNum: String, devNumM: String) = inventaireDAO.setSynced(
        devNum = devNum,
        devNumM = devNumM
    )

    override fun setToInserted(devNumM: String) = inventaireDAO.setToInserted(devNumM = devNumM)

    override fun setToFailed(devNumM: String) = inventaireDAO.setToFailed(devNumM = devNumM)

    override fun setLgSynced(devNum: String, devNumM: String) = inventaireDAO.setLgSynced(
        devNum = devNum,
        devNumM = devNumM
    )

    override fun deleteAll() = inventaireDAO.deleteAll()
    override fun deleteByIdM(codeM: String) = inventaireDAO.deleteByIdM(codeM = codeM)

    override fun getAll(station: String, devEtat: String): Flow<Map<BonCommande, List<LigneBonCommande>>> = inventaireDAO.getAll(station = station, devEtat = devEtat)


    override fun getAllFiltred(
        station: String,
        isAsc: Int,
        sortBy: String,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): Flow<Map<BonCommande, List<LigneBonCommande>>> = inventaireDAO.getAllFiltred(
        station = station,
        isAsc = isAsc,
        sortBy = sortBy,
        devEtat = devEtat,
        nbrMonth = nbrMonth,
        year = year
    )

    override fun filterByNumSerie(
        station: String,
        searchString: String,
        sortBy: String,
        isAsc: Int,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): Flow<Map<BonCommande, List<LigneBonCommande>>> = inventaireDAO.filterByNumSerie(
        station = station,
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        devEtat = devEtat,
        nbrMonth = nbrMonth,
        year = year
    )

    override fun filterByBonCommandeNum(
        station: String,
        searchString: String,
        sortBy: String,
        isAsc: Int,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): Flow<Map<BonCommande, List<LigneBonCommande>>> = inventaireDAO.filterByBonCommandeNum(
        station = station,
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        devEtat = devEtat,
        nbrMonth = nbrMonth,
        year = year
    )

    override fun filterByClient(
        station: String,
        searchString: String,
        sortBy: String,
        isAsc: Int,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): Flow<Map<BonCommande, List<LigneBonCommande>>>  = inventaireDAO.filterByClient(
        station = station,
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        devEtat = devEtat,
        nbrMonth = nbrMonth,
        year = year
    )

    override suspend fun getNotSyncedByType(devEtat: String): List<BonCommande> = inventaireDAO.getNotSyncedByType(devEtat)

    // Pagination method implementations
    override fun getAllPatrimoinePaginated(
        station: String,
        isAsc: Int,
        sortBy: String,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): PagingSource<Int, BonCommande> = inventaireDAO.getAllPatrimoinePaginated(
        station = station,
        isAsc = isAsc,
        sortBy = sortBy,
        devEtat = devEtat,
        nbrMonth = nbrMonth,
        year = year
    )

    override fun filterByClientPaginated(
        station: String,
        searchString: String,
        sortBy: String,
        isAsc: Int,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): PagingSource<Int, BonCommande> = inventaireDAO.filterByClientPaginated(
        station = station,
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        devEtat = devEtat,
        nbrMonth = nbrMonth,
        year = year
    )

    override fun filterByBonCommandeNumPaginated(
        station: String,
        searchString: String,
        sortBy: String,
        isAsc: Int,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): PagingSource<Int, BonCommande> = inventaireDAO.filterByBonCommandeNumPaginated(
        station = station,
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        devEtat = devEtat,
        nbrMonth = nbrMonth,
        year = year
    )

    override fun getLignesByDevNum(devNum: String): Flow<List<LigneBonCommande>> = inventaireDAO.getLignesByDevNum(devNum)
}