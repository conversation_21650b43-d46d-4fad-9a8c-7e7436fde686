package com.asmtunis.procaisseinventory.pro_caisse.client.data.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.CLIENT_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import kotlinx.coroutines.flow.Flow

@Dao
interface ClientDAO {
    @get:Query("SELECT * FROM $CLIENT_TABLE order by CLI_NomPren desc")
    val all: Flow<List<Client>>

    @Query("SELECT * FROM $CLIENT_TABLE WHERE CLI_Type != :CLIType  order by strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) desc ")
    fun getAllByexepProspectMutable(CLIType: String?): Flow<List<Client>>

    @Query("SELECT * FROM $CLIENT_TABLE WHERE CLI_Type = :CLIType order by strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) desc ")
    fun getAllProspectMutable(CLIType: String?): Flow<List<Client>>

    @Query("SELECT count(*) FROM $CLIENT_TABLE")
    fun count(): Flow<Int>

    @Query("SELECT count(*) FROM $CLIENT_TABLE WHERE CASE WHEN :station != '' THEN CLI_Station = :station ELSE CLI_Station != :station or CLI_Station like :station or CLI_Station is null END")
    fun countByStation(station: String): Flow<Int>




    @get:Query("SELECT * FROM Client WHERE isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val getNotSync: Flow<List<Client>>
/*    @get:Query("SELECT count(*) FROM $CLIENT_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCount: Int

    @get:Query("SELECT count(*) FROM $CLIENT_TABLE where isSync=0 and  Status='TOUPDATE'")
    val noSyncCountToUpdate: Int

    @get:Query("SELECT count(*) FROM $CLIENT_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMutable: Flow<Int?>?
*/
    // @Query("SELECT count(*) FROM Client WHERE CLI_Station = :station")
  /*  @get:Query("SELECT count(*) FROM $CLIENT_TABLE")
    val count: Int?

    @get:Query("SELECT * FROM $CLIENT_TABLE WHERE isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val byQuery: List<Client>

    @get:Query("SELECT * FROM $CLIENT_TABLE WHERE isSync=0 and  Status='TOUPDATE'")
    val clientToUpdateByQuery: Client

    @get:Query("SELECT count(*) FROM $CLIENT_TABLE where isSync=0 and  Status='TOUPDATE'")
    val noSyncToUpdateCountMutable: Flow<Int?>?*/

    //  @Query("SELECT ifnull(MAX(cast(substr(CLI_Code,length(:prefix) + 1 ,length(CLI_Code)) as integer)),0)+1 FROM $CLIENT_TABLE WHERE substr(CLI_Code, 0 ,length(:prefix)+1) = :prefix")
    // fun getNewCode(prefix: String?): Client

    @Query("SELECT * FROM $CLIENT_TABLE WHERE CLI_Code = :code ")
    fun getOneByCode(code: String): Flow<Client?>

    @Query("SELECT * FROM $CLIENT_TABLE WHERE CLI_Code_M = :code ")
    fun getOneByCodeM(code: String?): Client?





    // @Query("SELECT * FROM Client WHERE CLI_Station = :station ")
    @get:Query("SELECT * FROM $CLIENT_TABLE")
    val byStation: List<Client>

    // @Query("SELECT * FROM Client WHERE CLI_Station = :station order by CAST(CLI_Date_Cre AS DATE) desc ,CLI_Date_Cre desc")
    @get:Query("SELECT * FROM $CLIENT_TABLE order by strftime('%Y-%m-%d %H-%M',CLI_Date_Cre) desc ")
    val byStationMutable: Flow<List<Client>>

    @Query("SELECT * FROM $CLIENT_TABLE where CLI_Station = :station or CLI_Station is null order by CLI_NomPren asc ")
    fun getAllByStationMutable(station: String?): Flow<List<Client>>

    @get:Query("SELECT * FROM $CLIENT_TABLE LIMIT 1")
    val one: Client?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Client)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Client>)

    // @Query("DELETE FROM Client where Status='SELECTED'")
    //   void deleteAll();
    @Query("DELETE FROM $CLIENT_TABLE")
    fun deleteAll()

    //  @Query("DELETE FROM $CLIENT_TABLE where CLI_Code= :code_client")
    //  fun deleteById(code_client: Client?)

    @Query("UPDATE $CLIENT_TABLE SET isSync = 1, Status= 'SELECTED', CLI_Code=:code where CLI_Code_M = :codeM")
    fun updateSyncClient(codeM: String, code: String)


    @Query("UPDATE $CLIENT_TABLE SET  Solde=:soldClient where CLI_Code = :codeClt")
    fun updateSoldClient(codeClt: String, soldClient: String)

    @Query("UPDATE $CLIENT_TABLE SET  Solde=:soldClient, Cli_Credit = :CLICredit, Debit = :CLIDebit where CLI_Code = :codeClt")
    fun updateMoneyClient(codeClt: String, soldClient: String, CLICredit: String, CLIDebit: String)




    @Delete
    fun delete(client: Client)

    @Query("DELETE FROM $CLIENT_TABLE where CLI_Code_M= :code_client")
    fun deleteBycodeM(code_client: String?)

    @get:Query("SELECT strftime('%Y-%m-%d',CLI_DDm) FROM $CLIENT_TABLE order by strftime('%Y-%m-%d %H-%M',CLI_DDm) desc limit 1")
    val dDM: String?

   /* @get:Query("SELECT count(*) FROM $CLIENT_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountNonMutable: Int?*/

    @Query("SELECT count(*) FROM $CLIENT_TABLE where CLI_Station=:station")
    fun getAllCountBySessionMutable(station: String?): Flow<Int>

    @Transaction
    @Query(
        "SELECT * FROM $CLIENT_TABLE " +
            "WHERE CLI_NomPren LIKE '%' || :filterString || '%' " +
            // "and  (CASE WHEN :sold = 1 THEN CAST (Solde AS INT)  > 0 ELSE (Solde AS INT)<=0 END)" +
                "and (CASE WHEN :sold = 1 THEN (CAST (Solde AS REAL))  >= 0 ELSE NULL END " +
                "or CASE WHEN :sold = 0 THEN (CAST (Solde AS REAL))<0 ELSE NULL END " +
                "or CASE WHEN :sold = 2 THEN (CAST (Solde AS REAL))<0 or (CAST (Solde AS REAL))  >= 0 ELSE NULL END)" +
                "and  CASE WHEN :filterType !=  '' THEN CLI_Type =:filterType ELSE CLI_Type !=:filterType or CLI_Type like:filterType END " +
                "and   CASE WHEN :filterByClientEtat !=  '' THEN CLI_Etat =:filterByClientEtat ELSE CLI_Etat!=:filterByClientEtat END " +
                "and  CASE WHEN :filterByStation !=  '' THEN CLI_Station =:filterByStation ELSE CLI_Station !=:filterByStation or CLI_Station like:filterByStation or CLI_Station is null END " +
            " ORDER BY " +

                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_DDm'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) END ASC, " +
                "CASE WHEN :sortBy = 'CLI_DDm'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) END DESC, " +
                "CASE WHEN :sortBy = 'Solde'  AND :isAsc = 1 THEN (CAST (Solde AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'Solde'  AND :isAsc = 2 THEN (CAST (Solde AS REAL)) END DESC "

    )
    fun filterByName(filterString: String, sortBy: String?, sold: Int?, isAsc: Int?, filterType : String, filterByClientEtat: String, filterByStation: String): Flow<List<Client>>

    @Transaction
    @Query(
        "SELECT * FROM $CLIENT_TABLE " +
            "WHERE CLI_Code LIKE '%' || :filterString || '%' " +
                "and (CASE WHEN :sold = 1 THEN (CAST (Solde AS REAL))  >= 0 ELSE NULL END " +
                "or CASE WHEN :sold = 0 THEN (CAST (Solde AS REAL))<0 ELSE NULL END " +
                "or CASE WHEN :sold = 2 THEN (CAST (Solde AS REAL))<0 or (CAST (Solde AS REAL))  >= 0 ELSE NULL END)" +
                "and  CASE WHEN :filterType !=  '' THEN CLI_Type =:filterType ELSE CLI_Type !=:filterType or CLI_Type like:filterType END " +
                "and   CASE WHEN :filterByClientEtat !=  '' THEN CLI_Etat =:filterByClientEtat ELSE CLI_Etat!=:filterByClientEtat  END " +
                "and  CASE WHEN :filterByStation !=  '' THEN CLI_Station =:filterByStation ELSE CLI_Station !=:filterByStation or CLI_Station like:filterByStation or CLI_Station is null END " +
            " ORDER BY " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_DDm'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) END ASC, " +
                "CASE WHEN :sortBy = 'CLI_DDm'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) END DESC, " +
                "CASE WHEN :sortBy = 'Solde'  AND :isAsc = 1 THEN (CAST (Solde AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'Solde'  AND :isAsc = 2 THEN (CAST (Solde AS REAL)) END DESC "
    )
    fun filterByCLICode(filterString: String, sortBy: String?, sold: Int?, isAsc: Int?, filterType : String, filterByClientEtat: String, filterByStation: String): Flow<List<Client>>

    @Transaction
    @Query(
        "SELECT * FROM $CLIENT_TABLE " +
            "WHERE CLI_MatFisc LIKE '%' || :filterString || '%' " +
                "and ( CASE WHEN :sold = 1 THEN (CAST (Solde AS REAL))  >= 0 ELSE NULL END " +
                "or CASE WHEN :sold = 0 THEN (CAST (Solde AS REAL))<0 ELSE NULL END " +
                "or CASE WHEN :sold = 2 THEN (CAST (Solde AS REAL))<0 or (CAST (Solde AS REAL))  >= 0 ELSE NULL END)" +
                "and  CASE WHEN :filterType !=  '' THEN CLI_Type =:filterType ELSE CLI_Type !=:filterType or CLI_Type like:filterType END " +
                "and   CASE WHEN :filterByClientEtat !=  '' THEN CLI_Etat =:filterByClientEtat ELSE CLI_Etat!=:filterByClientEtat END " +
                "and  CASE WHEN :filterByStation !=  '' THEN CLI_Station =:filterByStation ELSE CLI_Station !=:filterByStation or CLI_Station like:filterByStation or CLI_Station is null END " +
            " ORDER BY " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_DDm'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) END ASC, " +
                "CASE WHEN :sortBy = 'CLI_DDm'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) END DESC, " +
                "CASE WHEN :sortBy = 'Solde'  AND :isAsc = 1 THEN (CAST (Solde AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'Solde'  AND :isAsc = 2 THEN (CAST (Solde AS REAL)) END DESC "
    )
    fun filterByCLIMat(filterString: String, sortBy: String?, sold: Int?, isAsc: Int?, filterType : String, filterByClientEtat: String, filterByStation: String): Flow<List<Client>>

    @Transaction
    @Query(
        "SELECT * FROM $CLIENT_TABLE " +
            "WHERE (CASE WHEN :sold = 1 THEN (CAST (Solde AS REAL))  >= 0 ELSE NULL END " +
            "or  CASE WHEN :sold = 0 THEN (CAST (Solde AS REAL))<0 ELSE NULL END " +
             "or  CASE WHEN :sold = 2 THEN (CAST (Solde AS REAL))>=0 or(CAST (Solde AS REAL))<0 ELSE NULL END) " +
                "and  CASE WHEN :filterType !=  '' THEN CLI_Type =:filterType ELSE CLI_Type !=:filterType or CLI_Type like:filterType END " +
                "and   CASE WHEN :filterByClientEtat !=  '' THEN CLI_Etat =:filterByClientEtat ELSE CLI_Etat !=:filterByClientEtat END " +
                "and  CASE WHEN :filterByStation !=  '' THEN CLI_Station =:filterByStation ELSE CLI_Station !=:filterByStation or CLI_Station like:filterByStation or CLI_Station is null END " +

            " ORDER BY " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_DDm'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) END ASC, " +
                "CASE WHEN :sortBy = 'CLI_DDm'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',CLI_Date_Cre) END DESC, " +
                "CASE WHEN :sortBy = 'Solde'  AND :isAsc = 1 THEN (CAST (Solde AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'Solde'  AND :isAsc = 2 THEN (CAST (Solde AS REAL)) END DESC "
    )
    fun getAllFiltred(isAsc: Int?, sortBy: String?, sold: Int?, filterType : String, filterByClientEtat: String, filterByStation: String): Flow<List<Client>>





}
