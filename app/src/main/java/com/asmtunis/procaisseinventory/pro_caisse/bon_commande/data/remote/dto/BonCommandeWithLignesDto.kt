package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.dto

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonIgnoreUnknownKeys

/**
 * Enhanced LigneBonCommande for sync operations that includes article properties
 * This prevents server errors like "Trying to get property 'ART_TVA' of non-object"
 */
@Serializable
data class LigneBonCommandeForSync(
    // All original ligne properties
    @SerialName("LG_DEV_NumBon")
    var lGDEVNumBon: String = "",

    @SerialName("LG_DEV_Code_M")
    var lGDEVCodeM: String? = "",

    @SerialName("LG_DEV_Exerc")
    var lGDEVExerc: String = "",

    @SerialName("LG_DEV_CodeArt")
    var lGDEVCodeArt: String = "",

    @SerialName("LG_DEV_Qte")
    var lGDEVQte: String? = "",

    @SerialName("LG_DEV_Unite")
    var lGDEVUnite: String? = "",

    @SerialName("LG_DEV_PUHT")
    var lGDEVPUHT: String? = "",

    @SerialName("LG_DEV_Tva")
    var lGDEVTva: String? = "",

    @SerialName("LG_DEV_Netht")
    var lGDEVNetht: String? = "",

    @SerialName("LG_DEV_Remise")
    var lGDEVRemise: String? = "",

    @SerialName("LG_DEV_station")
    var lGDEVStation: String? = "",

    @SerialName("LG_DEV_User")
    var lGDEVUser: String? = "",

    @SerialName("LG_DEV_NumOrdre")
    var lGDEVNumOrdre: Int = 0,

    @SerialName("LG_DEV_Tarif")
    var lGDEVTarif: String? = "",

    @SerialName("LG_DEV_QtePiece")
    var lGDEVQtePiece: String? = "",

    @SerialName("LG_DEV_export")
    var lGDEVExport: String? = "",

    @SerialName("LG_DEV_DDm")
    var lGDEVDDm: String? = "",

    @SerialName("LG_DEV_MntTTC")
    var lGDEVMntTTC: String? = "",

    @SerialName("LG_DEV_MntHT")
    var lGDEVMntHT: String? = "",

    @SerialName("LG_DEV_PUTTC")
    var lGDEVPUTTC: String? = "",

    @SerialName("LG_DEV_TauxFodec")
    var lGDEVTauxFodec: String? = "",

    @SerialName("LG_DEV_TauxDc")
    var lGDEVTauxDc: String? = "",

    @SerialName("LG_DEV_MntBrutHT")
    var lGDEVMntBrutHT: String? = "",

    @SerialName("LG_DEV_MntFodec")
    var lGDEVMntFodec: String? = "",

    @SerialName("LG_DEV_MntDc")
    var lGDEVMntDc: String? = "",

    @SerialName("LG_DEV_MntTva")
    var lGDEVMntTva: String? = "",

    @SerialName("LG_DEV_QteGratuite")
    var lGDEVQteGratuite: String? = "",

    @SerialName("LG_DEV_NumSerie")
    var lGDevNumSerie: String? = "",

    @SerialName("LG_DEV_CMarq")
    var lGDEVCMarq: String? = "",

    @SerialName("LG_DEV_Note")
    val lgDEVNote: String? = "",

    @SerialName("LG_DEV_N_DEV_IN")
    val lgDevNDevIN: String? = "",

    // Essential article properties that the server expects
    @SerialName("ART_Code")
    var aRTCode: String = "",

    @SerialName("ART_TVA")
    var aRTTVA: Double = 0.0,

    @SerialName("ART_Designation")
    var aRTDesignation: String = "",

    @SerialName("ART_PrixUnitaireHT")
    var aRTPrixUnitaireHT: String = "",

    @SerialName("ART_CodeBar")
    var aRTCodeBar: String = "",

    @SerialName("ART_QteStock")
    var aRTQteStock: String = "",

    @SerialName("ART_Unite")
    var aRTUnite: String? = "",

    @SerialName("ART_Famille")
    var aRTFamille: String? = "",

    @SerialName("ART_Marque")
    var aRTMarque: String? = ""
)

/**
 * Temporary data class for parsing API response with nested lignes_devis
 * This is used only for API response parsing and not stored in database
 */
@OptIn(ExperimentalSerializationApi::class)
@Serializable
@JsonIgnoreUnknownKeys
data class BonCommandeWithLignes(
    @SerialName("DEV_Code_M")
    val devCodeM: String? = "",
    @SerialName("DEV_Num")
    val dEVNum: String? = "",
    @SerialName("DEV_Exerc")
    val dEVExerc: String? = "",
    @SerialName("DEV_Date")
    val dEVDate: String? = "",
    @SerialName("DEV_CodeClient")
    val dEVCodeClient: String? = "",
    @SerialName("DEV_StationOrigine")
    val dEVStationOrigine: String? = "",
    @SerialName("DEV_Etat")
    val dEVEtat: String? = "",
    @SerialName("DEV_Station")
    val dEVStation: String? = "",
    @SerialName("DEV_User")
    val dEVUser: String? = "",
    @SerialName("DEV_Mntht")
    val dEVMntht: String? = "",
    @SerialName("DEV_MntNetHt")
    val dEVMntNetHt: String? = "",
    @SerialName("DEV_MntTva")
    val dEVMntTva: String? = "",
    @SerialName("DEV_MntTTC")
    val dEVMntTTC: String? = "",
    @SerialName("DEV_TauxRemise")
    val dEVTauxRemise: String? = "",
    @SerialName("DEV_Remise")
    val dEVRemise: String? = "",
    @SerialName("DEV_Regler")
    val dEVRegler: String? = "",
    @SerialName("DEV_export")
    val dEVExport: String? = "",
    @SerialName("DEV_DDm")
    val dEVDDm: String? = "",
    @SerialName("DEV_ExoNum")
    val dEVExoNum: String? = "",
    @SerialName("DEV_ExoVal")
    val dEVExoVal: String? = "",
    @SerialName("DEV_Timbre")
    val dEVTimbre: String? = "",
    @SerialName("DEV_Exonoration")
    val dEVExonoration: String? = "",
    @SerialName("DEV_Chauffeur")
    val dEVChauffeur: String? = "",
    @SerialName("DEV_vehicule")
    val dEVVehicule: String? = "",
    @SerialName("DEV_Observation")
    val dEVObservation: String? = "",
    @SerialName("DEV_Client")
    val dEVClient: String? = "",
    @SerialName("DEV_MntFodec")
    val dEVMntFodec: String? = "",
    @SerialName("DEV_MntDC")
    val dEVMntDC: String? = "",
    @SerialName("DEV_EtatBon")
    val dEVEtatBon: String? = "",
    @SerialName("BON_LIV_Num")
    val bONLIVNum: String? = "",
    @SerialName("BON_LIV_Exerc")
    val bONLIVExerc: String? = "",
    @SerialName("DEV_info3")
    val dEV_info3: String? = null,
    @SerialName("DEV_TyMvtCode")
    val dEVTyMvtCode: String? = "",
    @SerialName("DEV_Code_SF")
    val devCodeSF: String? = "",
    @SerialName("DEV_Code_SO")
    val devCodeSO: String? = "",

    // Additional fields from JSON response that may be present in API
    @SerialName("DEV_Latitude")
    val dEVLatitude: String? = "",
    @SerialName("DEV_Longitude")
    val dEVLongitude: String? = "",
    @SerialName("DEV_Zone")
    val dEVZone: String? = "",
    @SerialName("DEV_Ville")
    val dEVVille: String? = "",
    @SerialName("DEV_info1")
    val dEVInfo1: String? = "",
    @SerialName("DEV_info2")
    val dEVInfo2: String? = "",
    @SerialName("DEV_CBLiv")
    val dEVCBLiv: String? = "",
    @SerialName("DEV_LinkLiv")
    val dEVLinkLiv: String? = "",
    @SerialName("DEV_FraisLiv")
    val dEVFraisLiv: String? = "",
    @SerialName("DEV_EtatWeb")
    val dEVEtatWeb: String? = "",
    @SerialName("DEV_NumPiece")
    val dEVNumPiece: String? = "",
    @SerialName("DEV_TypePiece")
    val dEVTypePiece: String? = "",
    @SerialName("row_num")
    val rowNum: String? = "",

    // Additional fields that might be present in API response for compatibility
    @SerialName("DEV_Client_Name")
    val dEVClientName: String? = "",
    @SerialName("code")
    val code: String? = "",
    @SerialName("msg")
    val msg: String? = "",

    // Nested lignes_devis array
    @SerialName("lignes_devis")
    val lignesDevis: List<LigneBonCommande>? = null
)
