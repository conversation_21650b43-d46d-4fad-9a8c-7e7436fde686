package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.DrawerState
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.BonRetourDetailRoute
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.calculateDiscountAmount
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertDoubleToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.getClientName
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model.BonRetourViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.dokar.sonner.rememberToasterState
import kotlinx.coroutines.launch
import kotlin.math.abs

@Composable
fun BonRetourListPane(
    drawer: DrawerState,
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    navDrawerViewmodel: NavigationDrawerViewModel,
    bonRetourViewModel: BonRetourViewModel,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    mainViewModel : MainViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    wifiPrintVM: WifiPrintViewModel,
    sunmiPrintManager: SunmiPrintManager,
    settingVM: SettingViewModel,
    syncProcaisseViewModels: SyncProcaisseViewModels

    ) {
    val syncBonRetourViewModel = syncProcaisseViewModels.syncBonRetourViewModel
    val uiWindowState = settingVM.uiWindowState
    val density = LocalDensity.current
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val clientList = mainViewModel.clientList

    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode
    val bonRetourListstate = bonRetourViewModel.bonRetourListstate
    val listFilter = bonRetourListstate.search
    val listOrder = bonRetourListstate.listOrder
    val filterList = context.resources.getStringArray(R.array.bn_retour_filter)



    val selectedSessionCaisse = navDrawerViewmodel.sessionCaisse
    val isConnected = networkViewModel.isConnected
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData

    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val exerciceCode = mainViewModel.exerciceList.firstOrNull()?.exerciceCode?: ""
    val utilisateur = mainViewModel.utilisateur

    val selectedBonRetourWithClient = bonRetourViewModel.selectedBonRetourWithClient
    val selectedListLgBonRetourWithArticle = bonRetourViewModel.selectedListLgBonRetourWithArticle
    val bonRetour = selectedBonRetourWithClient.bonRetour

    val firstTimeConnected = printViewModel.firstTimeConnected
    val showSearchView = bonRetourViewModel.showSearchView
    val searchTextState = bonRetourViewModel.searchTextState

    val listState = rememberLazyListState()

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingVM.isDarkTheme)

    LaunchedEffect(
        key1 = searchTextState.text,
        key2 = bonRetourListstate.lists,
        // key4=state.filterByStationSource
    ) {
        bonRetourViewModel.filterBonRetour(
            bonRetourFilterListState = bonRetourListstate,
            utilisateur = utilisateur
        )
    }

    LaunchedEffect(key1 = clientByCode) {

        bonRetourViewModel.onSearchValueChange(TextFieldValue(clientByCode.cLICode))
        bonRetourViewModel.onEvent(
            event = ListEvent.ListSearch(ListSearch.ThirdSearch()),
            utilisateur = utilisateur
        )
    }

    LaunchedEffect(key1 = firstTimeConnected){
        if(!firstTimeConnected) return@LaunchedEffect

        // Direct printing without note functionality
        when {
            printParams.useSunmiPrinter -> {
                sunmiPrintManager.printBonRetour(
                    context = context,
                    bonRetourWithClient = selectedBonRetourWithClient,
                    lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
            }
            printParams.printViaWifi -> {
                wifiPrintVM.createBonRetourPDFFile(
                    context = context,
                    bonRetourWithClient = selectedBonRetourWithClient,
                    lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                    listener = {}
                )
            }
            else -> {
                // Bluetooth printing
                printViewModel.printBonRetour(
                    context = context,
                    bonRetourWithClient = selectedBonRetourWithClient,
                    lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
            }
        }

        bonRetourViewModel.onShowCustomModalBottomSheetChange(false)
        printViewModel.onFirstTimeConnectedChange(firstConnect = false)

    }

        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = {
                        // Navigate to shortcuts screen instead of opening drawer
                        navigate(com.asmtunis.procaisseinventory.core.navigation.HomePageRoute)
                    },
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    title = stringResource(id = navDrawerViewmodel.proCaisseSelectedMenu.title),
                    titleVisibilty = !showSearchView && searchTextState.text.isEmpty(),

                    actions = {
                        SearchSectionComposable(
                            label = context.getString(R.string.filter_by,
                                when (listFilter) {
                                    is ListSearch.FirstSearch -> filterList[0]
                                    is ListSearch.SecondSearch -> filterList[1]
                                    else -> filterList[2]}),
                            searchVisibility  = showSearchView || searchTextState.text.isNotEmpty(),
                            searchTextState = searchTextState,
                            onSearchValueChange = {
                                bonRetourViewModel.onSearchValueChange(TextFieldValue(it))
                                if(it == "")   {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                               //     mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowSearchViewChange = {
                                bonRetourViewModel.onShowSearchViewChange(it)
                                if(!it) {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    bonRetourViewModel.onSearchValueChange(TextFieldValue(""))
                                   // mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowCustomFilterChange = {
                                bonRetourViewModel.onShowCustomFilterChange(it)
                            }
                        )
                    },
                    )
            },
            floatingActionButton = {
                SnapScrollingButton(
                    isScrollInProgress = listState.isScrollInProgress,
                    isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15,
                    density = density,
                    animateScrollToItem = {
                        listState.animateScrollToItem(index = it)
                    }
                )

            }
        ) { padding ->
            if(printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }

            if (bonRetourViewModel.showCustomModalBottomSheet)
                CustomModalBottomSheet(
                    remoteResponseState = syncBonRetourViewModel.responseAddBonRetourState,
                    status = selectedBonRetourWithClient.bonRetour?.status?:"",
                    title = bonRetour?.bORNumero?: "NUll Number",
                    showDeleteIcon = bonRetour?.isSync == false,
                    onDismissRequest= { bonRetourViewModel.onShowCustomModalBottomSheetChange(false) },
                    onDeleteRequest= {
                        bonRetourViewModel.deleteBonRetourAndItsLines(
                            listStationStockArticl = mainViewModel.stationStockArticlMapByBarCode,
                            updateQtePerStation = {newQteStation, newSartQteDeclare, codeArticle, codeStation->
                                mainViewModel.updateQtePerStation (
                                    newQteStation = newQteStation,
                                    newSartQteDeclare = newSartQteDeclare,
                                    codeArticle = codeArticle,
                                    codeStation = codeStation
                                )
                            },
                            updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->

                                mainViewModel.updateArtQteStock(
                                    newQteAllStations = newQteAllStations,
                                    newQteStation = newQteStation,
                                    codeArticle = codeArticle
                                )
                            })

                    },
                    onPrintRequest= {
                        PrintFunctions.print(
                            navigate = { navigate(it) },
                            context = context,
                            toaster = toaster,
                            printParams = printParams,
                            printViewModel = printViewModel,
                            bluetoothVM = bluetoothVM,
                            sunmiPrintManager = sunmiPrintManager,
                            toPrintBT = {
                                printViewModel.printBonRetour(
                                    context = context,
                                    bonRetourWithClient = selectedBonRetourWithClient,
                                    lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                                    utilisateur = utilisateur,
                                    printParams = printParams
                                )
                            },
                            toPrintWifi = {
                                wifiPrintVM.createBonRetourPDFFile(
                                    context = context,
                                    bonRetourWithClient = selectedBonRetourWithClient,
                                    lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                                    listener = {}
                                )
                            },
                            toPrintSunmi = {
                                sunmiPrintManager.printBonRetour(
                                    context = context,
                                    bonRetourWithClient = selectedBonRetourWithClient,
                                    lgBonRetourWithArticle = selectedListLgBonRetourWithArticle,
                                    utilisateur = utilisateur,
                                    printParams = printParams
                                )
                            }
                        )
                    },
                    onSyncRequest = { syncBonRetourViewModel.syncBonRetour(selectedBonRetour = selectedBonRetourWithClient) }
                )

            if (bonRetourViewModel.showCustomFilter) {
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.bn_retour_order),
                    onShowCustomFilterChange  = {
                        bonRetourViewModel.onShowCustomFilterChange(false)
                    },
                    onEvent = {
                        bonRetourViewModel.onEvent(event = it, utilisateur = utilisateur)
                    }
                )
            }
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
                    .padding(top = 12.dp)
            ) {



                if(getProCaisseDataViewModel.bonRetourState.loading)
                    LottieAnim(lotti = R.raw.loading, size = 250.dp)
                else {
                    if (bonRetourListstate.lists.isNotEmpty()) {
                        BonRetourList(
                            selectedBonRetourWithClient = selectedBonRetourWithClient,
                            listState = listState,
                            navigate = { navigate(it) },
                            isConnected = isConnected,
                            selectedBaseconfig = selectedBaseconfig,
                            clientList = clientList,
                            exerciceCode = exerciceCode,
                            utilisateur = utilisateur,
                            articleMapByBarCode = articleMapByBarCode,
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            bonRetourViewModel = bonRetourViewModel,
                            filteredList = bonRetourListstate.lists,
                            selectArtMobilityVM = selectArtMobilityVM
                        )
                    } else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                }



            }
        }

}

@Composable
fun BonRetourList(
    listState: LazyListState,
    navigate: (route: Any) -> Unit,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    clientList: List<Client>,
    exerciceCode: String,
    utilisateur: Utilisateur,
    articleMapByBarCode: Map<String, Article>,
    selectedBonRetourWithClient: BonRetourWithClient,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    bonRetourViewModel: BonRetourViewModel,
    filteredList: Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>
) {

    val bonRetour: MutableList<BonRetour> = arrayListOf()
    val bonRetourWithClient: MutableList<BonRetourWithClient> = arrayListOf()
    val ligneBonRetour: MutableList<LigneBonRetour> = arrayListOf()



    filteredList.forEach { (key, value) ->
        run {
            bonRetourWithClient.add(key)
            bonRetour.add(key.bonRetour?: BonRetour())
            ligneBonRetour.addAll(value.map { it.ligneBonRetour?: LigneBonRetour() })
        }
    }


    val isRefreshing  = getProCaisseDataViewModel.bonRetourState.loading || getProCaisseDataViewModel.ligneBonRetourState.loading || getProCaisseDataViewModel.sessionCaisseState.loading

    PullToRefreshLazyColumn(
        items = bonRetourWithClient,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !bonRetour.any { !it.isSync } && isConnected,
        onRefresh = {
            /**
             * Get getSessionCaissesByUser not  getBonRetour and getLigneBonRetour to ensure no conflict
             * also applicable for bon commande and Ticket
             */
            getProCaisseDataViewModel.getSessionCaissesByUser(
                baseConfig = selectedBaseconfig,
                utilisateur = utilisateur,
                clientList = clientList,
                exerciceCode = exerciceCode
            )


        },
        key = { bnRetourWithClient -> bnRetourWithClient.bonRetour?.id?: bnRetourWithClient },
        content = { bnRetourWithClient ->
            val br = bnRetourWithClient.bonRetour?: BonRetour()
            ListItem(
                isSelected = bnRetourWithClient == selectedBonRetourWithClient,
                firstText = br.bORNumero,
                secondText = getClientName(cltName = br.bORNomfrs, cltCode = br.bORCodefrs),
                thirdText = StringUtils.convertStringToPriceFormat((abs(stringToDouble(br.bORMntTTC)) -  abs(stringToDouble(br.bORMntRemise))).toString()),
                // forthText = " : " + bonEntree[index].,
                dateText = br.bORDateFormatted?:"N/A",
                isSync = br.isSync,
                status = br.status,
                onResetDeletedClick = { /*bonRetourViewModel.restDeletedVisite(bonEntree[index])*/ },
                onItemClick = {
                    bonRetourViewModel.restBonRetour()
                    selectArtMobilityVM.resetSelectedMobilityArticles()
                    bonRetourViewModel.onSelectedBonRetourChange(filteredList.filter { it.key == bnRetourWithClient })


                    val listLigneBonRetour =  bonRetourViewModel.selectedListLgBonRetour

                    //  selectArtMobilityVM.resetSelectedMobilityArticles()
                    for (i in listLigneBonRetour.indices) {
                        val article = articleMapByBarCode[listLigneBonRetour[i].lIGBonEntreeCodeArt]?: Article(filsCodeBar = listLigneBonRetour[i].lIGBonEntreeCodeArt, aRTCode = listLigneBonRetour[i].lIGBonEntreeCodeArt, aRTCodeBar = listLigneBonRetour[i].lIGBonEntreeCodeArt)
                        val qty = listLigneBonRetour[i].lIGBonEntreeQte
                        //  val mntDiscount = (article.pvttc - abs(stringToDouble(listLigneBonRetour[i].lIGBonEntreePUTTC) * stringToDouble(qty) ))

                        val mntDiscount = calculateDiscountAmount(amount = article.pvttc, discount = stringToDouble(listLigneBonRetour[i].lIGBonEntreeRemise))


                        val prixCaisse = stringToDouble(listLigneBonRetour[i].lIGBonEntreeMntTTC) / stringToDouble(qty)

                        val selectedArticle = SelectedArticle(
                            article = article,
                            prixVente = article.pvttc.toString(),
                            quantity = qty,
                            prixCaisse = convertDoubleToDoubleFormat(prixCaisse),
                            discount = listLigneBonRetour[i].lIGBonEntreeRemise,
                            mntDiscount = mntDiscount.toString(),
                            lTMtTTC = convertStringToDoubleFormat(listLigneBonRetour[i].lIGBonEntreeMntTTC),
                            lTMtBrutHT = listLigneBonRetour[i].lIGPVENTEHT?: "0.0",
                            tva = Tva(),
                            mntTva = ""
                        )
                        selectArtMobilityVM.setConsultationSelectedArticleMobilityList(selectedArticle = selectedArticle)


                    }

                    navigate(BonRetourDetailRoute)
                },
                onMoreClick = {
                    bonRetourViewModel.restBonRetour()
                    selectArtMobilityVM.resetSelectedMobilityArticles()
                    bonRetourViewModel.onSelectedBonRetourChange(filteredList.filter { it.key == bnRetourWithClient })
                    bonRetourViewModel.onShowCustomModalBottomSheetChange(true)
                }
                )
        },
    )
}