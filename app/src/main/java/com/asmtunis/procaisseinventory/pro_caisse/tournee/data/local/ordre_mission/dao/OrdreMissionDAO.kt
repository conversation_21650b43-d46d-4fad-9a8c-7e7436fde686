package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ordre_mission.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMissionWithClient
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMissionWithEtat
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMissionWithLines
import kotlinx.coroutines.flow.Flow


@Dao
interface OrdreMissionDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(ordreMissions: List<OrdreMission>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOne(ordreMissions: OrdreMission)




    @Transaction
    @Query("SELECT * FROM ${ProCaisseConstants.ORDRE_MISSION_TABLE} " +
            "JOIN ${ProCaisseConstants.ORDRE_MISSION_LIGNE_TABLE} ON ${ProCaisseConstants.ORDRE_MISSION_TABLE}.ORD_Code = ${ProCaisseConstants.ORDRE_MISSION_LIGNE_TABLE}.LIGOR_Code ")
    fun getAll(): Flow<Map<OrdreMissionWithEtat, List<LigneOrdreMissionWithClient>>>

    @Query("select * from ORDRE_MISSION where ORD_Code=:code")
    fun getBycode(code: String): Flow<OrdreMission?>

    //@Query("select * from ORDRE_MISSION where ORD_date=:date")
       @Transaction
       @Query( "SELECT * FROM ${ProCaisseConstants.ORDRE_MISSION_TABLE} " +
               "LEFT JOIN ${ProCaisseConstants.ORDRE_MISSION_LIGNE_TABLE} ON ${ProCaisseConstants.ORDRE_MISSION_TABLE}.ORD_Code = ${ProCaisseConstants.ORDRE_MISSION_LIGNE_TABLE}.LIGOR_Code " +
               //"WHERE DATE(ORD_date) = DATE('now'); " //+
               "WHERE CASE WHEN :date ==  '' THEN DATE(ORD_date) = DATE('now') ELSE NULL END " //+
       )
    fun getBydate(date: String): Flow<Map<OrdreMissionWithEtat, List<LigneOrdreMissionWithClient>>?>



    @Query("UPDATE ${ProCaisseConstants.ORDRE_MISSION_TABLE} SET ORD_etat = :codeEtat  where ORD_Code = :oRDCode")
    fun updateOrdreMissionEtat(oRDCode: String, codeEtat: String)

    @Transaction
    @Query("select * from ORDRE_MISSION where ORD_Code= :code")
    fun getOrdreWithLines(code: String?): Flow<OrdreMissionWithLines?>

    @Query("Delete from ORDRE_MISSION")
    fun deleteAll()
}