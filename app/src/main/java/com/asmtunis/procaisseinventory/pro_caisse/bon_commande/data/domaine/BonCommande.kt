package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.Index
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


//@Entity(tableName = ProCaisseConstants.BON_COMMANDE_TABLE)
@Entity(tableName = ProCaisseConstants.BON_COMMANDE_TABLE, indices = [Index(value = ["DEV_Num", "DEV_Date", "DEV_CodeClient", "DEV_Client_Name"], unique = false)])
@Serializable
data class BonCommande  (
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,

   // @PrimaryKey
    @ColumnInfo(name = "DEV_Code_M")
    @SerialName("DEV_Code_M")
    var devCodeM: String ="",


    @ColumnInfo(name = "DEV_Num")
    @SerialName("DEV_Num")
    var dEVNum: String ="",

    @ColumnInfo(name = "DEV_Exerc")
    @SerialName("DEV_Exerc")
    var dEVExerc: String? ="",

    @ColumnInfo(name = "DEV_Date")
    @SerialName("DEV_Date")
    var dEVDate: String? ="",

    @ColumnInfo(name = "DEV_CodeClient")
    @SerialName("DEV_CodeClient")
    var dEVCodeClient: String? ="",

    @ColumnInfo(name = "DEV_StationOrigine")
    @SerialName("DEV_StationOrigine")
    var dEVStationOrigine: String? ="",

    @ColumnInfo(name = "DEV_Etat")
    @SerialName("DEV_Etat")
    var dEVEtat: String? ="",

    @ColumnInfo(name = "DEV_Station")
    @SerialName("DEV_Station")
    var dEVStation: String? ="",

    @ColumnInfo(name = "DEV_User")
    @SerialName("DEV_User")
    var dEVUser: String? ="",

    @ColumnInfo(name = "DEV_Mntht")
    @SerialName("DEV_Mntht")
    var dEVMntht: String? ="",

    @ColumnInfo(name = "DEV_MntNetHt")
    @SerialName("DEV_MntNetHt")
    var dEVMntNetHt: String? ="",

    @ColumnInfo(name = "DEV_MntTva")
    @SerialName("DEV_MntTva")
    var dEVMntTva: String? ="",

    @ColumnInfo(name = "DEV_MntTTC")
    @SerialName("DEV_MntTTC")
    var dEVMntTTC: String? ="",

    @ColumnInfo(name = "DEV_TauxRemise")
    @SerialName("DEV_TauxRemise")
    var dEVTauxRemise: String? ="",

    @ColumnInfo(name = "DEV_Remise")
    @SerialName("DEV_Remise")
    var dEVRemise: String? ="",

    @ColumnInfo(name = "DEV_Regler")
    @SerialName("DEV_Regler")
    var dEVRegler: String? ="",

    @ColumnInfo(name = "DEV_export")
    @SerialName("DEV_export")
    var dEVExport: String? ="",

    @ColumnInfo(name = "DEV_DDm")
    @SerialName("DEV_DDm")
    var dEVDDm: String? ="",

    @ColumnInfo(name = "DEV_ExoNum")
    @SerialName("DEV_ExoNum")
    var dEVExoNum: String? ="",

    @ColumnInfo(name = "DEV_ExoVal")
    @SerialName("DEV_ExoVal")
    var dEVExoVal: String? ="",

    @ColumnInfo(name = "DEV_Timbre")
    @SerialName("DEV_Timbre")
    var dEVTimbre: String? ="",

    @ColumnInfo(name = "DEV_Exonoration")
    @SerialName("DEV_Exonoration")
    var dEVExonoration: String? ="",

    @ColumnInfo(name = "DEV_Chauffeur")
    @SerialName("DEV_Chauffeur")
    var dEVChauffeur: String? ="",

    @ColumnInfo(name = "DEV_vehicule")
    @SerialName("DEV_vehicule")
    var dEVVehicule: String? ="",

    @ColumnInfo(name = "DEV_Observation")
    @SerialName("DEV_Observation")
    var dEVObservation: String? ="",

    @ColumnInfo(name = "DEV_Client")
    @SerialName("DEV_Client")
    var dEVClient: String? ="",

    @ColumnInfo(name = "DEV_Client_Name")
    @Transient
    var dEVClientName: String? ="",

    @ColumnInfo(name = "DEV_MntFodec")
    @SerialName("DEV_MntFodec")
    var dEVMntFodec: String? ="",

    @ColumnInfo(name = "DEV_MntDC")
    @SerialName("DEV_MntDC")
    var dEVMntDC: String? ="",

    @ColumnInfo(name = "DEV_EtatBon")
    @SerialName("DEV_EtatBon")
    var dEVEtatBon: String? ="",

    @ColumnInfo(name = "BON_LIV_Num")
    @SerialName("BON_LIV_Num")
    var bONLIVNum: String? ="",

    @ColumnInfo(name = "BON_LIV_Exerc")
    @SerialName("BON_LIV_Exerc")
    var bONLIVExerc: String? ="",


    @SerialName("code")
    @ColumnInfo(name = "code")
    @Transient
    var code: String? ="",

    @SerialName("msg")
    @ColumnInfo(name = "msg")
    @Transient
    var msg: String? ="",

  

    @ColumnInfo(name = "DEV_info3")
    @SerialName("DEV_info3")
    var dEV_info3: String? = null,


    @SerialName("DEV_TyMvtCode")
    @ColumnInfo(name = "DEV_TyMvtCode")
    val dEVTyMvtCode: String? = "",

    @SerialName("DEV_Code_SF")
    @ColumnInfo(name = "DEV_Code_SF")
    val devCodeSF: String? = "",

    @SerialName("DEV_Code_SO")
    @ColumnInfo(name = "DEV_Code_SO")
    val devCodeSO: String? = "",


): BaseModel() {
    @Ignore
    val dEVDDmFormatted = this.dEVDDm?.substringBefore(".")
}