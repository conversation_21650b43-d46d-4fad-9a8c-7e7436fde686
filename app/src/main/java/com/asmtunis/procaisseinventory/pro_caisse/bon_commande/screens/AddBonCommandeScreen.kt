package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.screens

import android.content.res.Configuration
import android.util.Log
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Note
import androidx.compose.material.icons.twotone.PersonOutline
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.authorizations.AuthorizationFunction
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.BonCommandeRoute
import com.asmtunis.procaisseinventory.core.navigation.SelectArticlesScreensCalculRoute
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.totalPriceTTC
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model.BonCommandeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.ui.PriceTableFooter
import com.asmtunis.procaisseinventory.pro_caisse.ui.SetArticleDialogue
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AddViewBottomAppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.session_management.SessionValidationWrapper
import com.asmtunis.procaisseinventory.shared_ui_components.session_management.canPerformActions
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils.infoText
import com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column.FiveColumnTable
import com.asmtunis.procaisseinventory.core.print.NoteInputDialog
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.dialogues.CustomAlertDialogue

@Composable
fun AddBonCommandeScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    clientId: String,
    settingViewModel: SettingViewModel,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    bonCommandeVM: BonCommandeViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel: MainViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel
) {
    val context = LocalContext.current

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val utilisateur = mainViewModel.utilisateur

    val codeM = mainViewModel.codeM
    val tvaList = mainViewModel.tvaList

    val currentStation = mainViewModel.stationList.firstOrNull { it.sTATCode == utilisateur.Station }
    val hasPromo = mainViewModel.hasPromo(currentStation)

    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList

    val haveFixedDiscountAuthorisation = proCaisseAuthorization.firstOrNull { it.AutoCodeAu == AuthorizationValuesProCaisse.FIXED_DISCOUNT }
    val haveControlQteAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.ART_ZERO_STOCK }

    val haveDiscountAuth = AuthorizationFunction.haveDiscountAuth(proCaisseAuthorization = proCaisseAuthorization)

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    selectArtMobilityVM.onHasPromoChange(hasPromo)

    val selectedArticle = selectArtMobilityVM.selectedArticle

    val selectedArtList = selectArtMobilityVM.selectedArticleList

    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val clientList = mainViewModel.clientList

    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode

    val isAutoScanMode = mainViewModel.isAutoScanMode
    val barCodeInfo = barCodeViewModel.barCodeInfo
    val showPriceDetail = selectArtMobilityVM.showPriceDetail
    val selectedBonCommandeWithClient = bonCommandeVM.selectedBonCommandeWithClient

    // Note dialog state
    var showNoteDialog by remember { mutableStateOf(false) }
    var noteText by remember { mutableStateOf("") }

    // Get enableNotes setting for conditional note button display
    val enableNotes = dataViewModel.printData.enableNotes

    // Debug logging to always check enableNotes value and note state

    // Set station-based price category when screen loads
    LaunchedEffect(key1 = currentStation?.typePrix) {
        selectArtMobilityVM.setStationBasedPriceCategory(currentStation)
    }

    LaunchedEffect(key1 = Unit) {
         selectArtMobilityVM.setControlQte(!haveControlQteAuthorisation)

    }
    LaunchedEffect(key1 = barCodeInfo) {
        if (barCodeInfo.value == "") return@LaunchedEffect

        selectArtMobilityVM.handleBareCodeResult(
            errorMessage = context.resources.getString(R.string.article_introvable, ""),
            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) },
            barCodeInfo = barCodeInfo,
            isAutoScanMode = isAutoScanMode,
            articleMapByBarCode = articleMapByBarCode,
            useSalePrice = true,
            tvaList = tvaList,
            showToast = { message, type ->
                showToast(
                    context = context,
                    toaster = toaster,
                    message = message,
                    type =  type,
                )

            }
        )
    }


    val totalDiscountError = selectArtMobilityVM.totalDiscountError
    val totalDiscount = selectArtMobilityVM.totalDiscount

    LaunchedEffect(key1 = totalDiscount) {
        if(haveFixedDiscountAuthorisation?.AutValues == null) return@LaunchedEffect
        val autValues = haveFixedDiscountAuthorisation.AutValues
        selectArtMobilityVM.onTotalDiscountErrorChange(
            autValues = autValues,
            errorMsg = "Remise maximale $autValues  %" //context.resources.getString(R.string.max_discount, autValues)

        )

    }

    val totPriceTTCWithDicount = totalPriceTTC(selectedArtList)
    val totPriceWithoutDicount = selectArtMobilityVM.totPriceWithoutDicount
    val totalPriceAfterDiscountChange = selectArtMobilityVM.totalPriceAfterDiscountChange



    LaunchedEffect(key1 = selectedArtList.size) {
        if (selectedArtList.isEmpty()) {
            selectArtMobilityVM.onTotalDiscountChange(value = "")
            selectArtMobilityVM.onTotalPriceAfterDiscountChange(value = "")
            return@LaunchedEffect
        }
        selectArtMobilityVM.setTotalPrices()
    }


/*
    BackHandler(enabled = true) {
        //todo repace passing client by view model when migrate to the new jetpack compose version 2.8.0 now in alpha
        mainViewModel.onSelectedClientChange(Client())
        popBackStack()
    }*/

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }
    Scaffold(
        topBar = {
            AppBar(
                onNavigationClick = {
                //    mainViewModel.onSelectedClientChange(Client())
                    mainViewModel.onShowDismissScreenAlertDialogChange(true)
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = if (selectedBonCommandeWithClient.bonCommande?.devCodeM != null) selectedBonCommandeWithClient.bonCommande?.devCodeM?.ifEmpty { codeM }?: "" else codeM,
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected
            )

        },
        bottomBar = {
            Column {
                PriceTableFooter(
                    listIsEmpty = selectedArtList.isEmpty(),
                    totalDiscountError = totalDiscountError,
                    totalDiscountChange = totalDiscount,
                    totalPriceWithDiscount = totalPriceAfterDiscountChange.toString(),
                    totalPriceWithoutDiscountTTC = totPriceWithoutDicount.toString(),
                    haveDiscountAuth = haveDiscountAuth,
                    isVisible = showPriceDetail && haveDiscountAuth && selectedArtList.isNotEmpty(),
                    noteText = noteText,
                    onTotalDiscountChange = {
                        selectArtMobilityVM.onTotalDiscountChange(it)
                        selectArtMobilityVM.changeTotPriceAfterDiscount()
                        selectArtMobilityVM.updateDiscountInEveryLine()
                    },
                    onTotalPriceWithDiscountChange = {
                        selectArtMobilityVM.onTotalPriceAfterDiscountChange(value = it)
                        selectArtMobilityVM.setTotalDiscount(totalPriceAfterDiscount = stringToDouble(it))
                        selectArtMobilityVM.updateDiscountInEveryLine()
                    },
                    onExpandClick = { selectArtMobilityVM.onShowPriceDetailChange(!showPriceDetail) }
                )

                // Debug note button visibility

                AddViewBottomAppBar(
                    haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                    toaster = toaster,
                    showSaveBtn = selectedArtList.isNotEmpty() && totalDiscountError == null && canPerformActions(navigationDrawerViewModel.sessionCaisse),
                    isAutoScanMode = isAutoScanMode,
                    showBareCodeScannerBtn = barCodeViewModel.haveCameraDevice,
                    showNoteBtn = enableNotes,
                    onSaveClick = {
                        //contentDescription = stringResource(id = R.string.cd_bon_retour_button)
                        bonCommandeVM.saveNewBonCommande(
                            bonCommandeCodeM = if (selectedBonCommandeWithClient.bonCommande != null) selectedBonCommandeWithClient.bonCommande!!.devCodeM.ifEmpty { codeM }
                            else codeM,
                            selectedClient = clientByCode,
                            totalDiscount = totalDiscount,
                            totalPriceAfterDiscountChange = totalPriceAfterDiscountChange.toString(),
                            utilisateur = utilisateur,
                            totPriceWithoutDicount = totPriceWithoutDicount,
                            proCaisseAuthorization = proCaisseAuthorization,
                            selectedArticleList = selectedArtList,
                            noteText = noteText,
                            enableNotes = enableNotes
                        )
                        bonCommandeVM.restBonCommande()

                        NavigationDrawerViewModel.proCaisseDrawerItems.find { it.id == ID.BON_COMMANDE_ID }?.let { navigationDrawerViewModel.onSelectedMenuChange(it) }


                        navigate(BonCommandeRoute())
                    },
                    onClickAddArticle = {
                        mainViewModel.setAddNewProductDialogueVisibility(false)

                        selectArtMobilityVM.onShowTvaChange(false)
                        navigate(SelectArticlesScreensCalculRoute)
                    },
                    setAutoScanMode = {
                        mainViewModel.setAutoAddMode(!isAutoScanMode)
                    },
                    openBareCodeScanner = {
                        openBareCodeScanner(
                            navigate = { navigate(it) },
                            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                        )
                    },
                    onNoteClick = {
                        showNoteDialog = true
                    }
                )
            }


        }
    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                popBackStack()
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non),

        )

        CustomAlertDialogue(
            title = context.getString(R.string.delete_confirmation_msg),
            msg = context.getString(R.string.confirm_remove_ligne_data),
            openDialog = selectArtMobilityVM.showDeleteConfirmationDialog,
            setDialogueVisibility = {
                selectArtMobilityVM.onShowDeleteConfirmationDialogChange(it)
            },
            customAction = {
                selectArtMobilityVM.confirmDeleteArticle()

                // Also delete from bon commande if it exists
                if (selectedBonCommandeWithClient.bonCommande?.devCodeM?.isNotEmpty() == true) {
                    selectArtMobilityVM.articleToDelete?.let { article ->
                        bonCommandeVM.deleteBcLgbyCodeMAndArtCode(
                            codeM = selectedBonCommandeWithClient.bonCommande?.devCodeM ?: "",
                            artCode = article.aRTCode
                        )
                    }
                }
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non),
            negatifAction = {
                selectArtMobilityVM.cancelDeleteArticle()
            }
        )
        if (selectArtMobilityVM.showSetArticle) {
            SetArticleDialogue(
                toaster = toaster,
                onConfirm = {
                    if (stringToDouble(selectedArticle.quantity) <= 0.0) {

                        selectArtMobilityVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article)

                        if (selectedBonCommandeWithClient.bonCommande?.devCodeM?.isNotEmpty() == true)
                            bonCommandeVM.deleteBcLgbyCodeMAndArtCode(
                                codeM = selectedBonCommandeWithClient.bonCommande?.devCodeM?: "",
                                artCode = selectedArticle.article.aRTCode
                            )
                        return@SetArticleDialogue
                    }
                    selectArtMobilityVM.setTotalPrices()
                },
                onReset = {
                    selectArtMobilityVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article)

                },
                hasPromo = hasPromo,
                proCaisseAuthorization = proCaisseAuthorization,
                setShowPriceCategoryChange = {
                    selectArtMobilityVM.setShowPriceCategoryChange(it)
                },
                onShowSetArticleChange = {
                    selectArtMobilityVM.onShowSetArticleChange(it)
                },
                setShowPriceCategorySingleArticleChange = {
                    selectArtMobilityVM.setShowPriceCategorySingleArticleChange(it)
                },
                getSingleArticlePrice = {
                    selectArtMobilityVM.getPrice(it.article)
                },
                onSelectedPriceCategorieChange = {
                    selectArtMobilityVM.onSelectedPriceCategoryChange(it)
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //   context = context,
                        operation = Globals.NO_OPERATION,
                        quantiti = selectedArticle.quantity,
                        useSalePrice = true
                    )
                },
                selectedArticle = selectedArticle,
                priceCategoryList = selectArtMobilityVM.priceCategoryList,
                updateQty = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        quantiti = it,
                        typeOperation = "qty",
                        useSalePrice = true
                    )
                },
                onRemiseChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        remise = it,
                        typeOperation = "remise",
                        useSalePrice = true
                    )
                },
                onPrixCaisseChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        // barCodeViewModel = barCodeViewModel,
                        typeOperation = "prixCaisse",
                        prixCaisse = it,
                        useSalePrice = true
                    )
                },
                onPrixTotalChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        typeOperation = "totalPrice",
                        prixtotal = it,
                        useSalePrice = true
                    )
                },
                showPriceCategorySingleArticle = selectArtMobilityVM.showPriceCategorySingleArticle
            )

        }

        // Note input dialog
        if (showNoteDialog) {
            NoteInputDialog(
                onDismiss = {
                    showNoteDialog = false
                },
                onConfirm = { note ->
                    noteText = note
                    showNoteDialog = false
                }
            )
        }

        // Session validation wrapper

        SessionValidationWrapper(
            currentSession = navigationDrawerViewModel.sessionCaisse
        ) {
            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {

            /*  TableHeader(
                  onClickShowCalendar = {
                  },
                  date = getCurrentDateTime(),
                  canModify = true,
                  selectedDateTime = mainViewModel.getSelectedDateTime(),
                  showAddDate = false
              )*/

            ItemDetail(
                modifier = Modifier.fillMaxWidth(0.85f),
                title = stringResource(id = R.string.client_field_title),
                dataText = selectedBonCommandeWithClient.client?.cLINomPren
                    ?: selectedBonCommandeWithClient.bonCommande?.dEVClientName
                    ?: clientByCode.cLINomPren,
                icon = Icons.TwoTone.PersonOutline,
                onClick = {

                }
            )
            Spacer(modifier = Modifier.height(9.dp))

            // Note display field (show note if exists)
            if (noteText.isNotEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth(0.85f)
                        .padding(vertical = 4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Note,
                            contentDescription = "Note",
                            tint = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = noteText,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
            }
            FiveColumnTable(
                selectedListArticle = selectedArtList,
                canModify = true,
                onPress = {
                    selectArtMobilityVM.setSelectedArticl(article = it.article, tvaList = tvaList)

                },
                onLongPress = {
                    //   selectArtMobilityVM.setSelectedArticl(it.article)
                    //  mainViewModel.onShowAlertDialogChange(true)
                },
                onSwipeToDelete = {
                    selectArtMobilityVM.requestDeleteArticle(it.article)
                },
                onTap = {
                    selectArtMobilityVM.onShowSetArticleChange(true)
                },

                firstColumn = { item ->
                    articleMapByBarCode[item.article.aRTCode]?.aRTDesignation
                        ?: context.resources.getString(
                            R.string.article_introvable,
                            " (" + item.article.aRTCode + ")"
                        )
                },
                secondColumn = {
                    convertStringToPriceFormat(it.prixCaisse)
                },
                thirdColumn = {
                    it.quantity
                },
                forthColumn = {
                    convertStringToPriceFormat(
                        CalculationsUtils.totalPriceArticle(
                            price = it.prixCaisse,
                            quantity = it.quantity
                        )
                    )
                },
                infoText = {
                    infoText(selectedArticle = it)
                }
            )

            }
        }
    }
}




