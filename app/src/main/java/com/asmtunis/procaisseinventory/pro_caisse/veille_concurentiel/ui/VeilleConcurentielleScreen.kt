package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.ui

import NavDrawer
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Add
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AutreDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.NewProductDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.PrixDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.PromotionDetailRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.getTabRowItems
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcTextValidationViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.simapps.ui_kit.CustomScrollableTabRow
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun VeilleConcurentielleScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    cameraViewModel: CameraViewModel,
    navDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    textValidationViewModel: VcTextValidationViewModel = hiltViewModel(),
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    mainViewModel: MainViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    proCaisseViewModels: ProCaisseViewModels
    ) {
    val vcViewModel = proCaisseViewModels.veilleConcurentielViewModel
    val newProdViewModel = proCaisseViewModels.newProdViewModel
    val promotionViewModel = proCaisseViewModels.promotionViewModel
    val prixViewModel = proCaisseViewModels.prixViewModel
    val autreViewModel = proCaisseViewModels.autreViewModel
     val syncVcViewModel = syncProcaisseViewModels.syncVcViewModel
    val uiWindowState = settingViewModel.uiWindowState

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)

    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    val isConnected = networkViewModel.isConnected
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val autreListState = rememberLazyListState()
    val newProductListState = rememberLazyListState()
    val prixListState = rememberLazyListState()
    val promotionListState = rememberLazyListState()




    val newProdState = newProdViewModel.newProductFilterListstate
    val listOrderNewProduct = newProdState.listOrder
    val listFilterNewProduct = newProdState.search
    val filterListNewProduct = context.resources.getStringArray(R.array.new_product_vc_search)

    val promoState = promotionViewModel.promotionVCListstate
    val listOrderPromo = promoState.listOrder
    val listFilterPromo = promoState.search
    val filterListPromo = context.resources.getStringArray(R.array.promo_vc_search)


    val autreState = autreViewModel.autreListstate
    val listOrderAutre = autreState.listOrder
    val listFilterAutre = autreState.search
    val filterListAutre = context.resources.getStringArray(R.array.autre_vc_search)


    val prixState = prixViewModel.prixVCListstate
    val listOrderPrix = prixState.listOrder
    val listFilterPrix = prixState.search
    val filterListPrix = context.resources.getStringArray(R.array.promo_vc_search)

    val concurentList = mainViewModel.listConcurentVC


    val newProductList = newProdState.lists
    val promotionList = promoState.lists
    val autreList = autreState.lists
    val prixList = prixState.lists


    val tabRowItems = getTabRowItems(
        navigate = { navigate(it) },
        popBackStack = { popBackStack() },
        mainViewModel = mainViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        cameraViewModel = cameraViewModel,
        proCaisseViewModels = proCaisseViewModels,
        isConnected = isConnected,
        selectedBaseconfig = selectedBaseconfig,
        newProductList = newProductList,
        promotionList = promotionList,
        autreList = autreList,
        prixList = prixList,
        autreListState = autreListState,
        newProductListState = newProductListState,
        prixListState = prixListState,
        promotionListState = promotionListState,
    )

    val pagerState = rememberPagerState(
        initialPage = 0,
        initialPageOffsetFraction = 0f
    ) {
        // provide pageCount
        tabRowItems.size
    }


    val currentPage = pagerState.currentPage

    val floatingButtonIsVisible = when (currentPage) {
        0 ->  floatingBtnIsVisible(
            listeSize = newProductList.size,
            canScrollForward = newProductListState.canScrollForward
        )
        1 -> floatingBtnIsVisible(
            listeSize = promotionList.size,
            canScrollForward = promotionListState.canScrollForward
        )
        2 -> floatingBtnIsVisible(
            listeSize = prixList.size,
            canScrollForward = prixListState.canScrollForward
        )
        3 -> floatingBtnIsVisible(
            listeSize = autreList.size,
            canScrollForward = autreListState.canScrollForward
        )
        else -> {
           false
        }
    }




    LaunchedEffect(
        key1 = autreViewModel.autreSearchTextState.text,
        key2 = autreState.lists,
        key3 = autreState.search
    ) {
        autreViewModel.filterAutreVC(autreState)
    }


    LaunchedEffect(
        key1 = newProdViewModel.newProductSearchTextState.text,
        key2 = newProdState.lists,
        key3 = newProdState.search
    ) {
        newProdViewModel.filterNewProductVC(newProdState)
    }



    LaunchedEffect(
        key1 = prixViewModel.prixSearchTextState.text,
        key2 = prixState.lists,
        key3 = prixState.search
    ) {
        prixViewModel.filterPrixVC(prixState)
    }



    LaunchedEffect(
        key1 = promotionViewModel.promotionSearchTextState.text,
        key2 = promoState.lists,
        key3 = promoState.search
    ) {
        promotionViewModel.filterPromoVC(promoState)
    }
    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = {
                        // Navigate to shortcuts screen instead of opening drawer
                        navigate(com.asmtunis.procaisseinventory.core.navigation.HomePageRoute)
                    },
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,                    title = stringResource(id = navDrawerViewModel.proCaisseSelectedMenu.title),
                    titleVisibilty = !vcViewModel.showSearchView,
                    actions = {
                        SearchSectionComposable(
                            label = context.getString(R.string.filter_by,
                                when (currentPage) {
                                    0 -> when (listFilterNewProduct) {
                                        is ListSearch.FirstSearch -> filterListNewProduct[0]
                                        is ListSearch.SecondSearch -> filterListNewProduct[1]
                                        else -> filterListNewProduct[2]}
                                    1 -> when (listFilterPromo) {
                                        is ListSearch.FirstSearch -> filterListPromo[0]
                                        is ListSearch.SecondSearch -> filterListPromo[1]
                                        else -> filterListPromo[2]}
                                    2 -> when (listFilterPrix) {
                                        is ListSearch.FirstSearch -> filterListPrix[0]
                                        is ListSearch.SecondSearch -> filterListPrix[1]
                                        else -> filterListPrix[2]}
                                    3 -> when (listFilterAutre) {
                                        is ListSearch.FirstSearch -> filterListAutre[0]
                                        is ListSearch.SecondSearch -> filterListAutre[1]
                                        else -> filterListAutre[2]}

                                    else -> {
                                        context.getString(R.string.search_hint)
                                    }
                                }
                                ),
                            searchVisibility  = vcViewModel.showSearchView,
                            searchTextState =  when (currentPage) {
                                0 -> newProdViewModel.newProductSearchTextState
                                1 -> promotionViewModel.promotionSearchTextState
                                2 -> prixViewModel.prixSearchTextState
                                3 -> autreViewModel.autreSearchTextState
                                else -> {
                                    newProdViewModel.newProductSearchTextState
                                }
                            },
                            onSearchValueChange = {
                                when (currentPage) {
                                    0 -> newProdViewModel.onNewProductSearchValueChange(TextFieldValue(it))
                                    1 -> promotionViewModel.onPromotionSearchValueChange(TextFieldValue(it))
                                    2 -> prixViewModel.onPrixSearchValueChange(TextFieldValue(it))
                                    3 -> autreViewModel.onAutreSearchValueChange(TextFieldValue(it))
                                }
                            },
                            onShowSearchViewChange = {
                                vcViewModel.onShowSearchViewChange(it)
                            },
                            onShowCustomFilterChange = {
                                vcViewModel.onShowCustomFilterChange(it)
                            }
                        )


                    },
                )
            },
            floatingActionButton = {
                val density = LocalDensity.current

                AnimatedVisibility(
                    // modifier = modifier,
                    visible = floatingButtonIsVisible,
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {
                    FloatingActionButton(
                        onClick = {
                            textValidationViewModel.resetVariable()
                            cameraViewModel.clearListImgeUri()
                            when (currentPage) {
                                0 -> {
                                    newProdViewModel.onselectedNewProdChange(NewProductVCWithImages())
                                    newProdViewModel.onModifyChange(true)
                                    navigate(NewProductDetailRoute)
                                }

                                1 -> {
                                promotionViewModel.onselectedPromotionChange(PromoVCWithImages())
                                promotionViewModel.onModifyChange(true)
                                navigate(PromotionDetailRoute)

                            }

                                2 -> {
                                prixViewModel.onselectedPrixChange(PrixVCWithImages())
                                prixViewModel.onModifyChange(true)
                                navigate(PrixDetailRoute)
                            }

                                3 -> {
                                autreViewModel.onselectedAutreChange(AutreVCWithImages())
                                autreViewModel.onModifyChange(true)
                                navigate(AutreDetailRoute)
                            }

                            }

                        }
                    ) {
                        Icon(
                            imageVector = Icons.TwoTone.Add,
                            contentDescription = stringResource(id = R.string.add_vc_button)
                        )
                    }
                }
            }
        ) { padding ->
            if (vcViewModel.showCustomModalBottomSheet) {
                CustomModalBottomSheet(
                    showPrintIcon = false,
                    remoteResponseState = when (currentPage) {
                        0 ->     syncVcViewModel.newProductState
                        1 ->   syncVcViewModel.promoState
                        2 ->     syncVcViewModel.prixState
                        3 ->  syncVcViewModel.autreState

                        else -> {syncVcViewModel.autreState}
                    },
                    title =   when (currentPage) {
                        0 ->     newProdViewModel.selectedNewProduct.newProductVC!!.codeVCLanP
                        1 ->   promotionViewModel.selectedPromotionWithImages.promoVC!!.codeVCPromo
                        2 ->     prixViewModel.selectedPrixVcWithImages.prixVC!!.codeVCPrix
                        3 ->  autreViewModel.selectedAutre.autreVC!!.codeAutre
                        else -> {""}
                    },
                    onDismissRequest= {
                        vcViewModel.onShowCustomModalBottomSheetChange(false)
                        when (currentPage) {
                            0 -> newProdViewModel.onselectedNewProdChange(NewProductVCWithImages())
                            1 -> promotionViewModel.onselectedPromotionChange(PromoVCWithImages())
                            2 -> prixViewModel.onselectedPrixChange(PrixVCWithImages())
                            3 -> autreViewModel.onselectedAutreChange(AutreVCWithImages())
                        }
                    },
                    onDeleteRequest= {
                        when (currentPage) {
                            0 ->    (newProdViewModel::setDeletedNewProduct)(newProdViewModel.selectedNewProduct.newProductVC!!)
                            1 ->  (promotionViewModel::setDeletedPromotion)(promotionViewModel.selectedPromotionWithImages.promoVC!!)
                            2 ->    (prixViewModel::setDeletedPrix)(prixViewModel.selectedPrixVcWithImages.prixVC!!)
                            3 -> (autreViewModel::setDeletedAutre)(autreViewModel.selectedAutre.autreVC!!)
                        }
                    },
                    onPrintRequest= {},
                    status =   when (currentPage) {
                        0 ->     newProdViewModel.selectedNewProduct.newProductVC!!.status
                        1 ->   promotionViewModel.selectedPromotionWithImages.promoVC!!.status
                        2 ->     prixViewModel.selectedPrixVcWithImages.prixVC!!.status
                        3 ->  autreViewModel.selectedAutre.autreVC!!.status
                        else -> {""}
                    },
                    onSyncRequest = {
                        when (currentPage) {
                            0 -> syncVcViewModel.syncNewProductsVc(listOf(newProdViewModel.selectedNewProduct.newProductVC!!))
                            1 -> syncVcViewModel.syncPromoVc(listOf(promotionViewModel.selectedPromotionWithImages.promoVC!!))
                            2 -> syncVcViewModel.syncPrixVc(listOf(prixViewModel.selectedPrixVcWithImages.prixVC!!))
                            3 -> syncVcViewModel.syncAutreVc(listOf(autreViewModel.selectedAutre.autreVC!!))
                        }
                    }
                )
            }

            if (vcViewModel.showCustomFilter) {
                VcFilterContainer(
                    vcViewModel = vcViewModel,
                    newProdViewModel = newProdViewModel,
                    promotionViewModel = promotionViewModel,
                    prixViewModel = prixViewModel,
                    autreViewModel = autreViewModel,
                    currentPage = currentPage,
                    concurentList = concurentList,

                    filterListNewProduct = filterListNewProduct,
                    listFilterNewProduct = listFilterNewProduct,
                    listOrderNewProduct = listOrderNewProduct,

                    filterListPromo = filterListPromo,
                    listFilterPromo = listFilterPromo,
                    listOrderPromo = listOrderPromo,

                    filterListPrix = filterListPrix,
                    listFilterPrix = listFilterPrix,
                    listOrderPrix = listOrderPrix,

                    filterListAutre = filterListAutre,
                    listFilterAutre = listFilterAutre,
                    listOrderAutre = listOrderAutre,
                )

            }


           Column(
                modifier = Modifier
                    .padding(padding)
                    .padding(top = 12.dp)
            ) {
                CustomScrollableTabRow(
                    pagerState = pagerState,
                    tabs = { sizeList, noInteraction->
                        tabRowItems.forEachIndexed { index, recipe ->
                            Tab(
                                selected = index == pagerState.currentPage,
                                onClick = {
                                    scope.launch {
                                        pagerState.animateScrollToPage(index)
                                    }
                                },
                                modifier = Modifier.onSizeChanged {
                                        sizeList[index] = Pair(it.width.toFloat(), it.height.toFloat())
                                    },
                                interactionSource = remember { noInteraction }
                            ) {
                                Text(
                                    text = recipe.title.uppercase(),
                                    style = TextStyle(
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 12.sp,
                                    ),
                                    modifier = Modifier
                                        .align(Alignment.CenterHorizontally)
                                        .padding(horizontal = 32.dp, vertical = 16.dp)
                                )
                            }
                        }
                    },
                    pagerContent = {
                        HorizontalPager(
                            state = pagerState,
                            beyondViewportPageCount = 10,
                        ) { page ->
                            tabRowItems[page].screen()
                        }
                    }
                )

            }
        }
    }
}



