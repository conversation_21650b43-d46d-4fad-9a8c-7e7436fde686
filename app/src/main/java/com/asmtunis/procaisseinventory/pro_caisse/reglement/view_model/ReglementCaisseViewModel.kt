package com.asmtunis.procaisseinventory.pro_caisse.reglement.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import android.util.Log
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.pro_caisse.reglement.filter.ReglementCaisseFilterListState
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject



    @OptIn(SavedStateHandleSaveableApi::class)
    @HiltViewModel
    class ReglementCaisseViewModel @Inject constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val proCaisseRemote: ProCaisseRemote,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        savedStateHandle : SavedStateHandle
    ) : ViewModel() {

        // Store current user for station filtering
        private var currentUtilisateur: Utilisateur? = null

        fun setCurrentUtilisateur(utilisateur: Utilisateur) {
            currentUtilisateur = utilisateur
        }

        fun filterReglementCaisse(reglementCaisseListState: ReglementCaisseFilterListState, utilisateur: Utilisateur) {
            setCurrentUtilisateur(utilisateur)
            filterReglementCaisse(reglementCaisseListState)
        }

        var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
            private set
        fun onShowCustomModalBottomSheetChange(value: Boolean) {
            showCustomModalBottomSheet = value
        }
        var showReglementDetail  by savedStateHandle.saveable { mutableStateOf(false) }
            private set
        fun onShowReglementDetailChange(value: Boolean) {
            showReglementDetail = value
        }


        var selectedReglementCaisse  by  mutableStateOf(ReglementCaisseWithTicketAndClient())
            private set
        fun onSelectedReglementCaisseChange(value: ReglementCaisseWithTicketAndClient) {
            selectedReglementCaisse = value
        }

        var showCustomFilter  by mutableStateOf(false)
            private set
        fun onShowCustomFilterChange(value: Boolean) {
            showCustomFilter = value
        }

        var showSearchView: Boolean by mutableStateOf(false)
            private set
        fun onShowSearchViewChange(value: Boolean) {
            showSearchView = value
        }



        var searchTextState by mutableStateOf(TextFieldValue(""))
            private set
        //var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
          //  private set
        fun onSearchValueChange(value: TextFieldValue) {
            searchTextState = value
        }

        var reglementCaisseFilterListState by  mutableStateOf(ReglementCaisseFilterListState())
            private set
       // var reglementCaisseFilterListState: ReglementCaisseFilterListState by mutableStateOf(ReglementCaisseFilterListState())
           // private set
        fun onEventReglementCaisse(event: ListEvent) {
            when (event) {
                is ListEvent.Order -> {
                    if (reglementCaisseFilterListState.listOrder::class == event.listOrder::class &&
                        reglementCaisseFilterListState.listOrder.orderType == event.listOrder.orderType
                    ) {
                        return
                    }
                    reglementCaisseFilterListState = reglementCaisseFilterListState.copy(
                        listOrder = event.listOrder
                    )
                    filterReglementCaisse(reglementCaisseFilterListState)
                }
                is ListEvent.Delete -> TODO()
                is ListEvent.Restore -> TODO()



                is ListEvent.ListSearch -> {
                    reglementCaisseFilterListState = reglementCaisseFilterListState.copy(
                        search = event.listSearch
                    )

                    filterReglementCaisse(reglementCaisseFilterListState)
                }

                is ListEvent.FirstCustomFilter -> {
                    reglementCaisseFilterListState = reglementCaisseFilterListState.copy(
                        filterByTypePayment = event.firstFilter
                    )

                    filterReglementCaisse(reglementCaisseFilterListState)
                }

                is ListEvent.SecondCustomFilter -> {
                    reglementCaisseFilterListState = reglementCaisseFilterListState.copy(
                        filterByTypeReglement = event.secondFiter
                    )

                    filterReglementCaisse(reglementCaisseFilterListState)
                }

                is ListEvent.ThirdCustomFilter -> TODO()
            }

        }
        var getReglementCaisseJob: Job = Job()
        fun filterReglementCaisse(reglementCaisseListState: ReglementCaisseFilterListState) {
            val utilisateur = currentUtilisateur
            if (utilisateur == null) {
                return
            }

            val searchedText = searchTextState.text
            val searchValue = reglementCaisseListState.search
            val filterByTypePayment = reglementCaisseListState.filterByTypePayment
            val filterByTypeReglement = reglementCaisseListState.filterByTypeReglement
            val station = utilisateur.Station

            getReglementCaisseJob.cancel()

            if (searchedText.isEmpty()) {
                getReglementCaisseJob = when (reglementCaisseListState.listOrder.orderType) {
                    is OrderType.Ascending -> {
                        when (reglementCaisseListState.listOrder) {
                            is ListOrder.Title -> viewModelScope.launch {
                                proCaisseLocalDb.reglementCaisse.getAllFiltred(
                                    isAsc = 1,
                                    sortBy = "REGC_Code",
                                    filterByTypePayment = filterByTypePayment,
                                    filterByTypeReglement = filterByTypeReglement,
                                    station = station
                                ).collect {
                                    setReglementCaisseList(listReglement = it)
                                }
                            }

                            is ListOrder.Date -> viewModelScope.launch {
                                proCaisseLocalDb.reglementCaisse.getAllFiltred(
                                    isAsc = 1,
                                    sortBy = "REGC_DateReg",
                                    filterByTypePayment = filterByTypePayment,
                                    filterByTypeReglement = filterByTypeReglement,
                                    station = station)
                                    .collect {
                                    setReglementCaisseList(listReglement = it)
                                }
                            }

                            is ListOrder.Third -> viewModelScope.launch {
                                proCaisseLocalDb.reglementCaisse.getAllFiltred(
                                    isAsc = 1,
                                    sortBy = "REGC_Montant",
                                    filterByTypePayment = filterByTypePayment,
                                    filterByTypeReglement = filterByTypeReglement,
                                    station = station
                                ).collect {
                                    setReglementCaisseList(listReglement = it)
                                }
                            }
                        }
                    }

                    is OrderType.Descending -> {
                        when (reglementCaisseListState.listOrder) {
                            is ListOrder.Title -> viewModelScope.launch {
                                proCaisseLocalDb.reglementCaisse.getAllFiltred(
                                    isAsc = 2,
                                    sortBy = "REGC_Code",
                                    filterByTypePayment = filterByTypePayment,
                                    filterByTypeReglement = filterByTypeReglement,
                                    station = station).collect {
                                    setReglementCaisseList(listReglement = it)
                                }
                            }

                            is ListOrder.Date -> viewModelScope.launch {
                                proCaisseLocalDb.reglementCaisse.getAllFiltred(
                                    isAsc = 2,
                                    sortBy = "REGC_DateReg",
                                    filterByTypePayment = filterByTypePayment,
                                    filterByTypeReglement = filterByTypeReglement,
                                    station = station
                                ).collect {
                                    setReglementCaisseList(listReglement = it)
                                }
                            }

                            is ListOrder.Third -> viewModelScope.launch {
                                proCaisseLocalDb.reglementCaisse.getAllFiltred(
                                    isAsc = 2,
                                    sortBy = "REGC_Montant",
                                    filterByTypePayment = filterByTypePayment,
                                    filterByTypeReglement = filterByTypeReglement,
                                    station = station
                                ).collect {
                                    setReglementCaisseList(listReglement = it)
                                }
                            }
                        }
                    }
                }
            } else {
                if (searchedText.isNotEmpty()) {
                    if (searchValue is ListSearch.FirstSearch) {
                        getReglementCaisseJob = when (reglementCaisseListState.listOrder.orderType) {
                            is OrderType.Ascending -> {
                                when (reglementCaisseListState.listOrder) {
                                    is ListOrder.Title -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegCode(
                                            searchString = searchedText,
                                            sortBy = "REGC_Code",
                                            isAsc = 1,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Date -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegCode(
                                            searchString = searchedText,
                                            sortBy = "REGC_DateReg",
                                            isAsc = 1,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Third -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegCode(
                                            searchString = searchedText,
                                            sortBy = "REGC_Montant",
                                            isAsc = 1,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }
                                }
                            }

                            is OrderType.Descending -> {
                                when (reglementCaisseListState.listOrder) {
                                    is ListOrder.Title -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegCode(
                                            searchString = searchedText,
                                            sortBy = "REGC_Code",
                                            isAsc = 2,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Date -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegCode(
                                            searchString = searchedText,
                                            sortBy = "REGC_DateReg",
                                            isAsc = 2,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Third -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegCode(
                                            searchString = searchedText,
                                            sortBy = "REGC_Montant",
                                            isAsc = 2,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (searchValue is ListSearch.SecondSearch) {
                        getReglementCaisseJob =  when (reglementCaisseListState.listOrder.orderType) {
                            is OrderType.Ascending -> {
                                when (reglementCaisseListState.listOrder) {
                                    is ListOrder.Title -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByClient(
                                            searchString = searchedText,
                                            sortBy = "REGC_Code",
                                            isAsc = 1,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Date -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByClient(
                                            searchString = searchedText,
                                            sortBy = "REGC_DateReg",
                                            isAsc = 1,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Third -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByClient(
                                            searchString = searchedText,
                                            sortBy = "REGC_Montant",
                                            isAsc = 1,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }
                                }
                            }

                            is OrderType.Descending -> {
                                when (reglementCaisseListState.listOrder) {
                                    is ListOrder.Title -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByClient(
                                            searchString = searchedText,
                                            sortBy = "REGC_Code",
                                            isAsc = 2,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Date -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByClient(
                                            searchString = searchedText,
                                            sortBy = "REGC_DateReg",
                                            isAsc = 2,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station)
                                            .collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Third -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByClient(
                                            searchString = searchedText,
                                            sortBy = "REGC_Montant",
                                            isAsc = 2,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (searchValue is ListSearch.ThirdSearch) {
                        getReglementCaisseJob =  when (reglementCaisseListState.listOrder.orderType) {
                            is OrderType.Ascending -> {
                                when (reglementCaisseListState.listOrder) {
                                    is ListOrder.Title -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegNumTicket(
                                            searchString = searchedText,
                                            sortBy = "REGC_Code",
                                            isAsc = 1,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Date -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegNumTicket(
                                            searchString = searchedText,
                                            sortBy = "REGC_DateReg",
                                            isAsc = 1,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Third -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegNumTicket(
                                            searchString = searchedText,
                                            sortBy = "REGC_Montant",
                                            isAsc = 1,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }
                                }
                            }

                            is OrderType.Descending -> {
                                when (reglementCaisseListState.listOrder) {
                                    is ListOrder.Title -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegNumTicket(
                                            searchString = searchedText,
                                            sortBy = "REGC_Code",
                                            isAsc = 2,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Date -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegNumTicket(
                                            searchString = searchedText,
                                            sortBy = "REGC_DateReg",
                                            isAsc = 2,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }

                                    is ListOrder.Third -> viewModelScope.launch {
                                        proCaisseLocalDb.reglementCaisse.filterByRegNumTicket(
                                            searchString = searchedText,
                                            sortBy = "REGC_Montant",
                                            isAsc = 2,
                                            filterByTypePayment = filterByTypePayment,
                                            filterByTypeReglement = filterByTypeReglement,
                                            station = station
                                        ).collect {
                                            setReglementCaisseList(listReglement = it)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }




        private fun setReglementCaisseList(listReglement:  List<ReglementCaisseWithTicketAndClient>){

            reglementCaisseFilterListState = reglementCaisseFilterListState.copy(
                lists =  emptyList() // ,
                // listOrder = articlesListState.listOrder
            )

            reglementCaisseFilterListState = reglementCaisseFilterListState.copy(
                lists = listReglement // ,
                // listOrder = articlesListState.listOrder
            )

         //   getReglementCaisseJob.cancel()
        }


        var listeReglementLibreByClient: MutableList<ReglementCaisse> = mutableStateListOf()
            private set

        var listeReglementTiketByClient: MutableList<ReglementCaisse> = mutableStateListOf()
            private set
        fun getReglementListByClient(codeClt : String){
            viewModelScope.launch {
                proCaisseLocalDb.reglementCaisse.getReglementByClient(codeClt = codeClt)
                    .collectLatest {listReglement->
                        listeReglementLibreByClient.clear()
                        listeReglementTiketByClient.clear()
                        listeReglementLibreByClient.addAll(listReglement.filter { it.rEGCRemarque == "Regler Acpt" && it.rEGCNumTicket== "0" })
                        listeReglementTiketByClient.addAll(listReglement.filter { it.rEGCRemarque == "vente du jour" && it.rEGCNumTicket!= "0" })

                    }
            }
        }
     
    }
