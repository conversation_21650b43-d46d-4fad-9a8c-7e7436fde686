package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.dao

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object BonCommandeDAOModule {

    @Provides
    @Singleton
    fun provideBonCommandeDAOWrapper(
        bonCommandeDAO: BonCommandeDAO
    ): BonCommandeDAOWrapper {
        return BonCommandeDAOWrapper(bonCommandeDAO)
    }
}
