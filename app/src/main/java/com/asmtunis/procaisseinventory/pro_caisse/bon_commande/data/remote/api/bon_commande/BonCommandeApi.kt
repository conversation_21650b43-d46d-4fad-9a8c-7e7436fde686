package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.bon_commande

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.InvPatBatchResponse
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.PaginationResponseBonCommande
import kotlinx.coroutines.flow.Flow
import kotlinx.serialization.json.JsonElement


interface BonCommandeApi {
    suspend fun getBonCommandes(baseConfig: String): Flow<DataResult<List<BonCommande>>>
    suspend fun getBonCommandesPagination(
        baseConfig: String,
        page: String,
        limit: String
    ): Flow<DataResult<PaginationResponseBonCommande?>>

    /**
     * Get bon commandes with nested lignes_devis data for direct extraction
     */
    suspend fun getBonCommandesPaginationWithLignes(
        baseConfig: String,
        page: String,
        limit: String
    ): Flow<DataResult<JsonElement?>>

    suspend fun addBatchBonCommande(baseConfig: String): Flow<DataResult<List<InvPatBatchResponse>>>
}