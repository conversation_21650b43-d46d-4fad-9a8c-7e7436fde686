package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.screens

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.PersonOutline
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.authorizations.AuthorizationFunction
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.BonRetourRoute
import com.asmtunis.procaisseinventory.core.navigation.SelectArticlesScreensCalculRoute
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model.BonRetourViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.ui.PriceTableFooter
import com.asmtunis.procaisseinventory.pro_caisse.ui.SetArticleDialogue
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AddViewBottomAppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column.FiveColumnTable
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.dialogues.CustomAlertDialogue

@Composable
fun AddBonRetour(
    navigate: (route: Any) -> Unit,
    navigateUp: () -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    bonRetourViewModel: BonRetourViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel: MainViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    settingViewModel: SettingViewModel
) {
    val utilisateur = mainViewModel.utilisateur

    val currentStation = mainViewModel.stationList.firstOrNull{ it.sTATCode == utilisateur.Station}
    val hasPromo = mainViewModel.hasPromo(currentStation)
    val context = LocalContext.current
    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList


    val clientList = mainViewModel.clientList
    val tvaList = mainViewModel.tvaList
    val clientByCode = clientList.firstOrNull{ it.cLICode == clientId }?: Client()//mainViewModel.clientByCode
    val haveFixedDiscountAuthorisation = proCaisseAuthorization.firstOrNull { it.AutoCodeAu == AuthorizationValuesProCaisse.FIXED_DISCOUNT}

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val codeM = mainViewModel.codeM

    val selectedArticleMobilityList = selectArtMobilityVM.selectedArticleList

    val selectedArtList = selectArtMobilityVM.selectedArticleList

    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val barCodeInfo = barCodeViewModel.barCodeInfo
    val isAutoScanMode = mainViewModel.isAutoScanMode
    val totalDiscountError = selectArtMobilityVM.totalDiscountError
    val totalDiscount = selectArtMobilityVM.totalDiscount


    val totPriceTTCWithDicount = CalculationsUtils.totalPriceTTC(selectedArticleMobilityList)
    val totPriceWithoutDicount = selectArtMobilityVM.totPriceWithoutDicount
    val totalPriceAfterDiscountChange = selectArtMobilityVM.totalPriceAfterDiscountChange
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    // Set station-based price category when screen loads
    LaunchedEffect(key1 = currentStation?.typePrix) {
        selectArtMobilityVM.setStationBasedPriceCategory(currentStation)
    }

    LaunchedEffect(key1 = totalDiscount) {

        if(haveFixedDiscountAuthorisation?.AutValues == null) return@LaunchedEffect

        val autValues = haveFixedDiscountAuthorisation.AutValues
        selectArtMobilityVM.onTotalDiscountErrorChange(
            autValues = autValues,
            errorMsg = "Remise maximale $autValues  %" //context.resources.getString(R.string.max_discount, autValues)

        )

    }
    val showPriceDetail = selectArtMobilityVM.showPriceDetail
    val selectedArticle = selectArtMobilityVM.selectedArticle
    val currentSelectdArt = selectArtMobilityVM.getCurrentSelectdArt(article = selectedArticle.article, tvaList = tvaList)




    LaunchedEffect(key1 = Unit) {
        selectArtMobilityVM.setControlQte(false)
    }


    LaunchedEffect(key1 = barCodeInfo) {
        if (barCodeInfo.value == "") return@LaunchedEffect


        selectArtMobilityVM.handleBareCodeResult(
            errorMessage = context.resources.getString(R.string.article_introvable, ""),
            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) },
            barCodeInfo = barCodeInfo,
            tvaList = tvaList,
            isAutoScanMode = isAutoScanMode,
            articleMapByBarCode = articleMapByBarCode,
            useSalePrice = true,
            showToast = { message, type ->
                showToast(
                    context = context,
                    toaster = toaster,
                    message = message,
                    type =  type,
                )

            }
        )
    }

    LaunchedEffect(key1 = selectedArticleMobilityList.size) {
        if(selectedArticleMobilityList.isEmpty()){
            selectArtMobilityVM.onTotalPriceAfterDiscountChange(value = "")
            return@LaunchedEffect
        }
        selectArtMobilityVM.setTotalPrices()


    }
    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { mainViewModel.onShowDismissScreenAlertDialogChange(true) },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM
            )

        },
        bottomBar = {

            Column {
                PriceTableFooter(
                    listIsEmpty = selectedArtList.isEmpty(),
                    totalDiscountError = totalDiscountError,
                    totalDiscountChange = totalDiscount,
                    totalPriceWithoutDiscountTTC =  totPriceWithoutDicount.toString(),
                    haveDiscountAuth = AuthorizationFunction.haveDiscountAuth(proCaisseAuthorization = proCaisseAuthorization),
                    isVisible = showPriceDetail && AuthorizationFunction.haveDiscountAuth(proCaisseAuthorization = proCaisseAuthorization) && selectedArticleMobilityList.isNotEmpty(),
                    totalPriceWithDiscount = totalPriceAfterDiscountChange.toString(),
                    noteText = "",
                    onTotalDiscountChange = {
                        selectArtMobilityVM.onTotalDiscountChange(it)
                        selectArtMobilityVM.changeTotPriceAfterDiscount()
                        selectArtMobilityVM.updateDiscountInEveryLine()

                    },
                    onTotalPriceWithDiscountChange = {
                        selectArtMobilityVM.onTotalPriceAfterDiscountChange(value = it)
                        selectArtMobilityVM.setTotalDiscount(totalPriceAfterDiscount = stringToDouble(it))
                        selectArtMobilityVM.updateDiscountInEveryLine()
                    },

                    onExpandClick = {
                        selectArtMobilityVM.onShowPriceDetailChange(!showPriceDetail)
                    }
                )

                AddViewBottomAppBar(
                    haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                    toaster = toaster,
                    showSaveBtn = selectedArticleMobilityList.isNotEmpty() && totalDiscountError == null,
                    isAutoScanMode = isAutoScanMode,
                    showBareCodeScannerBtn = barCodeViewModel.haveCameraDevice,
                    onSaveClick = {
                        //contentDescription = stringResource(id = R.string.cd_bon_retour_button)
                        bonRetourViewModel.saveNewBonRetour(
                            clientByCode = clientByCode,
                            codeM = codeM,
                            utilisateur = utilisateur,
                            exerciceList = mainViewModel.exerciceList,
                            listStationStockArticl = mainViewModel.stationStockArticlMapByBarCode,
                            totPriceWithoutDicount = totPriceWithoutDicount,
                            selectedArticleList = selectedArticleMobilityList,
                            updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                                mainViewModel.updateQtePerStation(newQteStation = newQteStation, newSartQteDeclare = newSartQteDeclare, codeArticle = codeArticle, codeStation = codeStation)
                            },
                            updateArtQteStock = { newQteAllStations,newQteStation,codeArticle ->
                                mainViewModel.updateArtQteStock(
                                    newQteAllStations = newQteAllStations,
                                    newQteStation=newQteStation,
                                    codeArticle=codeArticle
                                )
                            }
                        )

                        bonRetourViewModel.restBonRetour()

                        mainViewModel.resetClientByCode()
                        NavigationDrawerViewModel.proCaisseDrawerItems.find { it.id == ID.BON_RETOUR_ID }?.let { navigationDrawerViewModel.onSelectedMenuChange(it) }


                        navigate(BonRetourRoute())
                    },
                    onClickAddArticle = {
                        selectArtMobilityVM.onShowSetArticleChange(false)

                        selectArtMobilityVM.onShowTvaChange(false)
                        /*selectArtMobilityVM.setSelectedArticlMobility(
                            selectArtMobilityVM.selectedArticle.copy(
                                controlQuantity = false
                            )
                        )*/
                        navigate(SelectArticlesScreensCalculRoute)
                    },
                    setAutoScanMode = {
                        mainViewModel.setAutoAddMode(!isAutoScanMode)
                    },
                    openBareCodeScanner = {
                        openBareCodeScanner(
                            navigate = { navigate(it) },
                            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it)}
                        )
                    }
                )
            }




        }
    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                popBackStack()
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non),

        )

        CustomAlertDialogue(
            title = context.getString(R.string.delete_confirmation_msg),
            msg = context.getString(R.string.confirm_remove_ligne_data),
            openDialog = selectArtMobilityVM.showDeleteConfirmationDialog,
            setDialogueVisibility = {
                selectArtMobilityVM.onShowDeleteConfirmationDialogChange(it)
            },
            customAction = {
                selectArtMobilityVM.confirmDeleteArticle()
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non),
            negatifAction = {
                selectArtMobilityVM.cancelDeleteArticle()
            }
        )
        if (selectArtMobilityVM.showSetArticle) {
            SetArticleDialogue(
                toaster = toaster,
                showPriceCategorySingleArticle = selectArtMobilityVM.showPriceCategorySingleArticle,
                selectedArticle = currentSelectdArt,
                priceCategoryList = selectArtMobilityVM.priceCategoryList,
                hasPromo = hasPromo,
                proCaisseAuthorization = proCaisseAuthorization,
                onConfirm = {
                    if(stringToDouble(selectedArticle.quantity)<=0.0 || stringToDouble(selectedArticle.lTMtBrutHT) <= 0.0){

                        selectArtMobilityVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article)

                        // if(bonCommandeVM.selectedBonCommandeWithClient.bonCommande?.devCodeM?.isNotEmpty() == true)
                        //  bonCommandeVM.deletebyCodeMAndArtCode(codeM = bonCommandeVM.selectedBonCommandeWithClient.bonCommande!!.devCodeM, artCode = selectedArticle.article.aRTCode)
                        return@SetArticleDialogue
                    }
                    selectArtMobilityVM.setTotalPrices()
                },
                onReset = { selectArtMobilityVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article) },
                setShowPriceCategoryChange = { selectArtMobilityVM.setShowPriceCategoryChange(it) },
                onShowSetArticleChange = { selectArtMobilityVM.onShowSetArticleChange(it) },
                setShowPriceCategorySingleArticleChange = { selectArtMobilityVM.setShowPriceCategorySingleArticleChange(it) },
                getSingleArticlePrice = { selectArtMobilityVM.getPrice(it.article) },
                onSelectedPriceCategorieChange = {
                    selectArtMobilityVM.onSelectedPriceCategoryChange(it)
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //   context = context,
                        operation = Globals.NO_OPERATION,
                        quantiti = currentSelectdArt.quantity,
                        useSalePrice = true
                    )
                },
                updateQty= {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        quantiti = it,
                        typeOperation = "qty",
                        useSalePrice = true
                    )
                },
                onRemiseChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        remise = it,
                        typeOperation = "remise",
                        useSalePrice = true
                    )
                },
                onPrixCaisseChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        // barCodeViewModel = barCodeViewModel,
                        typeOperation = "prixCaisse",
                        prixCaisse = it,
                        useSalePrice = true
                    )
                },
                onPrixTotalChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        typeOperation = "totalPrice",
                        prixtotal = it,
                        useSalePrice = true
                    )
                }
            )
        }



        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {



          /*  TableHeader(
                onClickShowCalendar = {
                },
                date = getCurrentDateTime(),
                canModify = true,
                selectedDateTime = mainViewModel.getSelectedDateTime(),
                showAddDate = false
            )*/

            ItemDetail(
                modifier = Modifier.fillMaxWidth(0.85f),
                title = stringResource(id = R.string.client_field_title),
                dataText = clientByCode.cLINomPren,
                icon = Icons.TwoTone.PersonOutline,
            )
            Spacer(modifier = Modifier.height(9.dp))
            FiveColumnTable(
                selectedListArticle = selectedArticleMobilityList,
                canModify = true,
                onPress = {
                    selectArtMobilityVM.setSelectedArticl(article = it.article, tvaList = tvaList)
                },
                onLongPress = {
                    selectArtMobilityVM.setSelectedArticl(article = it.article, tvaList = tvaList)
                    mainViewModel.onShowAlertDialogChange(true)
                },
                onSwipeToDelete = {
                    selectArtMobilityVM.requestDeleteArticle(it.article)
                },
                onTap = {
                    selectArtMobilityVM.onShowSetArticleChange(true)
                },
                firstColumn = { item->
                    articleMapByBarCode[item.article.aRTCode]?.aRTDesignation?: context.resources.getString(R.string.article_introvable, " ("+ item.article.aRTCode+")")
                } ,
                secondColumn = { convertStringToPriceFormat(it.prixCaisse) },
                thirdColumn = { it.quantity },
                forthColumn = {
                    convertStringToPriceFormat(
                        CalculationsUtils.totalPriceArticle(
                            price = it.prixCaisse,
                            quantity = it.quantity
                        )
                    )
                },
                infoText = {
                    TableTextUtils.infoText(selectedArticle = it)
                }
            )
        }
    }
}



