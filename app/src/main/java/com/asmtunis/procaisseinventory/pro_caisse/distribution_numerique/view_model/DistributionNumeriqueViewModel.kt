package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.VisiteListState
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation.AddVisiteFormState
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation.ValidationAddVisiteEvent
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.EXERCICE_KEY
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.dokar.sonner.ToastType
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@OptIn(SavedStateHandleSaveableApi::class)
@HiltViewModel
class DistributionNumeriqueViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val dataStoreRepository: DataStoreRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    /**
     * Get exercice from DataStore with fallback to exerciceList
     * This prevents crashes when exerciceList is empty
     */
    private fun getExerciceCode(exerciceList: List<Exercice>): String {
        return exerciceList.firstOrNull()?.exerciceCode ?: runBlocking {
            dataStoreRepository.getString(EXERCICE_KEY, default = "2024").first() ?: "2024"
        }
    }

    init {
        // Initialize data loading with proper error handling
        initializeData()
    }

    private fun initializeData() {
        try {
            getsuperficieList()
            gettypePtVenteList()
            gettypeServiceList()
        } catch (e: Exception) {
            // Log the error but don't crash the app
            android.util.Log.e("DistributionNumeriqueViewModel", "Error initializing data", e)
        }
    }




    var showAlertDialog by savedStateHandle.saveable { mutableStateOf(false) }
        private set
    fun onShowAlertDialogChange(value: Boolean) {
        showAlertDialog = value
    }




    var showCustomFilter  by mutableStateOf(false)
        private set
    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }
    var showCustomModalBottomSheet  by mutableStateOf(false)
        private set
    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }





    var modifyVisite: Boolean by mutableStateOf(false)
        private set
    fun onModifyVisiteChange(value: Boolean) {
        modifyVisite = value
    }

    var selectedVisiteWithLinesMap: Map<VisitesDn, List<LigneVisitesDn>> by mutableStateOf(emptyMap())
        private set


    var selectedVisiteLines = mutableStateListOf(LigneVisitesDn())
        private set
    var selectedVisite: VisitesDn by mutableStateOf(VisitesDn())
        private set
    fun onSelectedVisiteChange(value: Map<VisitesDn, List<LigneVisitesDn>>) {
        selectedVisiteWithLinesMap = emptyMap()
        selectedVisiteLines.clear()
        selectedVisite = VisitesDn()

        if (value.isEmpty()) return

        selectedVisiteWithLinesMap = value
        selectedVisite = value.firstNotNullOfOrNull { it.key }?: VisitesDn()
        selectedVisiteLines.addAll(value.firstNotNullOfOrNull { it.value }?: emptyList())


    }




    fun onDeleteLigneVisitesDnChange(value: LigneVisitesDn) {
        selectedVisiteLines.remove(value)
    }


    var selectedLigneVisiteDn: LigneVisitesDn by mutableStateOf(LigneVisitesDn())
        private set
    fun onSelectedLigneVisiteDnChange(value: LigneVisitesDn) {
        selectedLigneVisiteDn = value
    }

    var typePtVenteExpanded: Boolean by mutableStateOf(false)
        private set
    fun onTypePtVenteExpandedChange(value: Boolean) {
        typePtVenteExpanded = value
    }


    var familleProduitExpanded: Boolean by mutableStateOf(false)
        private set
    fun onFamilleProduitExpandedChange(value: Boolean) {
        familleProduitExpanded = value
    }

    var concurrentVCExpanded: Boolean by mutableStateOf(false)
        private set
    fun onConcurrentVCExpandedChange(value: Boolean) {
        concurrentVCExpanded = value
    }

    var typeServiceExpanded: Boolean by mutableStateOf(false)
        private set
    fun ontypeServiceExpandedChange(value: Boolean) {
        typeServiceExpanded = value
    }

    var superficieExpanded: Boolean by mutableStateOf(false)
        private set
    fun onSuperficieExpandedChange(value: Boolean) {
        superficieExpanded = value
    }

    var showSearchView: Boolean by mutableStateOf(false)
        private set
    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }


    var superficieList: List<SuperficieDn> by mutableStateOf(emptyList())
        private set
    private fun getsuperficieList() {
        viewModelScope.launch {
            try {
                proCaisseLocalDb.superficieDn.getAll().collect { result ->
                    // Ensure we handle null values and empty lists properly
                    superficieList = result ?: emptyList()
                }
            } catch (e: Exception) {
                android.util.Log.e("DistributionNumeriqueViewModel", "Error loading superficie list", e)
                superficieList = emptyList()
            }
        }
    }

    var typeServiceList: List<TypeServicesDn> by mutableStateOf(emptyList())
        private set
    private fun gettypeServiceList() {
        viewModelScope.launch {
            try {
                proCaisseLocalDb.typeServicesDn.getAll().collect { result ->
                    // Ensure we handle null values and empty lists properly
                    typeServiceList = result ?: emptyList()
                }
            } catch (e: Exception) {
                android.util.Log.e("DistributionNumeriqueViewModel", "Error loading type service list", e)
                typeServiceList = emptyList()
            }
        }
    }







    var typePtVenteList: List<TypePointVenteDn> by mutableStateOf(emptyList())
        private set
    private fun gettypePtVenteList() {
        viewModelScope.launch {
            try {
                proCaisseLocalDb.typePointVenteDn.getAll().collect { result ->
                    // Ensure we handle null values and empty lists properly
                    typePtVenteList = result ?: emptyList()
                }
            } catch (e: Exception) {
                android.util.Log.e("DistributionNumeriqueViewModel", "Error loading type point vente list", e)
                typePtVenteList = emptyList()
            }
        }
    }

    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }

    private var recentlyDeletedVisiste: VisitesDn? = null

    var state: VisiteListState by mutableStateOf(VisiteListState())
        private set





    fun onEvent(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (state.listOrder::class == event.listOrder::class &&
                    state.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                state = state.copy(
                    listOrder = event.listOrder
                )
                filterVisites(state)
            }
            is ListEvent.Delete -> {
                viewModelScope.launch(dispatcher) {
                    val visiteToDelete = event.mainTable as VisitesDn
                    visiteToDelete.isSync = false
                    visiteToDelete.status = ItemStatus.DELETED.status
                    proCaisseLocalDb.visitesDn.upsertVisite(visiteToDelete)
                    //  local.clientsUseCases.deleteClient(event.note)
                    recentlyDeletedVisiste = event.mainTable
                }
            }
            is ListEvent.Restore -> {
                viewModelScope.launch(dispatcher) {
                    (recentlyDeletedVisiste ?: return@launch).isSync = true
                    (recentlyDeletedVisiste ?: return@launch).status = ItemStatus.SELECTED.status

                    proCaisseLocalDb.visitesDn.upsertVisite((recentlyDeletedVisiste ?: return@launch))
                    recentlyDeletedVisiste = null
                }
            }


            is ListEvent.ListSearch -> {
                state = state.copy(
                    filter = event.listSearch
                )

                filterVisites(state)
            }

            is ListEvent.FirstCustomFilter -> {
                state = state.copy(
                    codeClient = event.firstFilter
                )

                filterVisites(state)
            }

            is ListEvent.SecondCustomFilter -> TODO()
            is ListEvent.ThirdCustomFilter -> TODO()
        }
    }


    fun filterVisites(visiteListState: VisiteListState) {
        val searchedText = searchTextState.text
        val listFilter = visiteListState.filter
        val codeClt = visiteListState.codeClient



        if (searchedText.isEmpty()) {
             when (visiteListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (visiteListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.visitesDn.getAllFiltred(isAsc = 1, sortBy = "VIS_Num", codeClt).collect {
                                setVisiteList(it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.visitesDn.getAllFiltred(isAsc = 1, sortBy = "VIS_DDM", codeClt).collect {
                                setVisiteList(it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.visitesDn.getAllFiltred(isAsc = 1, sortBy = "VIS_NomGerant", codeClt).collect {
                                setVisiteList(it)
                            }
                        }
                    }
                }

                is OrderType.Descending -> {
                    when (visiteListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.visitesDn.getAllFiltred(isAsc = 2, sortBy = "VIS_Num", codeClt).collect {
                                setVisiteList(it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.visitesDn.getAllFiltred(isAsc = 2, sortBy = "VIS_DDM", codeClt).collect {
                                setVisiteList(it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.visitesDn.getAllFiltred(isAsc = 2, sortBy = "VIS_NomGerant", codeClt).collect {
                                setVisiteList(it)
                            }
                        }
                    }
                }
            }
        } else {
            if (searchedText.isNotEmpty()) {
                if (listFilter is ListSearch.FirstSearch) {
                      when (visiteListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (visiteListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNumVisite(searchedText, sortBy = "VIS_Num", isAsc = 1, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNumVisite(searchedText, sortBy = "VIS_DDM", isAsc = 1, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNumVisite(searchedText, sortBy = "VIS_NomGerant", isAsc = 1, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (visiteListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNumVisite(searchedText, sortBy = "VIS_Num", isAsc = 2, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNumVisite(searchedText, sortBy = "VIS_DDM", isAsc = 2, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNumVisite(searchedText, sortBy = "VIS_NomGerant", isAsc = 2, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (listFilter is ListSearch.SecondSearch) {
                    when (visiteListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (visiteListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNomGerant(searchedText, sortBy = "VIS_Num", isAsc = 1, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNomGerant(searchedText, sortBy = "VIS_DDM", isAsc = 1, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNomGerant(searchedText, sortBy = "VIS_NomGerant", isAsc = 1, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (visiteListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNomGerant(searchedText, sortBy = "VIS_Num", isAsc = 2, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNomGerant(searchedText, sortBy = "VIS_DDM", isAsc = 2, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByNomGerant(searchedText, sortBy = "VIS_NomGerant", isAsc = 2, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (listFilter is ListSearch.ThirdSearch) {
                       when (visiteListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (visiteListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByProspect(searchedText, sortBy = "VIS_Num", isAsc = 1, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByProspect(searchedText, sortBy = "VIS_DDM", isAsc = 1, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByProspect(searchedText, sortBy = "VIS_NomGerant", isAsc = 1, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (visiteListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByProspect(searchedText, sortBy = "VIS_Num", isAsc = 2, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByProspect(searchedText, sortBy = "VIS_DDM", isAsc = 2, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.visitesDn.filterByProspect(searchedText, sortBy = "VIS_NomGerant", isAsc = 2, codeClt).collect {
                                        setVisiteList(it)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    private fun saveVisite(visitesDn: VisitesDn, listLigneVisitesDn: List<LigneVisitesDn>) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.visitesDn.deleteVisiteByVisNum(visitesDn.vIS_Num, visitesDn.vIS_Exerc)
            proCaisseLocalDb.visitesDn.upsertVisite(visitesDn)

            proCaisseLocalDb.visitesDn.deleteLigneVisiteByVisNum(visitesDn.vIS_Num, visitesDn.vIS_Exerc)
            proCaisseLocalDb.visitesDn.upsertLigneVisiteAll(listLigneVisitesDn)
        }
    }

    var selectClientVisit: Client by mutableStateOf(Client())
        private set

    fun getClientByCode(codeClt: String) {
        viewModelScope.launch(dispatcher) {
            try {
                proCaisseLocalDb.clients.getOneByCode(code = codeClt).collectLatest {
                    if(it==null) return@collectLatest

                    selectClientVisit = it


                    //  if (Globals.BASE_URL != "") syncClients(it)
                }
            } catch (e: Exception) {
                android.util.Log.e("DistributionNumeriqueViewModel", "Error loading client by code", e)
                selectClientVisit = Client()
            }
        }
    }


    private fun setVisiteList(visitAndLigneList:  Map<VisitesDn, List<LigneVisitesDn>>){
        state = state.copy(
            lists = emptyMap()
        )

        state = state.copy(
            lists = visitAndLigneList
        )


       // getVisiteJob.cancel()
    }
    fun setIsDeletedVisite(visite: VisitesDn) {
        viewModelScope.launch(dispatcher) {
            if (visite.vIS_Num == visite.vIS_Code_M){ // if not sync and not added to back end db then delet it immitiatly
                proCaisseLocalDb.visitesDn.deleteVisite(visite)
            }
            else {
                proCaisseLocalDb.visitesDn.setIsDeletedVisite(visite.vIS_Num, visite.vIS_Exerc)
                filterVisites(state)
            }
        }
    }


    fun restDeletedVisite(visite: VisitesDn) {
        viewModelScope.launch(dispatcher) {

            val status :String
            val isSync :Boolean
          /*  if(visite.vIS_Num == visite.vIS_Code_M){
                status = ItemStatus.INSERTED.status
                isSync = false
            }
            else {*/
                status = ItemStatus.SELECTED.status
                isSync = true

           // }

            proCaisseLocalDb.visitesDn.restDeletedVisite(
                code = visite.vIS_Num,
                exercice = visite.vIS_Exerc,
                status = status,
                isSync = isSync
            )






            filterVisites(state)
        }
    }


    fun deleteLigneVisiteByCode(ligneVisitesDn: LigneVisitesDn) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.visitesDn.deleteLigneVisiteByVisNum(ligneVisitesDn.lgVISNum, ligneVisitesDn.lgVISExerc)


        }
    }




    fun handleAddVisiteEvents(
        codeM: String,
        validationAddVisiteEvents: ValidationAddVisiteEvent,
        popBackStack: () -> Unit,
        utilisateur: Utilisateur,
        visitesDn: VisitesDn,
        exerciceList: List<Exercice>,
        client: Client
    ) {

                when (validationAddVisiteEvents) {
                    is ValidationAddVisiteEvent.AddVisite -> {
                        // TODO
                        /**
                         * TO DO
                         * if (App.database.prefixeDAO().getOneById("client") == null) {
                        Toasty.warning(context, getString(R.string.there_is_no_client_prefix_try_update_data_base)).show();
                        return;
                        }
                         */
                        // TODO
                        /**
                         * get coordinate * get adresse
                         */
                        val typeUtilisateur = utilisateur.typeUser
                        val userID = utilisateur.codeUt


                        val visite = VisitesDn(
                            id = if(visitesDn.id!=0L) visitesDn.id else 0,

                            vIS_Code_M = codeM,
                            vIS_Num = visitesDn.vIS_Num.ifEmpty { codeM },
                            vIS_Exerc = exerciceList.firstOrNull()?.exerciceCode ?: "2024",
                            vIS_Date = getCurrentDateTime(),
                            vIS_CodeClient = if (visitesDn.vIS_Num.isNotEmpty()) visitesDn.vIS_CodeClient else  client.cLICode,
                            vIS_NomClient = client.cLINomPren,
                            vIS_User = userID,
                            vIS_NomMagazin = validationAddVisiteEvents.addVisite.nomMagasin,
                            vIS_Gouvernorat = validationAddVisiteEvents.addVisite.gouvernorat,
                            vIS_Delegations = validationAddVisiteEvents.addVisite.delegation,
                            vIS_Adresse = validationAddVisiteEvents.addVisite.adresse,
                            vIS_NomGerant = validationAddVisiteEvents.addVisite.nomGerant,
                            vIS_NumTele = validationAddVisiteEvents.addVisite.phone1,
                            vIS_TypePV = validationAddVisiteEvents.addVisite.typePtVente.codeTypePV,
                            vIS_TypeServ = validationAddVisiteEvents.addVisite.typeService.codeTypeSv,
                            vIS_Superf = validationAddVisiteEvents.addVisite.superficie.codeSuperf,
                            vIS_DDM = getCurrentDateTime(),
                            vIS_Info1 = "",
                            vIS_Latitude = validationAddVisiteEvents.addVisite.latitude,
                            vIS_Longitude = validationAddVisiteEvents.addVisite.longitude,
                            vIS_Info2 = "",
                            vIS_Info3 = ""
                        )
                        visite.isSync = false
                        visite.status = ItemStatus.INSERTED.status


                        saveVisite(visite, selectedVisiteLines)

                        popBackStack()
                    }
                }


    }
    
    
    
    
    fun addFamilleItem(
        codeM: String,
        stateAddVisite: AddVisiteFormState,
        exerciceList: List<Exercice>,
        showToast: (message: Int, type: ToastType) -> Unit,
        onFinish: () -> Unit
    ) {
        if (stateAddVisite.familleProduit.codeFamille.isEmpty() ||
            stateAddVisite.fournisseur.codeconcurrent.isEmpty()
        ) {
            showToast(R.string.select_famille_fournisseur, ToastType.Error)
            return
        }
        

        for (ligneVisite in selectedVisiteLines) {
            if (ligneVisite.lgVISFamille == stateAddVisite.familleProduit.codeFamille &&
                ligneVisite.lgVISTier == stateAddVisite.fournisseur.codeconcurrent
            ) {
                showToast(R.string.ligne_visite_existe, ToastType.Warning)
                return
            }
        }
        val ligneVisite = LigneVisitesDn(
            lgVISFamille = stateAddVisite.familleProduit.codeFamille,
            lgVISTier = stateAddVisite.fournisseur.codeconcurrent,
            lgVISDDM = getCurrentDateTime(),
            lgVISExerc = getExerciceCode(exerciceList),
            lgVISInfo1 = stateAddVisite.remarque,
            lgVISInfo2 = "",
            lgVISInfo3 = "",
            lgVISNum = selectedVisite.vIS_Num.ifEmpty { codeM }
        )

        ligneVisite.isSync = false
        ligneVisite.status = ItemStatus.INSERTED.status

        selectedVisiteLines.add(ligneVisite)

        onFinish()

     }
}
