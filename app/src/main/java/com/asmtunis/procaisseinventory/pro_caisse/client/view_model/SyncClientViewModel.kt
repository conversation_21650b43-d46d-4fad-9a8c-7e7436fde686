package com.asmtunis.procaisseinventory.pro_caisse.client.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

@HiltViewModel
class SyncClientViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val dataStoreRepository: DataStoreRepository,
    private val listenNetwork: ListenNetwork
    // app: Application
) : ViewModel() {//: AndroidViewModel(app) {
var clientsState: RemoteResponseState<List<Client>> by mutableStateOf(RemoteResponseState())
    private set

    private var autoSyncState  by mutableStateOf(false)

    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()

    private var  isConnect  by mutableStateOf(false)


    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()


    var clientsNotSync: List<Client> by mutableStateOf(emptyList())
        private set

    var notSyncClientObj : String by mutableStateOf("")
        private set


    init {
        getNotSyncClient()
    }

    private fun getNotSyncClient() {
        val clientFlow = proCaisseLocalDb.clients.getNotSync().distinctUntilChanged()



        viewModelScope.launch {
            combine(networkFlow, clientFlow, autoSyncFlow) { isConnected, clientList, autoSync ->
                isConnect = isConnected
                autoSyncState = autoSync
                if (!clientList.isNullOrEmpty()) clientList else emptyList()
            }.collect {
                if (it.isEmpty()) {
                    clientsNotSync = emptyList()
                    return@collect
                }
                clientsNotSync = it
                if(isConnect && autoSyncState) syncClients(clientsNotSync)
            }
        }
    }
    fun syncClients(notSyncClient: List<Client>) {
        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(notSyncClient)
            )
            notSyncClientObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.clients.addClients(notSyncClientObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        //  viewModelScope.launch(dispatcherIO) {
                        for (client in result.data!!) {
                            proCaisseLocalDb.clients.updateSyncClient(client.cLICodeM!!, client.cLICode)
                        }
                        //  }
                        clientsState = RemoteResponseState(data = result.data!!, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        clientsState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        clientsState = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }




}










/*
fun main() {
    val example = Example(mapOf("b" to 2, "c" to 3, "a" to 1))
    println(Json.encodeToString(
        example
    ))
}*/
