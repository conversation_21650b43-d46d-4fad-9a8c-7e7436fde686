package com.asmtunis.procaisseinventory.pro_caisse.ui

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowDown
import androidx.compose.material.icons.rounded.KeyboardArrowUp
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToDoubleFormat
import com.simapps.ui_kit.edit_text.EditTextField

@Composable
fun PriceTableFooter(
    isVisible: Boolean,
    canEdit: Boolean = true,
    listIsEmpty: Boolean,
    haveDiscountAuth: Boolean,
    onExpandClick: () -> Unit,
    totalDiscountChange: String,
    totalDiscountError: String?,
    onTotalDiscountChange: (String) -> Unit,
    totalPriceWithDiscount: String,
    onTotalPriceWithDiscountChange: (String) -> Unit,
    totalPriceWithoutDiscountTTC: String,
    noteText: String = ""
) {


    val context = LocalContext.current
    Spacer(modifier = Modifier.height(6.dp))

    Column(
        modifier = Modifier
            .clip(RoundedCornerShape(topStart = 30.dp, topEnd = 30.dp))
            .background(MaterialTheme.colorScheme.inverseOnSurface)
            .animateContentSize(),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Row(
            modifier = Modifier
                .padding(16.dp)
                .animateContentSize()
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {

            if (haveDiscountAuth) {
                Box(modifier = Modifier
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.secondary)
                    .clickable {
                        onExpandClick()
                    }
                ) {
                    Icon(
                        modifier = Modifier.size(25.dp),
                        imageVector = if (isVisible) {
                            Icons.Rounded.KeyboardArrowUp
                        } else {
                            Icons.Rounded.KeyboardArrowDown
                        },
                        contentDescription = "Currencies",
                        tint = MaterialTheme.colorScheme.onSecondary
                    )
                }
            }


            Spacer(modifier = Modifier.width(20.dp))

            EditTextField(
                modifier = Modifier.fillMaxWidth(),
            // text = convertStringToDoubleFormat(input = totalPriceWithDiscount),
                text = totalPriceWithDiscount,
                errorValue = null,
                label = if(haveDiscountAuth) context.getString(R.string.total_price_with_discount_field_title) else context.getString(R.string.total),
                onValueChange = {
                    onTotalPriceWithDiscountChange(it)
                },
                readOnly = !canEdit,
                enabled = haveDiscountAuth && !listIsEmpty,
                showTrailingIcon = false,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Done
            )

        }

        // Note display field
        if (noteText.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                EditTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = noteText,
                    errorValue = null,
                    label = context.getString(R.string.note_field),
                    onValueChange = { },
                    readOnly = true,
                    enabled = false,
                    showTrailingIcon = false,
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Done
                )
            }
        }

        Spacer(
            modifier = Modifier
                .height(1.dp)
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.secondaryContainer)
        )

        if (isVisible) {
            EditTextField(
                modifier = Modifier.fillMaxWidth(0.85f),
                text = convertStringToDoubleFormat(input = totalPriceWithoutDiscountTTC),
                errorValue = null,
                label = context.getString(R.string.total),
                onValueChange = {
                    //  onTotalPriceWithDiscountChange(it)
                },
                readOnly = true,
                enabled = true,
                showTrailingIcon = false,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Done
            )

            EditTextField(
                modifier = Modifier.fillMaxWidth(0.85f),
               // text = convertStringToDoubleFormat(input = totalDiscountChange),
                text = totalDiscountChange,
                errorValue = totalDiscountError,
                label = context.getString(R.string.discount_field_title),
                onValueChange = {
                    onTotalDiscountChange(it)
                },
                readOnly = !canEdit,
                enabled = true,
                showTrailingIcon = false,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Done
            )


            Spacer(modifier = Modifier.height(16.dp))

        }
    }


}


