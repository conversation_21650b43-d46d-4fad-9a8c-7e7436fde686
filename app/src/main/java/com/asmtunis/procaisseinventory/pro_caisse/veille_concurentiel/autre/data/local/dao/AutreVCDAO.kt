package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.AUTRE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVCWithImages
import kotlinx.coroutines.flow.Flow


@Dao
interface AutreVCDAO {
    @get:Query("SELECT * FROM $AUTRE_TABLE where Status !='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',DateOp) desc")
    val all: Flow<List<AutreVC>>

    @Query("SELECT * FROM $AUTRE_TABLE WHERE Code_Mob = :code")
    fun getByCodeM(code: String): Flow<AutreVC>

    @Query("SELECT * FROM $AUTRE_TABLE WHERE CodeAutre = :code")
    fun getByCode(code: String): Flow<AutreVC>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(vcAutres: List<AutreVC>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(vcAutres: AutreVC)

    @get:Query("SELECT * FROM $AUTRE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    val noSyncedToAddOrUpdate: Flow<List<AutreVC>>

    @get:Query("SELECT * FROM $AUTRE_TABLE where isSync=0 and Status='DELETED' ")
    val noSyncedToDelete: Flow<List<AutreVC>>

    @get:Query("SELECT count(*) FROM $AUTRE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val countNonSync: Flow<Int>

    @get:Query("SELECT count(*) FROM $AUTRE_TABLE where isSync=0 and Status='DELETED' ")
    val countNoSyncedToDelete: Flow<Int>

    @get:Query("SELECT count(*) FROM $AUTRE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMubtale: Flow<Int>

    @get:Query("SELECT count(*) FROM $AUTRE_TABLE where isSync=0 and Status='DELETED' ")
    val countNoSyncedToDeleteMubtale: Flow<Int>

    @Query("delete from $AUTRE_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $AUTRE_TABLE where CodeAutre=:codeAutre")
    fun deleteByCode(codeAutre: String)

    @Query("DELETE FROM $AUTRE_TABLE where CodeAutre=:CodeAutre or Code_Mob = :CodeMobile")
    fun deleteByIdAndCodeM(CodeAutre: String, CodeMobile: String)

    @Query("UPDATE $AUTRE_TABLE SET CodeAutre = :code, Status = 'SELECTED' , IsSync = 1 where Code_Mob = :codeM")
    fun updateCloudCode(code: String, codeM: String)





    @Query("UPDATE $AUTRE_TABLE SET Status = 'DELETED' , IsSync = 0 where Code_Mob = :codeMobile  or CodeAutre=:code")
    fun setDeleted(code: String, codeMobile: String)

    @Query("UPDATE $AUTRE_TABLE SET Status = :status , IsSync = :isSync where Code_Mob = :code  or CodeAutre=:code")
    fun restDeleted(code: String,status : String, isSync:Boolean)


    @Transaction
    @Query(
        "SELECT * FROM $AUTRE_TABLE " +
                "WHERE CodeAutre LIKE '%' || :searchString || '%' " +
                "and ( CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +
                "and  CASE WHEN :filterByConcurrent !=  ''THEN CodeConcur=:filterByConcurrent ELSE CodeConcur !=:filterByConcurrent  END) " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 1 THEN CodeAutre END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 2 THEN CodeAutre END DESC, " +

                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 1 THEN Autre END ASC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 2 THEN Autre END DESC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByNum(searchString: String, sortBy: String, filterByTypComm: String, filterByConcurrent: String, isAsc: Int): Flow<List<AutreVCWithImages>>

    @Transaction
    @Query(
        "SELECT * FROM $AUTRE_TABLE " +
                "WHERE Autre LIKE '%' ||  :searchString || '%' " +
                "and ( CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +
                "and  CASE WHEN :filterByConcurrent !=  ''THEN CodeConcur=:filterByConcurrent ELSE CodeConcur !=:filterByConcurrent  END) " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 1 THEN CodeAutre END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 2 THEN CodeAutre END DESC, " +

                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 1 THEN Autre END ASC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 2 THEN Autre END DESC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByAutre(searchString: String, sortBy: String, filterByTypComm: String, filterByConcurrent: String, isAsc: Int): Flow<List<AutreVCWithImages>>

    @Transaction
    @Query(
        "SELECT * FROM $AUTRE_TABLE " +
                "WHERE AutreNote LIKE '%' || :searchString || '%' " +
                "and ( CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +
                "and  CASE WHEN :filterByConcurrent !=  ''THEN CodeConcur=:filterByConcurrent ELSE CodeConcur !=:filterByConcurrent  END) " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 1 THEN CodeAutre END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 2 THEN CodeAutre END DESC, " +

                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 1 THEN Autre END ASC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 2 THEN Autre END DESC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByAutreNote(searchString: String, filterByTypComm: String, filterByConcurrent: String, sortBy: String?, isAsc: Int?): Flow<List<AutreVCWithImages>>

    @Transaction
    @Query(
        "SELECT * FROM $AUTRE_TABLE " +
                "WHERE  CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END   " +
                "and  CASE WHEN :filterByConcurrent !=  ''THEN CodeConcur=:filterByConcurrent ELSE CodeConcur !=:filterByConcurrent  END " +


                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 1 THEN CodeAutre END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 2 THEN CodeAutre END DESC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 1 THEN Autre END ASC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 2 THEN Autre END DESC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun getAllFiltred(isAsc: Int, filterByTypComm: String, filterByConcurrent: String, sortBy: String): Flow<List<AutreVCWithImages>>

}