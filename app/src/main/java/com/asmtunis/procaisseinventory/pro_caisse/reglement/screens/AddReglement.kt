package com.asmtunis.procaisseinventory.pro_caisse.reglement.screens

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AttachMoney
import androidx.compose.material.icons.filled.Expand
import androidx.compose.material.icons.filled.Minimize
import androidx.compose.material.icons.filled.Payments
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.PaymentBottomAppBar
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.PaymentConst
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cash.CashPaymentInputView
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cash.CashPaymentView
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cheque.CheckPaymentInputView
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cheque.ChequeTable
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.ticket_resto.TicketRestoInputView
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.ticket_resto.TicketRestoTable
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.view_model.PaymentViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.dialogues.CustomAlertDialogue

@Composable
fun AddReglement(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    navDrawerViewModel : NavigationDrawerViewModel,
    mainViewModel: MainViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    paymentViewModel : PaymentViewModel
) {
    val context = LocalContext.current


    val utilisateur = mainViewModel.utilisateur
    val cashValue = paymentViewModel.cashValue
    val banqueList = mainViewModel.banqueList

    val sessionCaisse = navDrawerViewModel.sessionCaisse
    val exerciceList = mainViewModel.exerciceList

    val clientList = mainViewModel.clientList
    val client = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val requestFocus = paymentViewModel.requestFocus
    val selectedListChequeCaisse = paymentViewModel.selectedListChequeCaisse
    val selectedTicketRestoList = paymentViewModel.selectedTicketRestoList
    val showTicketRestoDetail =paymentViewModel.showTicketRestoDetail
    val showChequeDetail = paymentViewModel.showChequeDetail
    val montantTotalTicketResto = paymentViewModel.montantTotalTicketResto
    val montantTotalCheques = paymentViewModel.montantTotalCheques

    val codeM = paymentViewModel.codeM
    LaunchedEffect(
        key1 = cashValue,
        key2 = montantTotalCheques,
        key3 = montantTotalTicketResto
    ) {

        paymentViewModel.onRestValueChange(client.solde)
    }
    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { mainViewModel.onShowDismissScreenAlertDialogChange(true) },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = client.cLINomPren + " (" + client.cLICode + ")",//stringResource(id = R.string.invpat_number_field),
            )
        },
                bottomBar = {
                    PaymentBottomAppBar (
                        onTicketClick = {
                            paymentViewModel.onShowPaimentTicketRestoChange(true)
                        },
                    onChequeClick = {
                        paymentViewModel.onRequestFocusChange(true)
                        paymentViewModel.onShowPaimentChequeChange(true)
                    },
                    onEspeceClick = {
                        paymentViewModel.onShowPaimentEspeceChange(true)
                    },
                    onFloatingActionButtonClick = {
                        paymentViewModel.savePayment(
                            canSync = true,
                            codeM = codeM,
                            sessionCaisse  = sessionCaisse,
                            client  = client,
                            user = utilisateur,
                            exercice  = exerciceList.firstOrNull()?.exerciceCode ?: "2024",
                            regRemarque = PaymentConst.CREDIT_PAYEMENT_REMARK,
                            tIKMtTTC = client.solde
                        )

                        popBackStack()
                    },
                    floatingActionButtonVisibility = (cashValue!="" &&
                            stringToDouble(cashValue) !=0.0) ||
                            selectedListChequeCaisse.isNotEmpty() ||
                            selectedTicketRestoList.isNotEmpty(),

                    )
                }

    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                popBackStack()
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)
        )

        Column(
            modifier = Modifier.padding(padding)
        ) {

            if (paymentViewModel.showPaimentEspece) {
                CashPaymentInputView(
                    cashValue = cashValue,
                    onValueChange = {
                        paymentViewModel.onCashValueChange(it)
                    },
                    cashErrorValue = null,
                    onDismissRequest = {
                        paymentViewModel.onShowPaimentEspeceChange(false)
                    }
                )
            }


            if (paymentViewModel.showPaimentCheque) {
                CheckPaymentInputView(
                    montantCheque = paymentViewModel.montantCheque,
                    requestFocus = requestFocus,
                    montantChequeError = null,
                    onMontantChequeChange = {
                        // textValidationViewModel.onAddChequeEvent(AddChequeFormEvent.Montant(it))
                        paymentViewModel.onMontantChequeChange(it)
                    },
                    checkNbr = paymentViewModel.numeroCheque,
                    checkNbrError = null,
                    onCheckNbrChange = {

                        //textValidationViewModel.onAddChequeEvent(AddChequeFormEvent.NumeroChequeChanged(it))
                        paymentViewModel.onNumeroChequeChange(it)
                    },
                    checkDeadline = paymentViewModel.echeance,
                    checkDeadlineError = null,
                    onCheckDeadlineChange = {
                        //    textValidationViewModel.onAddChequeEvent(AddChequeFormEvent.EcheanceChanged(it))
                        paymentViewModel.onEcheanceChange(it)
                    },
                    onDismissRequest = {
                        paymentViewModel.resetChequeVariable()
                        paymentViewModel.onShowPaimentChequeChange(false)
                    },
                    onAddClicked = {
                        // textValidationViewModel.onAddChequeEvent(AddChequeFormEvent.SubmitAddCheque)
                        paymentViewModel.addChequeCaisse(
                            client = client,
                            exerciceList = exerciceList,
                        sessionCaisse = sessionCaisse
                        )
                    },
                    bankList = banqueList,
                    onSelectedBanqueChange = {
                        //  textValidationViewModel.onAddChequeEvent(AddChequeFormEvent.Bank(it))
                        paymentViewModel.onBankChange(it)
                    },
                    isBanqueExpanded = paymentViewModel.banqueExpand,
                    onBanqueExpandChange = { paymentViewModel.onBanqueExpandChange(it) },
                    selectedBanque = paymentViewModel.bank,
                    selectedBanqueError = null,
                    showDatePicker = mainViewModel.showDatePicker,
                    onShowDatePickerChange = { mainViewModel.onShowDatePickerChange(it) }

                )
            }




            if (paymentViewModel.showPaimentTicketResto) {
                TicketRestoInputView(
                    montantTicketResto = paymentViewModel.montantTicketResto,
                    montantTicketRestoError = null,
                    onMontantTicketRestoChange = {
                        paymentViewModel.onMontantTicketRestoChange(it)
                    },
                    ticketRestoNbr = paymentViewModel.ticketRestoNbr,
                    ticketRestoNbrError = null,
                    onTicketRestoNbrChange = {

                        paymentViewModel.onTicketRestoNbrChange(it)
                    },
                    taux = paymentViewModel.tauxTicketResto,
                    tauxError = null,
                    onTicketRestoTauxChange = {
                        paymentViewModel.onTauxTicketRestoChange(it)
                    },
                    onDismissRequest = {
                        paymentViewModel.resetTicketRestoVariable()
                        paymentViewModel.onShowPaimentTicketRestoChange(false)
                    },
                    onAddClicked = {

                        paymentViewModel.addTraiteCaisse(
                                       sessionCaisse = sessionCaisse,
                                       client = client,
                                       exerciceList = exerciceList
                        )
                    },
                    ticketRestoList = mainViewModel.ticketRestoList,
                    onSelectedTicketRestoChange = {
                        //  textValidationViewModel.onAddTicketRestoEvent(AddTicketRestoFormEvent.Bank(it))
                        paymentViewModel.onSelectedTicketRestoChange(it)

                    },
                    isTicketRestoExpanded = paymentViewModel.ticketRestoExpand,
                    onTicketRestoExpandChange = {
                        paymentViewModel.onTicketRestoExpandChange(it)
                    },
                    selectedTicketResto = paymentViewModel.selectedTicketResto,
                    selectedTicketRestoError = null

                )
            }

            AnimatedVisibility(
                visible = convertStringToPriceFormat(paymentViewModel.restValue) != convertStringToPriceFormat(client.solde),
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
              //  Spacer(modifier = Modifier.height(12.dp))

                ItemDetail(
                    title = stringResource(id = R.string.new_sold_title),
                    dataText = convertStringToPriceFormat(paymentViewModel.restValue),
                    icon = Icons.Default.AttachMoney
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            ItemDetail(
                title = stringResource(id = R.string.solde_client),
                dataText = convertStringToPriceFormat(client.solde),
                icon = Icons.Default.Payments
            )


            Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = cashValue.isNotEmpty() && stringToDouble(cashValue)!=0.0,
                enter = fadeIn(),
                exit = fadeOut()
            ) {

                CashPaymentView(
                    cashValue = cashValue,
                    onLongPress = { paymentViewModel.onCashValueChange("") },
                    onTap = { paymentViewModel.onShowPaimentEspeceChange(true) }
                )

            }
            Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = selectedListChequeCaisse.isNotEmpty(),
                enter = fadeIn(),
                exit = fadeOut()
            ) {

                OutlinedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, end = 16.dp)

                ) {
                    ItemDetail(
                        modifier = Modifier.padding(top = 18.dp, bottom = 18.dp),
                        title = stringResource(id = R.string.check_title),
                        dataText = convertStringToPriceFormat(montantTotalCheques),
                        icon = if(showChequeDetail) Icons.Default.Minimize else Icons.Default.Expand,
                        onClick = { paymentViewModel.onShowChequeDetailChange(!showChequeDetail) }
                    )
                }

            }

             Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = showChequeDetail && selectedListChequeCaisse.isNotEmpty(),
                enter = fadeIn(),
                exit = fadeOut()
            ) {

                ChequeTable(
                    listChequeCaisse = selectedListChequeCaisse,
                    onTap = { index, banque->
                        paymentViewModel.onRequestFocusChange(false)
                        paymentViewModel.onShowPaimentChequeChange(true)
                        paymentViewModel.onBankChange(banque)
                        paymentViewModel.onEcheanceChange(selectedListChequeCaisse[index].echeanceCheque ?: "N/A")
                        paymentViewModel.onNumeroChequeChange(selectedListChequeCaisse[index].numCheque)
                        paymentViewModel.onMontantChequeChange(selectedListChequeCaisse[index].montant)

                    },
                    onLongPress = {index ->
                        paymentViewModel.removeChequeCaisse(chequeCaisse = selectedListChequeCaisse[if (selectedListChequeCaisse.size == 1) 0 else index])
                        paymentViewModel.claculateMontantTotalCheques()

                    },
                    banque = { index ->
                            banqueList.firstOrNull { it.bANDes == selectedListChequeCaisse[index].banque }?: Banque(
                            bANCode = selectedListChequeCaisse[index].banque ?: "N/A",
                            bANDes = "N/A"
                        )
                    },
                    onShowChequeTableChange = {
                        paymentViewModel.onShowChequeDetailChange(false)
                    }
                )
            }



            Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = selectedTicketRestoList.isNotEmpty(),
                enter = fadeIn(),
                exit = fadeOut()
            ) {

                OutlinedCard (
                    modifier = Modifier.fillMaxWidth().padding(start = 16.dp, end = 16.dp)

                ){
                    ItemDetail(
                        modifier = Modifier.padding(top = 18.dp, bottom = 18.dp),
                        title = stringResource(id = R.string.resto_ticket),
                        dataText = convertStringToPriceFormat(montantTotalTicketResto),
                        icon = if(showTicketRestoDetail) Icons.Default.Minimize else Icons.Default.Expand,
                        onClick = { paymentViewModel.onShowTicketRestoDetailChange(!showTicketRestoDetail) },

                    )
                }

                }


            Spacer(modifier = Modifier.height(12.dp))
            AnimatedVisibility(
                visible = showTicketRestoDetail && selectedTicketRestoList.isNotEmpty(),
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                TicketRestoTable(
                    listTraiteCaisse = selectedTicketRestoList,
                onTap = { index, carteResto->
                    paymentViewModel.onShowPaimentTicketRestoChange(true)


                    paymentViewModel.onSelectedTicketRestoChange(
                        CarteResto(
                            code = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].tRAITCompteLocal,
                            societe = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].tRAITType?: "N/A"
                        )
                    )
                    paymentViewModel.onTauxTicketRestoChange(
                        selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].taux.toString()
                    )
                    paymentViewModel.onTicketRestoNbrChange(
                        selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].nbrTicket.toString()
                    )
                    paymentViewModel.onMontantTicketRestoChange(
                        selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].montantInitial.toString()
                    )

                },
                onLongPress = {index->
                    paymentViewModel.removeTraiteCaisse(traiteCaisse = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index])
                    paymentViewModel.claculateMontantTotalTicketResto()

                },
                carteResto = {index->
                     CarteResto(
                        code = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].tRAITCompteLocal,
                        societe = selectedTicketRestoList[if (selectedTicketRestoList.size == 1) 0 else index].tRAITType?: "N/A"
                    )

                },
                onShowTicketRestoTableChange = { paymentViewModel.onShowTicketRestoDetailChange(false) }
                )
            }
        }
    }
}

