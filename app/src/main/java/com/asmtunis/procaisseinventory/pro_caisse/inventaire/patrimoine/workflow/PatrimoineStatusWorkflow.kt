package com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.workflow

import android.util.Log
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.core.enum_classes.EtatBonTransfert

/**
 * Gestionnaire de workflow pour les statuts du module patrimoine
 * Gère le cycle de vie des mouvements d'actifs avec validation automatique
 */
object PatrimoineStatusWorkflow {
    
    private const val TAG = "PatrimoineStatusWorkflow"
    
    /**
     * Détermine le statut initial basé sur le type d'inventaire
     * @param typeInv Type d'inventaire (SORTIE ou ENTREE)
     * @return Statut approprié selon le workflow
     */
    fun getInitialStatus(typeInv: String): String {

        val status = when (typeInv) {
            TypePatrimoine.SORTIE.typePat -> {
                EtatBonTransfert.EN_INSTANCE.value
            }
            TypePatrimoine.ENTREE.typePat -> {
                EtatBonTransfert.VALIDEE.value
            }
            else -> {
                EtatBonTransfert.EN_INSTANCE.value
            }
        }

        return status
    }

    /**
     * Convertit une valeur legacy (numérique) en énumération
     * @param value Valeur à convertir ("1", "2", "En Instance", "Validée")
     * @return L'énumération correspondante
     */
    fun fromValue(value: String?): EtatBonTransfert {
        return when (value) {
            "1", "En Instance" -> EtatBonTransfert.EN_INSTANCE
            "2", "Validé", "Validée" -> EtatBonTransfert.VALIDEE
            else -> EtatBonTransfert.EN_INSTANCE // Par défaut
        }
    }

    /**
     * Obtient le texte d'affichage pour une valeur donnée
     * @param value Valeur à convertir
     * @return Texte d'affichage formaté
     */
    fun getDisplayText(value: String?): String {
        return fromValue(value).value
    }


    
    /**
     * Détermine le statut pour un Deplacement In créé depuis l'écran État
     * @param isFromEtatScreen true si créé depuis l'écran État Deplacement Out
     * @return Statut approprié (toujours VALIDEE pour les Deplacement In depuis État)
     */
    fun getDeplacementInStatusFromEtat(isFromEtatScreen: Boolean): String {
        
        val status = if (isFromEtatScreen) {
            EtatBonTransfert.VALIDEE.value
        } else {
            EtatBonTransfert.VALIDEE.value
        }
        
        return status
    }
    
    /**
     * Valide automatiquement un Deplacement Out lors de la création d'un Deplacement In
     * @param originalStatus Statut actuel du Deplacement Out
     * @return Nouveau statut (VALIDEE si transition valide)
     */
    fun validateDeplacementOut(originalStatus: String?): String {

        val newStatus = if (isEnInstance(originalStatus)) {
            EtatBonTransfert.VALIDEE.value
        } else {
            originalStatus ?: EtatBonTransfert.EN_INSTANCE.value
        }

        return newStatus
    }

    /**
     * Vérifie si un statut est "En Instance"
     * @param value Valeur à vérifier
     * @return true si le statut est "En Instance"
     */
    private fun isEnInstance(value: String?): Boolean {
        return when (value) {
            "1", "En Instance" -> true
            else -> false
        }
    }
}
