package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.screens

import androidx.compose.runtime.Composable
import com.asmtunis.procaisseinventory.articles.consultation.view_model.SyncArticlesViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model.SyncBonCommandeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.SyncBonLivraisonViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model.SyncBonRetourViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.view_model.ClientViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.view_model.SyncClientViewModel
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model.DistributionNumeriqueViewModel
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model.SyncDistributionNumViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.SyncInvBatimentViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.SyncInvPatrimoineViewModel
import com.asmtunis.procaisseinventory.pro_caisse.reglement.view_model.SyncReglementViewModel
import com.asmtunis.procaisseinventory.pro_caisse.tournee.view_model.SyncTourneeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.SyncVcViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import kotlinx.coroutines.FlowPreview

@OptIn(FlowPreview::class)
@Composable
fun DigitalDistributionListPane(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    clientViewModel: ClientViewModel,
    mainViewModel: MainViewModel,
    locationViewModule : LocationViewModule,
    distNumViewModel: DistributionNumeriqueViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,

    syncVcViewModel : SyncVcViewModel,
    syncDistNumViewModel: SyncDistributionNumViewModel,
    syncReglementViewModel : SyncReglementViewModel,
    syncBonRetourViewModel: SyncBonRetourViewModel,
    syncInvPatrimoineViewModel : SyncInvPatrimoineViewModel,
    syncBonCommandeViewModel : SyncBonCommandeViewModel,
    syncBLVM : SyncBonLivraisonViewModel,
    syncClientViewModel: SyncClientViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncArticlesViewModel: SyncArticlesViewModel,
    syncTourneeViewModel: SyncTourneeViewModel,
    syncInvBatimentViewModel: SyncInvBatimentViewModel
) {

   /* Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    scope.launch { drawer.open() }
                },
                showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                title = stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title),
                titleVisibilty = !distNumViewModel.showSearchView && distNumViewModel.searchTextState.text.isEmpty(),
                actions = {
                    SearchSectionComposable(
                        label = context.getString(
                            R.string.filter_by,
                            when (listFilter) {
                                is ListSearch.FirstSearch -> filterList[0]
                                is ListSearch.SecondSearch -> filterList[1]
                                else -> filterList[2]}),
                        searchVisibility  = distNumViewModel.showSearchView || distNumViewModel.searchTextState.text.isNotEmpty(),
                        searchTextState = distNumViewModel.searchTextState,
                        onSearchValueChange = {
                            distNumViewModel.onSearchValueChange(TextFieldValue(it))
                            if(it == "")   {
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                //   mainViewModel.onSelectedClientChange(Client())
                            }
                        },
                        onShowSearchViewChange = {
                            distNumViewModel.onShowSearchViewChange(it)
                            if(!it) {
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                /** this bloc to handle search visibility when custom search by client*/
                                distNumViewModel.onSearchValueChange(TextFieldValue(""))
                                // mainViewModel.onSelectedClientChange(Client())
                            }
                        },
                        onShowCustomFilterChange = {
                            distNumViewModel.onShowCustomFilterChange(it)
                        }
                    )


                }
            )
        },
        floatingActionButton = {
            SnapScrollingButton(
                isScrollInProgress = listState.isScrollInProgress,
                firstVisibleItemIndex = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value,
                density = density,
                animateScrollToItem = {
                    listState.animateScrollToItem(index = it)
                }
            )
        }
    ) { padding ->
        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {

            if (distNumViewModel.showCustomModalBottomSheet) {
                CustomModalBottomSheet(
                    title = distNumViewModel.selectedVisite.vIS_Num,
                    showPrintIcon = false,
                    onDismissRequest= {
                        distNumViewModel.onShowCustomModalBottomSheetChange(false)
                        distNumViewModel.onSelectedVisiteChange(emptyMap())
                    },
                    onDeleteRequest= {
                        (distNumViewModel::setIsDeletedVisite)(distNumViewModel.selectedVisite)
                    },
                    onPrintRequest= {

                    },
                    status = distNumViewModel.selectedVisite.status
                )
            }

            if (distNumViewModel.showCustomFilter) {
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.visite_order),
                    onShowCustomFilterChange  = {
                        distNumViewModel.onShowCustomFilterChange(false)
                    },
                    onEvent = {
                        distNumViewModel.onEvent(event = it)
                    }
                )
            }



            if(getProCaisseDataViewModel.visitesState.loading)
                LottieAnim(lotti = R.raw.loading, size = 250.dp)
            else {
                if (state.lists.isNotEmpty()) {
                    VisiteList (
                        listState = listState,
                        navigate = { navigate(it) },
                        popBackStack = { popBackStack() },
                        isConnected = isConnected,
                        selectedBaseconfig = selectedBaseconfig,
                        clientList = mainViewModel.clientList,
                        getProCaisseDataViewModel = getProCaisseDataViewModel,
                        distNumViewModel = distNumViewModel,
                        filteredList = state.lists,
                        locationViewModule = locationViewModule
                    )
                } else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
            }

        }
    }*/
}

