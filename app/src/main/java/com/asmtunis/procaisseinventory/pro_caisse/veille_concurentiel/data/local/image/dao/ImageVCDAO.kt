package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.image.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.PIECE_JOINTS_TABLE
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import kotlinx.coroutines.flow.Flow


@Dao
interface ImageVCDAO {
    @get:Query("SELECT * FROM $PIECE_JOINTS_TABLE where Status!='DELETED' order by strftime('%Y-%m-%d %H-%M',DateOp) desc")
    val all: Flow<List<ImagePieceJoint>>

    @Query("SELECT * FROM $PIECE_JOINTS_TABLE  WHERE Code_TypeVC =:code order by strftime('%Y-%m-%d %H-%M',DateOp) desc")
    fun getImageByCode(code: String?): Flow<List<ImagePieceJoint>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(vcImages: List<ImagePieceJoint>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(vcImages: ImagePieceJoint)

    @get:Query("SELECT count(*) FROM $PIECE_JOINTS_TABLE  where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMubtale: Flow<Int>

    @get:Query("SELECT * FROM $PIECE_JOINTS_TABLE  where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    val noSynced: Flow<List<ImagePieceJoint>>

    @Query("delete from $PIECE_JOINTS_TABLE ")
    fun deleteAll()


    @Query("delete from $PIECE_JOINTS_TABLE where isSync=0 and  Status='INSERTED' and Code_TypeVC like :codeTypeVc")
    fun deleteByCodeTypeVc(codeTypeVc: String)



    @Query("UPDATE $PIECE_JOINTS_TABLE SET Status = 'DELETED' , IsSync = 0 where Code_IMG = :codeIMG ")
    fun setDeleted(codeIMG: String)

    @Query("UPDATE $PIECE_JOINTS_TABLE SET isSync = 1, Status= 'SELECTED' where Code_Mob = :codeM")
    fun setImagesSynced(codeM: String)



}
