package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.workflow

import android.util.Log
import com.asmtunis.procaisseinventory.core.model.BaseModel
import com.simapps.ui_kit.utils.getCurrentDateTime
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.BonCommandeUtils
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * Comprehensive BC to BL Conversion Workflow Manager
 * Handles the complete conversion process with validation, business rules, and error handling
 * Following the old app's sync pattern for automatic synchronization
 */
class BcToBLConversionWorkflow {

    /**
     * Complete BC to BL conversion workflow
     */
    suspend fun executeConversion(
        request: ConversionRequest,
        dataProvider: ConversionDataProvider
    ): Flow<ConversionResult> = flow {
        try {
            emit(ConversionResult.Loading("Initialisation de la conversion..."))

            // Step 1: Validate input parameters
            emit(ConversionResult.Loading("Validation des paramètres..."))
            validateRequest(request)

            // Step 2: Retrieve BC and its lines
            emit(ConversionResult.Loading("Récupération du bon de commande..."))
            val bonCommande = dataProvider.getBonCommande(request.bonCommandeM, request.exerciceCode)
                ?: throw ConversionException("Bon de commande non trouvé: ${request.bonCommandeM}")

            val ligneBonCommandes = dataProvider.getLigneBonCommande(request.bonCommandeM, request.exerciceCode)

            // Step 3: Validate BC for conversion
            emit(ConversionResult.Loading("Validation du bon de commande..."))
            val validationResult = BonCommandeUtils.validateBcForConversion(bonCommande, ligneBonCommandes)
            if (!validationResult.isValid) {
                emit(ConversionResult.ValidationError(validationResult.errors))
                return@flow
            }

            // Step 4: Check if BC already converted
            emit(ConversionResult.Loading("Vérification des conversions existantes..."))
            val alreadyConverted = dataProvider.checkIfBcAlreadyConverted(bonCommande.dEVNum)
            if (alreadyConverted) {
                throw ConversionException("Ce bon de commande a déjà été converti en BL")
            }

            // Step 5: Retrieve client information
            emit(ConversionResult.Loading("Récupération des informations client..."))
            val client = dataProvider.getClient(bonCommande.dEVCodeClient ?: "")
                ?: throw ConversionException("Client non trouvé: ${bonCommande.dEVCodeClient}")

            // Step 6: Generate new BL number
            emit(ConversionResult.Loading("Génération du numéro BL..."))
            val maxNumTicket = dataProvider.getMaxNumTicketForCarnet(request.sessionCaisse.sCIdCarnet ?: "N/A")
            val newBlNumber = generateBlNumber(maxNumTicket + 1, request.utilisateur)

            // Step 7: Convert BC to BL
            emit(ConversionResult.Loading("Conversion en cours..."))
            val (ticket, ligneTickets) = BonCommandeUtils.convertBcToBl(
                bonCommande = bonCommande,
                ligneBonCommandes = ligneBonCommandes,
                newBlNumber = newBlNumber,
                carnetId = request.sessionCaisse.sCIdCarnet ?: "N/A",
                currentDate = getCurrentDateTime(),
                userId = request.utilisateur.codeUt
            )

            // Step 8: Apply business rules
            emit(ConversionResult.Loading("Application des règles métier..."))
            val businessRulesResult = applyBusinessRules(
                ticket = ticket,
                ligneTickets = ligneTickets,
                client = client,
                dataProvider = dataProvider
            )

            if (businessRulesResult.hasErrors()) {
                emit(ConversionResult.BusinessRuleError(businessRulesResult.errors))
                return@flow
            }

            // Step 9: Generate preview
            emit(ConversionResult.Loading("Génération de l'aperçu..."))
            val preview = ConversionPreview(
                originalBc = bonCommande,
                originalLines = ligneBonCommandes,
                convertedTicket = ticket,
                convertedLines = ligneTickets,
                client = client,
                warnings = businessRulesResult.warnings
            )

            emit(ConversionResult.PreviewReady(preview))

        } catch (e: ConversionException) {
            emit(ConversionResult.Error(e.message ?: "Erreur de conversion"))
        } catch (e: Exception) {
            emit(ConversionResult.Error("Erreur inattendue: ${e.message}"))
        }
    }

    /**
     * Execute the final conversion after user confirmation
     * Now uses sync-enabled saving following old app pattern
     */
    suspend fun executeConfirmedConversion(
        preview: ConversionPreview,
        dataProvider: ConversionDataProvider
    ): Flow<ConversionResult> = flow {
        try {
            // Step 1: Deduct stock for each article (this was missing in BC creation)
            emit(ConversionResult.Loading("Déduction du stock..."))
            deductStockForConversion(preview, dataProvider)

            // Step 2: Save ticket and lines with sync enabled - following old app pattern
            emit(ConversionResult.Loading("Sauvegarde du bon de livraison..."))
            dataProvider.saveTicketWithSync(preview.convertedTicket, preview.convertedLines)

            // Step 3: Update BC status
            emit(ConversionResult.Loading("Mise à jour du statut BC..."))
            dataProvider.updateBonCommandeStatus(
                bonCommandeM = preview.originalBc.devCodeM,
                status = "PROCESSED",
                devDdm = getCurrentDateTime()
            )

            emit(ConversionResult.Success(
                ticket = preview.convertedTicket,
                ligneTickets = preview.convertedLines,
                message = "Conversion réussie: BC ${preview.originalBc.dEVNum} → BL ${preview.convertedTicket.tIKNumTicketM}"
            ))

        } catch (e: Exception) {
            emit(ConversionResult.Error("Erreur lors de la sauvegarde: ${e.message}"))
        }
    }

    private fun validateRequest(request: ConversionRequest) {
        if (request.bonCommandeM.isBlank()) {
            throw ConversionException("Numéro de bon de commande requis")
        }
        if (request.exerciceCode.isBlank()) {
            throw ConversionException("Code exercice requis")
        }
        if (request.utilisateur.codeUt.isBlank()) {
            throw ConversionException("Utilisateur requis")
        }
        if (request.sessionCaisse.sCIdCarnet.isNullOrBlank()) {
            throw ConversionException("Session caisse requise")
        }
    }

    private fun generateBlNumber(ticketNumber: Int, utilisateur: Utilisateur): String {
        // Following old app logic: simple sequential numbering within session
        // Format: BL_M_STATION_USERID_TICKETNUMBER
        val prefix = "BL_M_"
        val station = utilisateur.Station
        val userId = utilisateur.codeUt
        return "${prefix}${station}_${userId}_${ticketNumber}"
    }

    private suspend fun applyBusinessRules(
        ticket: Ticket,
        ligneTickets: List<LigneTicket>,
        client: Client,
        dataProvider: ConversionDataProvider
    ): BusinessRulesResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // Stock validation
        ligneTickets.forEach { ligne ->
            val stockArticle = dataProvider.getStockArticle(ligne.lTCodArt, ligne.lTStation ?: "")
            val availableQty = stringToDouble(stockArticle?.toString() ?: "0")
            val requestedQty = stringToDouble(ligne.lTQte)

            if (requestedQty > availableQty) {
                if (availableQty == 0.0) {
                    errors.add("Article ${ligne.lTCodArt}: Stock épuisé")
                } else {
                    warnings.add("Article ${ligne.lTCodArt}: Stock insuffisant (demandé: $requestedQty, disponible: $availableQty)")
                }
            }
        }

        // Client credit limit validation
        val currentSold = stringToDouble(client.solde ?: "0")
        val ticketAmount = stringToDouble(ticket.tIKMtTTC)
        val creditLimit = stringToDouble(client.cLIMaxCredit ?: "0")

        if (currentSold - ticketAmount < -creditLimit) {
            warnings.add("Limite de crédit dépassée pour client ${client.cLICode}")
        }

        return BusinessRulesResult(errors, warnings)
    }

    /**
     * Deduct stock for each article during BC to BL conversion
     * This is where stock is actually reduced since BC creation doesn't touch stock
     */
    private suspend fun deductStockForConversion(
        preview: ConversionPreview,
        dataProvider: ConversionDataProvider
    ) {

        preview.convertedLines.forEach { ligneTicket ->
            val qty = stringToDouble(ligneTicket.lTQte)
            val articleCode = ligneTicket.lTCodArt
            val station = ligneTicket.lTStation ?: ""


            // Get station stock article
            val stationStockArticle = dataProvider.getStationStockArticle(articleCode, station)

            if (stationStockArticle != null) {
                // Update station stock
                ArticleOpeartions.updateStationArticleQte(
                    stationStockArticle = stationStockArticle,
                    opeartion = Globals.MINUS,
                    quatity = qty,
                    aRTCode = articleCode,
                    codeStation = station,
                    updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                        // Note: This will be called synchronously by ArticleOpeartions
                        // The actual database update will be handled by the dataProvider
                    }
                )

                // Update the station stock in database
                val sARTQte = com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble(stationStockArticle.sARTQteStation)
                val sartQteDeclare = com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble(stationStockArticle.sARTQteDeclaree)
                val newQteStation = sARTQte - qty
                val newSartQteDeclare = sartQteDeclare - qty
                dataProvider.updateQtePerStation(newQteStation.toString(), newSartQteDeclare.toString(), articleCode, station)

                // Update global article stock
                val article = dataProvider.getArticle(articleCode)
                if (article != null) {
                    val aRTQteStock = com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble(article.aRTQteStock)
                    val newQteAllStations = aRTQteStock - qty
                    dataProvider.updateArtQteStock(newQteAllStations.toString(), newQteAllStations.toString(), articleCode)
                }

            } else {
            }
        }

    }
}

// Data classes for the workflow
data class ConversionRequest(
    val bonCommandeM: String,
    val exerciceCode: String,
    val utilisateur: Utilisateur,
    val sessionCaisse: SessionCaisse
)

data class ConversionPreview(
    val originalBc: BonCommande,
    val originalLines: List<LigneBonCommande>,
    val convertedTicket: Ticket,
    val convertedLines: List<LigneTicket>,
    val client: Client,
    val warnings: List<String>
)

data class BusinessRulesResult(
    val errors: List<String>,
    val warnings: List<String>
) {
    fun hasErrors(): Boolean = errors.isNotEmpty()
}

sealed class ConversionResult {
    data class Loading(val message: String) : ConversionResult()
    data class ValidationError(val errors: List<String>) : ConversionResult()
    data class BusinessRuleError(val errors: List<String>) : ConversionResult()
    data class PreviewReady(val preview: ConversionPreview) : ConversionResult()
    data class Success(
        val ticket: Ticket,
        val ligneTickets: List<LigneTicket>,
        val message: String
    ) : ConversionResult()
    data class Error(val message: String) : ConversionResult()
}

class ConversionException(message: String) : Exception(message)

// Interface for data access during conversion
interface ConversionDataProvider {
    suspend fun getBonCommande(bonCommandeM: String, exercice: String): BonCommande?
    suspend fun getLigneBonCommande(bonCommandeM: String, exercice: String): List<LigneBonCommande>
    suspend fun checkIfBcAlreadyConverted(bcNumber: String): Boolean
    suspend fun getClient(clientCode: String): Client?
    suspend fun getMaxNumTicketForCarnet(carnetId: String): Int
    suspend fun getStockArticle(articleCode: String, station: String): Any? // Replace with actual type
    suspend fun getArticle(articleCode: String): Article?
    suspend fun saveTicket(ticket: Ticket)
    suspend fun saveLigneTickets(ligneTickets: List<LigneTicket>)
    suspend fun updateBonCommandeStatus(bonCommandeM: String, status: String, devDdm: String)

    // New sync-enabled method following old app pattern
    suspend fun saveTicketWithSync(ticket: Ticket, ligneTickets: List<LigneTicket>)

    // Stock management methods for BC to BL conversion
    suspend fun getStationStockArticle(articleCode: String, station: String): StationStockArticle?
    suspend fun updateQtePerStation(newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String)
    suspend fun updateArtQteStock(newQteAllStations: String, newQteStation: String, codeArticle: String)
}
