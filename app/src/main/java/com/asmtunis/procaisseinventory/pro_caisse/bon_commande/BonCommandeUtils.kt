package com.asmtunis.procaisseinventory.pro_caisse.bon_commande

import com.asmtunis.procaisseinventory.core.model.BaseModel
import com.asmtunis.procaisseinventory.core.sync_workmanager.InstantSyncTrigger
import com.asmtunis.procaisseinventory.core.sync_workmanager.insertLignesTicketWithSync
import com.asmtunis.procaisseinventory.core.sync_workmanager.insertTicketWithSync
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

object BonCommandeUtils {

    fun bcTobl(
        bonCommandeWithClient: BonCommandeWithClient,
        lgBonCommandeWithArticle: List<LigneBonCommandeWithArticle>,
        setTableList: (lgBCWithArticle: LigneBonCommandeWithArticle) -> Unit,
        setModify: () -> Unit
    ) {
        if (bonCommandeWithClient.bonCommande != BonCommande()) {
            for (lgBonCommande in lgBonCommandeWithArticle) {
                setTableList(lgBonCommande)
            }

            setModify()
        }
    }

    /**
     * Convert BC to BL following the old app's logic
     * This matches the data transformation from the old app workflow
     */
    fun convertBcToBl(
        bonCommande: BonCommande,
        ligneBonCommandes: List<LigneBonCommande>,
        newBlNumber: String,
        carnetId: String,
        currentDate: String,
        userId: String
    ): Pair<Ticket, List<LigneTicket>> {

        // Extract numeric ticket number from the generated BL number
        val numericTicketNumber = extractNumericFromBlNumber(newBlNumber)

        // Convert BC to BL (Ticket) - following old app logic
        val ticket = Ticket(
            tIKNumTicketM = newBlNumber,
            tIKNumTicket = numericTicketNumber,
            tIKExerc = bonCommande.dEVExerc ?: "",
            tIKIdCarnet = carnetId,
            tIKDateHeureTicket = currentDate,
            tIKCodClt = bonCommande.dEVCodeClient ?: "",
            tIKMtTTC = bonCommande.dEVMntTTC ?: "0", // Use correct field name
            tIKMtRemise = bonCommande.dEVRemise ?: "0",
            tIKDDm = currentDate,
            dDmM = currentDate,
            tIKEtat = "0", // Initial status as in old app
            tIKUser = userId,
            // Additional fields from old app
            tIKNomClient = bonCommande.dEVClientName ?: "", // Use correct field name
            tIKMtHT = calculateTicketHT(ligneBonCommandes),
            tIKMtTVA = calculateTicketTVA(ligneBonCommandes)
        )

        // Convert BC lines to BL lines (LigneTicket) - following old app logic
        val ligneTickets = ligneBonCommandes.mapIndexed { index, bcLine ->
            LigneTicket(
                lTNumTicketM = newBlNumber,
                lTNumTicket = numericTicketNumber,
                lTCodArt = bcLine.lGDEVCodeArt,
                lTQte = bcLine.lGDEVQte ?: "0",
                lTPrixVente = bcLine.lGDEVPUHT ?: "0", // Use correct field name
                lTRemise = bcLine.lGDEVRemise ?: "0",
                lTTVA = bcLine.lGDEVTva ?: "0",
                lTMtHT = bcLine.lGDEVNetht ?: "0",
                lTMtTTC = calculateLineTTC(bcLine),
                lTExerc = bonCommande.dEVExerc ?: "",
                lTIdCarnet = carnetId,
                lTDDm = currentDate,
                dDmM = currentDate,
                lTUnite = bcLine.lGDEVUnite ?: "",
                lTStation = bcLine.lGDEVStation ?: "",
                lTUser = userId,
                lTNumOrdre = (index + 1).toString(), // Sequential line numbering
                lTCommande = bonCommande.dEVNum // Reference to original BC
            )
        }

        return Pair(ticket, ligneTickets)
    }

    /**
     * Validate BC for conversion - following old app validation rules
     */
    fun validateBcForConversion(
        bonCommande: BonCommande,
        ligneBonCommandes: List<LigneBonCommande>
    ): ValidationResult {
        val errors = mutableListOf<String>()

        // Check if BC exists and is valid
        if (bonCommande.dEVNum.isBlank()) {
            errors.add("Numéro de bon de commande invalide")
        }

        // Check if BC has lines
        if (ligneBonCommandes.isEmpty()) {
            errors.add("Le bon de commande ne contient aucune ligne")
        }

        // Check if BC is already processed
        if (bonCommande.dEVEtat == "PROCESSED") {
            errors.add("Ce bon de commande a déjà été traité")
        }

        // Validate each line
        ligneBonCommandes.forEachIndexed { index, ligne ->
            if (ligne.lGDEVCodeArt.isBlank()) {
                errors.add("Ligne ${index + 1}: Code article manquant")
            }

            val quantity = stringToDouble(ligne.lGDEVQte ?: "0")
            if (quantity <= 0) {
                errors.add("Ligne ${index + 1}: Quantité invalide")
            }

            val price = stringToDouble(ligne.lGDEVPUHT ?: "0")
            if (price < 0) {
                errors.add("Ligne ${index + 1}: Prix invalide")
            }
        }

        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }

    /**
     * Extract numeric part from BL number for compatibility
     * Following old app logic: use the maxNumTicket + 1 approach
     */
    private fun extractNumericFromBlNumber(blNumber: String): String {
        // Extract numeric part from BL_M_STATION_NUMBER_TIMESTAMP format
        val parts = blNumber.split("_")
        return if (parts.size >= 4) parts[3] else "1"
    }

    /**
     * Calculate line TTC amount following old app logic
     */
    private fun calculateLineTTC(bcLine: LigneBonCommande): String {
        val quantity = stringToDouble(bcLine.lGDEVQte ?: "0")
        val priceHT = stringToDouble(bcLine.lGDEVPUHT ?: "0")
        val tva = stringToDouble(bcLine.lGDEVTva ?: "0")
        val remise = stringToDouble(bcLine.lGDEVRemise ?: "0")

        val netHT = (quantity * priceHT) - remise
        val ttc = netHT * (1 + tva / 100)

        return ttc.toString()
    }

    /**
     * Calculate total HT amount for ticket
     */
    private fun calculateTicketHT(ligneBonCommandes: List<LigneBonCommande>): String {
        val totalHT: Double = ligneBonCommandes.sumOf { bcLine ->
            val quantity = stringToDouble(bcLine.lGDEVQte ?: "0")
            val priceHT = stringToDouble(bcLine.lGDEVPUHT ?: "0")
            val remise = stringToDouble(bcLine.lGDEVRemise ?: "0")
            (quantity * priceHT) - remise
        }
        return totalHT.toString()
    }

    /**
     * Calculate total TVA amount for ticket
     */
    private fun calculateTicketTVA(ligneBonCommandes: List<LigneBonCommande>): String {
        val totalTVA: Double = ligneBonCommandes.sumOf { bcLine ->
            val quantity = stringToDouble(bcLine.lGDEVQte ?: "0")
            val priceHT = stringToDouble(bcLine.lGDEVPUHT ?: "0")
            val tva = stringToDouble(bcLine.lGDEVTva ?: "0")
            val remise = stringToDouble(bcLine.lGDEVRemise ?: "0")
            val netHT = (quantity * priceHT) - remise
            netHT * (tva / 100)
        }
        return totalTVA.toString()
    }

    /**
     * Save BL with sync enabled following old app pattern
     * This ensures the BL is automatically synchronized like in the old app
     */
    suspend fun saveBLWithSync(
        ticketToSave: Ticket,
        ligneTicketsToSave: List<LigneTicket>,
        proCaisseLocalDb: ProCaisseLocalDb,
        instantSyncTrigger: InstantSyncTrigger
    ) {
        // Save ticket with sync enabled - this will trigger automatic sync
        proCaisseLocalDb.insertTicketWithSync(ticketToSave, instantSyncTrigger)

        // Save line tickets with sync enabled
        proCaisseLocalDb.insertLignesTicketWithSync(ligneTicketsToSave, instantSyncTrigger)
    }
}

/**
 * Validation result data class
 */
data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String>
)