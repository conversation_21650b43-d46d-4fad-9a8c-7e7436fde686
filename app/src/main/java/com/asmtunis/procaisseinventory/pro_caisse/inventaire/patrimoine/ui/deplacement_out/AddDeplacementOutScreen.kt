package com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.deplacement_out

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.SetNumSerieView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.updateInvPatQty
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column.ThreeColumnTableWithImage
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState

@Composable
fun AddDeplacementOutScreen (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    clientId: String,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel : MainViewModel,
    barCodeViewModel : BarCodeViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    settingViewModel: SettingViewModel,
    proCaisseViewModels: ProCaisseViewModels
){
    val invPatViewModel = proCaisseViewModels.invPatViewModel
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM

    val context = LocalContext.current
    val imageList = mainViewModel.imageList
    val selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine
    val marqueList = mainViewModel.marqueList
    val haveCamera = dataViewModel.getHaveCameraDevice()

   val articleMapByBarCode = mainViewModel.articleMapByBarCode
   val utilisateur = mainViewModel.utilisateur
    val clientList = mainViewModel.clientList
    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
   val codeM = mainViewModel.codeM
   val patrimoineVerificationState = selectPatrimoineVM.patrimoineVerificationState
   val selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList
   val barCodeInfo = barCodeViewModel.barCodeInfo

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM,
            )
        },
        floatingActionButton = {
            Column {
                FloatingActionButton(
                    onClick = {
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    }) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(id = R.string.cd_achat_button)
                    )
                }


                Spacer(modifier = Modifier.height(12.dp))
                FloatingActionButton(
                    onClick = {

                            invPatViewModel.saveInvPat(
                                articleMapByBarCode = articleMapByBarCode,
                                codeM = codeM,
                                listSelectedPatrimoine = selectedPatrimoineList,
                                exercice = mainViewModel.getExerciceCode(),
                                client = clientByCode,
                                utilisateur = utilisateur,
                                typeInv = invPatViewModel.typeInvetaireState,
                                devEtat = Constants.PATRIMOINE,
                                onComplete = { navigateUp() }
                            )


                    }) {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = stringResource(id = R.string.cd_achat_button)
                    )
                }
            }

        }
    ) { padding ->
        if (selectPatrimoineVM.showSetNumeSerie)
            SetNumSerieView(
                haveCamera = haveCamera,
                articleMapByBarCode = articleMapByBarCode,
                marqueList = marqueList,
                selectedPatrimoine = selectedPatrimoine,
                selectedPatrimoineList = selectedPatrimoineList,
                onNumSerieChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(numSerie = it))
                },
                onDismiss = {

                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                    selectPatrimoineVM.resetPatrimoineVerificationState()
                            selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                            },
                onConfirm = {
                    val controlInvPat = ControleInventaire(
                        LG_DEV_NumSerie = selectedPatrimoine.numSerie,
                        DEV_CodeClient = clientByCode.cLICode,
                        DEV_info3 = invPatViewModel.typeInvetaireState
                    )
                    selectPatrimoineVM.patrimoineVerification(
                        baseConfig = mainViewModel.selectedBaseconfig,
                        controlPatrimoine = controlInvPat
                    )

                },
                onAddInvPat = {
                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                val codeArt = selectPatrimoineVM.invPatByNumSerie.entries.first().value.first().lGDEVCodeArt
                    updateInvPatQty(
                        imageList = imageList,
                        articleCode = codeArt,
                        numeSerie = selectedPatrimoine.numSerie,
                        patrimoineVerificationState = patrimoineVerificationState,
                        selectedPatrimoineList = selectedPatrimoineList,
                        addItemToSelectedPatrimoineList = {
                            selectPatrimoineVM.addItemToSelectedPatrimoineList(it)
                        },
                        marque = marqueList.firstOrNull { it.mARCode == selectedPatrimoine.marqueCode }?: Marque(),
                        note = selectedPatrimoine.note
                    )


                    selectPatrimoineVM.resetPatrimoineVerificationState()

                },
                onBareCodeScan = {
                    openBareCodeScanner(
                        navigate = { navigate(it) },
                        onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                    )
                },
                barCodeInfo = barCodeInfo,
                patrimoineVerificationState = patrimoineVerificationState,
                showDropDownMenuComposable = false,
                onNoteChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(note = it))
                }

            )

        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            Text(text = clientByCode.cLINomPren)
            ThreeColumnTableWithImage(
                articleMapByBarCode = articleMapByBarCode,
                marqueList = marqueList,
                haveCamera = haveCamera,
                canModify = true,
                selectedPatrimoineList = selectedPatrimoineList,
                onPress = {
                    //TODO Maybe show more detail : TVA / DISCOUNT / . . .
                }
            )
        }
    }
}
