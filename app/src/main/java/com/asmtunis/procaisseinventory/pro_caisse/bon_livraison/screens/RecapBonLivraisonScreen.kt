package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.InsertInvitation
import androidx.compose.material.icons.twotone.Numbers
import androidx.compose.material.icons.twotone.Print
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.calculateBonLivraisonTotal
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertDoubleToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.BonLivraisonViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.TotalPriceView
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column.FiveColumnTable
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail


@Composable
    fun RecapBonLivraisonScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    bonLivraisonVM: BonLivraisonViewModel,
    dataViewModel: DataViewModel,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    mainViewModel: MainViewModel,
    wifiPrintVM: WifiPrintViewModel,
    sunmiPrintManager: SunmiPrintManager,
    settingVM: SettingViewModel,
    networkViewModel: NetworkViewModel,
    navDrawerViewmodel: NavigationDrawerViewModel,
    ) {
    val context = LocalContext.current
    val scrollState = rememberScrollState()
    val state = bonLivraisonVM.bonTransfertListstate





    val selectedArticleList = bonLivraisonVM.selectedArticleList
    val bonLivraison = bonLivraisonVM.bonLivraison
    val ligneBonLivraisonWithArticle = bonLivraisonVM.ligneBonLivraisonWithArticle



    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val clientByCode = mainViewModel.clientByCode
    val tvaList = mainViewModel.tvaList

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingVM.isDarkTheme)

    val utilisateur = mainViewModel.utilisateur
    val firstTimeConnected = printViewModel.firstTimeConnected






//    LaunchedEffect(key1 = Unit){
 //        selectArtMobilityVM.resetSelectedMobilityArticles()
//
//
//
//
//            for (i in ligneBonLivraison.indices) {
//                //  val tva = mainViewModel.tvaList.firstOrNull { it.tVACode == ligneBonLivraison[i].lTTVA}
//
//                val lgBonLiv = ligneBonLivraison[i].ligneTicket
//
//                val article = ligneBonLivraison[i].article?:
//                Article(
//                    aRTCodeBar = lgBonLiv?.codeBarFils?:"N/A $i",
//                    aRTCode = lgBonLiv?.lTCodArt?:"N/A $i",
//                    aRTDesignation = lgBonLiv?.codeBarFils?:"N/A $i",
//                    aRTTVA = stringToDouble(lgBonLiv?.lTTVA?:"0.0"),
//                    pvttc = stringToDouble(lgBonLiv?.lTPrixVente?:"0")
//                )
//                val selectedArticle = SelectedArticle(
//                    article = article,
//                    //  tva = tva,
//                    quantity=  lgBonLiv!!.lTQte,
//                    prixCaisse = lgBonLiv.lTPrixVente,
//                    discount = lgBonLiv.lTTauxRemise,
//                    mntDiscount = lgBonLiv.lTRemise,
//                    lTMtTTC = lgBonLiv.lTMtTTC,
//                    lTMtBrutHT = lgBonLiv.lTMtHT,
//                    prixVente = lgBonLiv.lTPrixVente,
//                    tva = Tva(),
//                    mntTva = ""
//                )
//                    selectArtMobilityVM.setConsultationSelectedArticleMobilityList(selectedArticle = selectedArticle)
//            }
//    }


    LaunchedEffect(key1 = firstTimeConnected){
        if(!firstTimeConnected) return@LaunchedEffect

        // Direct printing without note functionality
        when {
            printParams.useSunmiPrinter -> {
                sunmiPrintManager.printRecapTicket(
                    context = context,
                    ticket = bonLivraison.first(),
                    listLigneTicket = ligneBonLivraisonWithArticle,
                    client = clientByCode,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
            }
            printParams.printViaWifi -> {
                wifiPrintVM.createReceapTicketPDFFile(
                    context = context,
                    ticket = bonLivraison.first(),
                    listLigneTicketWithArticle = ligneBonLivraisonWithArticle,
                    client = clientByCode
                ) {}
            }
            else -> {
                // Bluetooth printing
                printViewModel.printRecapTicket(
                    context = context,
                    ticket = bonLivraison.first(),
                    listLigneTicket = ligneBonLivraisonWithArticle,
                    client = clientByCode,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
            }
        }

        printViewModel.onFirstTimeConnectedChange(firstConnect = false)
    }

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    popBackStack()
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(id = R.string.RecapeBL),
                actions = {
                    IconButton(
                        onClick = {

                            // Direct printing without note functionality
                            when {
                                printParams.useSunmiPrinter -> {
                                    sunmiPrintManager.printRecapTicket(
                                        context = context,
                                        ticket = bonLivraison.first(),
                                        listLigneTicket = ligneBonLivraisonWithArticle,
                                        client = clientByCode,
                                        utilisateur = utilisateur,
                                        printParams = printParams
                                    )
                                }
                                printParams.printViaWifi -> {
                                    wifiPrintVM.createReceapTicketPDFFile(
                                        context = context,
                                        ticket = bonLivraison.first(),
                                        listLigneTicketWithArticle = ligneBonLivraisonWithArticle,
                                        client = clientByCode
                                    ) {}
                                }
                                else -> {
                                    // Bluetooth printing
                                    printViewModel.printRecapTicket(
                                        context = context,
                                        ticket = bonLivraison.first(),
                                        listLigneTicket = ligneBonLivraisonWithArticle,
                                        client = clientByCode,
                                        utilisateur = utilisateur,
                                        printParams = printParams
                                    )
                                }
                            }
                        }
                    ) {
                        Icon(
                            imageVector = Icons.TwoTone.Print,
                            contentDescription = stringResource(id = R.string.icn_search_clear_content_description)
                        )
                    }
                }
            )
        }
    ) { padding ->
        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
           //     .verticalScroll(scrollState)
                .padding(padding)
        ) {
            if(printViewModel.openPrintInfoDialogue)
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )

            Spacer(modifier = Modifier.height(9.dp))

            ItemDetail(
                modifier = Modifier.fillMaxWidth(0.95f),
                title = stringResource(id = R.string.Nombre_des_tickets_title),
                dataText = bonLivraison.first().nbreTicketsRecap?: "N/A",
                icon = Icons.TwoTone.Numbers,
                onClick = {

                }
            )
            Spacer(modifier = Modifier.height(9.dp))

            ItemDetail(
                modifier = Modifier.fillMaxWidth(0.95f),
                title = bonLivraison.first().tIKIdSCaisse,
                dataText = bonLivraison.first().tIKDateHeureTicket.substringBefore("."),
                icon = Icons.TwoTone.InsertInvitation,
                )
            Spacer(modifier = Modifier.height(9.dp))

            FiveColumnTable(
                selectedListArticle = selectedArticleList,
                onPress = { selectArtMobilityVM.setSelectedArticl(article = it.article, tvaList = tvaList) },
                onLongPress = {},
                onTap = { mainViewModel.setAddNewProductDialogueVisibility(true) },
                firstColumn = { item->
                   articleMapByBarCode[item.article.aRTCode]?.aRTDesignation?: context.resources.getString(R.string.article_introvable, " ("+ item.article.aRTCode+")")
                },
                secondColumn = { removeTrailingZeroInDouble(it.prixCaisse) },
                thirdColumn = { removeTrailingZeroInDouble(it.quantity) },
                forthColumn = { convertDoubleToDoubleFormat(stringToDouble(it.lTMtTTC)) },
                infoText = { TableTextUtils.infoText(selectedArticle = it) }
            )

            Spacer(modifier = Modifier.weight(1f))
            TotalPriceView(
                totalPrice = calculateBonLivraisonTotal(
                    tIKMtTTC = bonLivraison.first().tIKMtTTC,
                    tIKMtRemise = bonLivraison.first().tIKMtRemise,
                    tIKTimbre = bonLivraison.first().tIKTimbre,
                    mntRevImp = bonLivraison.first().mntRevImp,
                    includeDiscount = true,
                    includeStamps = true,
                    includeTax = true
                ).toString(),
                totalPriceAfterDiscount = calculateBonLivraisonTotal(
                    tIKMtTTC = bonLivraison.first().tIKMtTTC,
                    tIKMtRemise = bonLivraison.first().tIKMtRemise,
                    tIKTimbre = bonLivraison.first().tIKTimbre,
                    mntRevImp = bonLivraison.first().mntRevImp,
                    includeDiscount = false,
                    includeStamps = true,
                    includeTax = true
                ).toString(),
                showPriceWithDiscount = stringToDouble(bonLivraison.first().tIKMtRemise ?: "0") != 0.0
            )


            Spacer(modifier = Modifier.height(12.dp))


        }


    }

}


