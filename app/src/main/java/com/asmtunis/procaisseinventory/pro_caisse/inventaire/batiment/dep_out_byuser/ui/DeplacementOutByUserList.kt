package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringPlural
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUserWithImmobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.shared_ui_components.DeplacementStatusBadge
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel

@Composable
fun DeplacementOutByUserList(
    selectedBaseconfig: BaseConfig,
    immobilisationList: List<Immobilisation>,
    isConnected: Boolean,
    listState: LazyListState,
    filteredZoneConsomation: Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>,
    deplacementOutByUser: MutableList<DeplacementOutByUserWithImmobilisation>,
    ligneBonCommande: MutableList<LigneBonCommande>,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    onItemClick: (index: DeplacementOutByUserWithImmobilisation) -> Unit
) {
    // val state = remember { deplacementOutByUserViewModel.state.value }

    val context = LocalContext.current


    val isRefreshing  = getProCaisseDataViewModel.deplacementOutByUserResponseState.loading

    PullToRefreshLazyColumn(
        items = deplacementOutByUser,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !deplacementOutByUser.any { !(it.immobilisation?.isSync?: true) } && isConnected,
        onRefresh = {

            getProCaisseDataViewModel.getDeplacementOutByUser(
                baseConfig = selectedBaseconfig,
                listImmobilisation =  immobilisationList
            )
        },
        key = {depOutByUser ->
            depOutByUser.deplacementOutByUser!!.dEVNum
        },
        content = { depOutByUser ->
            OutlinedCard(
                modifier = Modifier.padding(start = 9.dp,end = 9.dp, top = 12.dp)
            )  {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                    modifier =
                    Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .padding(top = 12.dp, start = 9.dp)
                        .clickable {
                           // onItemClick(deplacementOutByUser.indexOf(depOutByUser))
                            onItemClick(depOutByUser)
                        },
                    // .background("#063041".color)
                ) {

                    Column(
                        modifier =
                        Modifier
                            //  .background(colorResource(id =if(state.lists[index].solde.toDouble() < 0.0) R.color.teal_700 else R.color.white ))
                            .wrapContentSize()
                            .weight(1f),
                        // .padding(padding) ,
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.Start,
                    ) {
                        // Status and document number row
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceBetween,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // Document number on the left
                            Text(
                                text = depOutByUser.deplacementOutByUser!!.dEVNum,
                                fontSize = MaterialTheme.typography.titleMedium.fontSize,
                                fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                                maxLines = 1,
                            )

                            // Status text on the right
                            val (statusText, statusColor) = when (depOutByUser.deplacementOutByUser?.dEVEtatBon) {
                                "1", "En Instance" -> Pair(stringResource(id = R.string.en_instance), Color.Red)
                                "2", "Validée" -> Pair(stringResource(id = R.string.validee), Color.Green)
                                else -> Pair("", Color.Transparent)
                            }

                            if (statusText.isNotEmpty()) {
                                Text(
                                    text = statusText,
                                    color = statusColor,
                                    fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }



                        Text(
                            text = (depOutByUser.immobilisation?.cLINomPren?: depOutByUser.immobilisation?.cLICode?: "") + " ("+depOutByUser.deplacementOutByUser!!.dEVCodeClient+")",
                            fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                            fontWeight = MaterialTheme.typography.bodyMedium.fontWeight,
                        )

                        Text(
                            text =
                            stringPlural(
                                nbr = filteredZoneConsomation[depOutByUser]?.size ?: 0,
                                single = stringResource(id = R.string.article_title),
                                plural = stringResource(id = R.string.article_field_title)
                            ),
                            fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                            fontWeight = MaterialTheme.typography.bodyMedium.fontWeight,
                        )

                        Text(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(end = 12.dp),
                            text = depOutByUser.deplacementOutByUser?.dEVDDm?.replace(".000", "")?.removeSuffix(" 00:00:00")?: "",
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                            textAlign = TextAlign.End
                        )
                    }
                }
            }
        },
    )

}