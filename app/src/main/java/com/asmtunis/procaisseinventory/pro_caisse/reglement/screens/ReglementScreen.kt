package com.asmtunis.procaisseinventory.pro_caisse.reglement.screens

import NavDrawer
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.REGLER_ACPT
import com.asmtunis.procaisseinventory.core.Globals.VENTE_DU_JOUR
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.wifi.WifiPrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.reglement.ReglementUtils.getReglementNumber
import com.asmtunis.procaisseinventory.pro_caisse.reglement.ReglementUtils.getReglementType
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.pro_caisse.reglement.view_model.ReglementCaisseViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.dialogues.DetailReglementDialogue
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.PaymentUtils.getPaymentModeDetailed
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.rememberToasterState
import kotlinx.coroutines.launch


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReglementScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,

    mainViewModel: MainViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    wifiPrintVM: WifiPrintViewModel,
    sunmiPrintManager: SunmiPrintManager,
    settingVM: SettingViewModel,
    dataViewModel: DataViewModel,

    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    proCaisseViewModels: ProCaisseViewModels
    ) {

    val syncReglementViewModel = syncProcaisseViewModels.syncReglementViewModel
    val reglementCaisseViewModel = proCaisseViewModels.reglementCaisseViewModel
    val paymentViewModel = proCaisseViewModels.paymentViewModel

    val uiWindowState = settingVM.uiWindowState
    val density = LocalDensity.current

    val utilisateur = mainViewModel.utilisateur


    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val context = LocalContext.current
    val reglementCaisseState = reglementCaisseViewModel.reglementCaisseFilterListState
    val listState = rememberLazyListState()
    val reglementCaisseList = reglementCaisseViewModel.reglementCaisseFilterListState.lists
    val listOrder = reglementCaisseViewModel.reglementCaisseFilterListState.listOrder
    val prefixList = mainViewModel.prefixList
    val listFilter = reglementCaisseViewModel.reglementCaisseFilterListState.search
    val filterList = context.resources.getStringArray(R.array.reglement_caisse_filter)
    val scope = rememberCoroutineScope()

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingVM.isDarkTheme)

    val isConnected = networkViewModel.isConnected
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData

    val listTicket = mainViewModel.listTicket

    val selectedSessionCaisse =  navigationDrawerViewModel.sessionCaisse

    val searchTextState = reglementCaisseViewModel.searchTextState
    val showSearchView = reglementCaisseViewModel.showSearchView

    val clientList = mainViewModel.clientList
    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode

    val selectedReglementCaisse = reglementCaisseViewModel.selectedReglementCaisse
    val reglementCaisse = selectedReglementCaisse.reglementCaisse?: ReglementCaisse()

    LaunchedEffect(key1 = Unit) {
        reglementCaisseViewModel.setCurrentUtilisateur(utilisateur) // Set current user for station filtering
        reglementCaisseViewModel.onSearchValueChange(TextFieldValue(clientByCode.cLICode))
        reglementCaisseViewModel.onEventReglementCaisse(ListEvent.ListSearch(ListSearch.SecondSearch()))

        // OFFLINE-FIRST: Load from local database immediately
        // Data will be displayed instantly from local database
        // Background sync will update the local database when connected
        android.util.Log.d("ReglementScreen", "📱 Loading reglement from local database first...")

        // Trigger background sync to update local database when connected
        if (networkViewModel.isConnected) {
            android.util.Log.d("ReglementScreen", "🔄 Triggering background sync for reglement...")
            getProCaisseDataViewModel.getAllReglementForStation(
                baseConfig = selectedBaseconfig,
                utilisateur = utilisateur,
                exerciceCode = mainViewModel.exerciceList.firstOrNull()?.exerciceCode ?: ""
            )
        } else {
            android.util.Log.d("ReglementScreen", "📴 Offline mode - using local data only")
        }
    }
    LaunchedEffect(
        key1 = searchTextState.text,
        key2 = reglementCaisseState.lists,
        key3 = reglementCaisseState.search
    ) {
        reglementCaisseViewModel.filterReglementCaisse(reglementCaisseState)
    }

    LaunchedEffect(key1 = printViewModel.firstTimeConnected){
        if(!printViewModel.firstTimeConnected) return@LaunchedEffect

        printViewModel.awaitPrint (
                context = context,
                toPrint = {
                    printViewModel.printReglement(
                        context = context,
                        reglementCaisse = selectedReglementCaisse,
                        utilisateur = utilisateur,
                        printParams = printParams,
                        prefixList = prefixList
                    )
                }
            )
        reglementCaisseViewModel.onShowCustomModalBottomSheetChange(false)
        printViewModel.onFirstTimeConnectedChange(firstConnect = false)

    }

    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navigationDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingVM
    ) {
        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    onNavigationClick = {
                        // Navigate to shortcuts screen instead of opening drawer
                        navigate(com.asmtunis.procaisseinventory.core.navigation.HomePageRoute)
                    },
                    title = stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title),
                    titleVisibilty = !showSearchView && searchTextState.text.isEmpty(),
                    actions = {
                        SearchSectionComposable(
                            label = context.getString(
                                R.string.filter_by,
                                when (listFilter) {
                                    is ListSearch.FirstSearch -> filterList[0]
                                    is ListSearch.SecondSearch -> filterList[1]
                                    else -> filterList[2]
                                }
                            ),
                            searchVisibility = showSearchView || searchTextState.text.isNotEmpty(),
                            searchTextState = searchTextState,
                            onSearchValueChange = {
                                reglementCaisseViewModel.onSearchValueChange(TextFieldValue(it))
                                if (it == "") {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                   // mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowSearchViewChange = {
                                reglementCaisseViewModel.onShowSearchViewChange(it)
                                if (!it) {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    reglementCaisseViewModel.onSearchValueChange(TextFieldValue(""))
                                 //   mainViewModel.onSelectedClientChange(Client())
                                }
                            },

                            onShowCustomFilterChange = {
                                reglementCaisseViewModel.onShowCustomFilterChange(it)
                            }
                        )

                    },

                    )
            },
            floatingActionButton = {
                SnapScrollingButton(
                    isScrollInProgress = listState.isScrollInProgress,
                    isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15,
                    density = density,
                    animateScrollToItem = {
                        listState.animateScrollToItem(index = it)
                    }
                )
            }
        ) { padding ->
            if(printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }


            if (reglementCaisseViewModel.showCustomModalBottomSheet) {
                CustomModalBottomSheet(
                    title = getReglementNumber(
                        reglementCaisse = reglementCaisse,
                        ticket = selectedReglementCaisse.ticket,
                        context = context,
                        prefixList = prefixList
                    ) + if(reglementCaisse.isSync) "\n"+ reglementCaisse.rEGCCode else "",
                    status = reglementCaisse.status,
                    remoteResponseState = syncReglementViewModel.reglementCaisseUpdateState,
                    showDeleteIcon = !reglementCaisse.isSync,
                    onDismissRequest= { reglementCaisseViewModel.onShowCustomModalBottomSheetChange(false) },
                    onDeleteRequest= { paymentViewModel.deleteReglement(reglementCaisseWithTicketAndClient = selectedReglementCaisse) },
                    onPrintRequest= {
                        PrintFunctions.print (
                            navigate = { navigate(it) },
                            context = context,
                            toaster = toaster,
                            printParams = printParams,
                            printViewModel = printViewModel,
                            bluetoothVM = bluetoothVM,
                            sunmiPrintManager = sunmiPrintManager,
                            toPrintBT = {
                                printViewModel.printReglement(
                                    context = context,
                                    reglementCaisse = selectedReglementCaisse,
                                    utilisateur = utilisateur,
                                    printParams = printParams,
                                    prefixList = prefixList
                                )
                            },
                            toPrintWifi = {
                                wifiPrintVM.createReglementPDFFile(
                                    context = context,
                                    reglementCaisseWithTicketAndClient = selectedReglementCaisse,
                                    listener = {}
                                )
                            },
                            toPrintSunmi = {
                                sunmiPrintManager.printReglement(
                                    context = context,
                                    reglementCaisse = selectedReglementCaisse,
                                    utilisateur = utilisateur,
                                    printParams = printParams,
                                    prefixList = prefixList
                                )
                            }
                        )

                    },
                    onSyncRequest = {
                        syncReglementViewModel.syncReglementLibre(selectedRegCaisse = selectedReglementCaisse)
                    }
                )
            }


            if (reglementCaisseViewModel.showReglementDetail) {
                DetailReglementDialogue(
                    listTicket = listTicket,
                    reglementCaisse = reglementCaisse,
                    onDismiss = {
                        reglementCaisseViewModel.onSelectedReglementCaisseChange(ReglementCaisseWithTicketAndClient())
                        reglementCaisseViewModel.onShowReglementDetailChange(false)
                    }
                )
            }




//            val isVisible = floatingBtnIsVisible(
//                       listeSize = reglementCaisseList.size,
//                       canScrollForward = listState.canScrollForward
//                   )





            if (reglementCaisseViewModel.showCustomFilter) {
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.reglement_caisse_order),
                    onShowCustomFilterChange = {
                        reglementCaisseViewModel.onShowCustomFilterChange(false)
                    },
                    onEvent = {
                        reglementCaisseViewModel.onEventReglementCaisse(event = it)
                    },
                    customFilterContent = {
                        val state = reglementCaisseViewModel.reglementCaisseFilterListState
                        val typeReglementList = listOf(VENTE_DU_JOUR, REGLER_ACPT)

                        /**
                         * if change "Espéce", "Chéque", "Traite", "Carte Bancaire" then change also in ReglementCaissedao
                         */
                        val typePaymentList = context.resources.getStringArray(R.array.type_payment)
                        FilterSectionComposable(
                            title = stringResource(id = R.string.filter_by_type_paiement),
                            currentFilterLable = state.filterByTypePayment,
                            onAllEvent = { reglementCaisseViewModel.onEventReglementCaisse(ListEvent.FirstCustomFilter("")) },
                            onEvent = {
                                reglementCaisseViewModel.onEventReglementCaisse(
                                    ListEvent.FirstCustomFilter(
                                        typePaymentList[it]
                                    )
                                )
                            },

                            filterCount = typePaymentList.size,
                            customFilterCode = { typePaymentList[it] },
                            filterLabel = { typePaymentList[it] }
                        )


                        FilterSectionComposable(
                            title = stringResource(id = R.string.filter_by_type_reglement),
                            currentFilterLable = state.filterByTypeReglement,
                            onAllEvent = { reglementCaisseViewModel.onEventReglementCaisse(ListEvent.SecondCustomFilter("")) },
                            filterCount = typeReglementList.size,
                            customFilterCode = { typeReglementList[it] },
                            filterLabel = { typeReglementList[it] },
                            onEvent = { reglementCaisseViewModel.onEventReglementCaisse(ListEvent.SecondCustomFilter(typeReglementList[it])) }
                        )
                    }
                )
            }

            Column(
             verticalArrangement = if (reglementCaisseList.isEmpty() && !getProCaisseDataViewModel.reglementCaisseState.loading) Arrangement.Center else Arrangement.Top,
             horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxSize().padding(padding)
            ) {


                if (getProCaisseDataViewModel.reglementCaisseState.loading)
                    LottieAnim(lotti = R.raw.loading, size = 250.dp)
                else {
                    if (reglementCaisseList.isNotEmpty())
                        ListReglementCaisse(
                            isConnected = isConnected,
                            selectedBaseconfig = selectedBaseconfig,
                            sCIdSCaisse = selectedSessionCaisse.sCIdSCaisse,
                            exercice = mainViewModel.exerciceList.firstOrNull()?.exerciceCode?:"N/A",
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            listState = listState,
                            prefixList = prefixList,
                            reglementCaisseList = reglementCaisseList,
                            reglementCaisseViewModel = reglementCaisseViewModel,
                            listTicket = listTicket,
                            utilisateur = utilisateur
                        )
                    else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                }

            }

        }


    }
}

@Composable
fun ListReglementCaisse(
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    sCIdSCaisse: String,
    exercice: String,
    prefixList: List<Prefixe>,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    listState: LazyListState,
    listTicket: List<Ticket>,
    reglementCaisseList: List<ReglementCaisseWithTicketAndClient>,
    reglementCaisseViewModel: ReglementCaisseViewModel,
    utilisateur: Utilisateur
) {
    val context = LocalContext.current

    val isRefreshing  = getProCaisseDataViewModel.reglementCaisseState.loading

    PullToRefreshLazyColumn(
        items = reglementCaisseList,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !reglementCaisseList.any { it.reglementCaisse?.isSync != true } && isConnected,
        onRefresh = {
            /**
             * OFFLINE-FIRST: Background sync to update local database
             * UI continues to show local data during sync
             */
            if (isConnected) {
                android.util.Log.d("ReglementScreen", "🔄 Pull-to-refresh: Triggering background sync for reglement...")
                getProCaisseDataViewModel.getAllReglementForStation(
                    baseConfig = selectedBaseconfig,
                    utilisateur = utilisateur,
                    exerciceCode = exercice
                )
            } else {
                android.util.Log.d("ReglementScreen", "📴 Pull-to-refresh: Offline mode - no sync available")
            }
        },
        key = { regCaisseList -> regCaisseList.reglementCaisse?.id?: regCaisseList },
        content = { regCaisseList ->
            val regCaisse = regCaisseList.reglementCaisse?: ReglementCaisse()
          val typePayment =  getPaymentModeDetailed(
                context = context,
                rEGCMntEspece = regCaisse.rEGCMntEspece?:0.0,
                rEGCMntCarteBancaire = regCaisse.rEGCMntCarteBancaire?:0.0,
                rEGCMntTraite = regCaisse.rEGCMntTraite?:0.0,
                rEGCMntCheque = regCaisse.rEGCMntCheque?:0.0
          )

                ListItem(
                firstText = (regCaisse.rEGCNomPrenom ?: ( context.resources.getString(R.string.client_info,regCaisse.rEGCCodeClient))),
                secondText = getReglementNumber(
                    reglementCaisse = regCaisse,
                    ticket = regCaisseList.ticket,
                    context = context,
                    prefixList = prefixList
                ),
                thirdText = getReglementType(
                       reglementCaisse = regCaisse,
                       listTicket = listTicket,
                       context = context
                   ),
                forthText = convertStringToPriceFormat(regCaisse.rEGCMontant.toString()),
                dateText = "(" + typePayment + ")  " + (regCaisse.rEGCDateReg ?: "N/A"),
                isSync = regCaisse.isSync,
                status = regCaisse.status,
                onItemClick = {
                        reglementCaisseViewModel.onSelectedReglementCaisseChange(regCaisseList)
                        reglementCaisseViewModel.onShowReglementDetailChange(true)
                    },
                onMoreClick = {
                    reglementCaisseViewModel.onSelectedReglementCaisseChange(regCaisseList)
                    reglementCaisseViewModel.onShowCustomModalBottomSheetChange(true)
                }
            )
        },
    )
}





