package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.BON_COMMANDE_TABLE
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.LIGNE_BON_COMMANDE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import kotlinx.coroutines.flow.Flow


@Dao
interface BonCommandeDAO {
    @Transaction
    @Query("SELECT * FROM $BON_COMMANDE_TABLE " +
            "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon " +
            "where DEV_Station =:station and DEV_Etat LIKE 'BCC_client' " +
            "order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc " +
            "LIMIT :limit OFFSET :offset")
    fun getAllByStationPaginated(station: String, limit: Int, offset: Int): Flow<Map<BonCommande, List<LigneBonCommande>>>

    @Transaction
    @Query("SELECT * FROM $BON_COMMANDE_TABLE " +
            "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon " +
            "where DEV_Station =:station and DEV_Etat LIKE 'BCC_client' " +
            "order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc " +
            "LIMIT 100")
    fun getAllByStation(station: String): Flow<Map<BonCommande, List<LigneBonCommande>>>

    @Transaction
    @Query("SELECT * FROM $BON_COMMANDE_TABLE " +
            "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon " +
            "order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    fun getAll(): Flow<Map<BonCommande, List<LigneBonCommande>>>

    @get:Query("SELECT * FROM $BON_COMMANDE_TABLE order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    val allMutable: Flow<List<BonCommande>>

    @Query("SELECT * FROM $BON_COMMANDE_TABLE where DEV_Station =:station and DEV_Etat =:BCType order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    fun getByStationMutable(station: String, BCType: String): Flow<List<BonCommande>>

    @Transaction
    @Query("SELECT *  FROM $BON_COMMANDE_TABLE " +
            "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon " +

            "where DEV_Station =:station and DEV_CodeClient= :codeClient " +
            "   order by strftime('%Y-%m-%d %H-%M',$BON_COMMANDE_TABLE.DEV_Date) desc")
    fun getByCodeCltAndStation(codeClient: String, station : String): Flow<Map<BonCommande, List<LigneBonCommande>>>


    @Query("SELECT * FROM $BON_COMMANDE_TABLE where DEV_Num= :codeCommande order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    fun getByNumOrderedBydate(codeCommande: String): Flow<List<BonCommande>>

    @get:Query("SELECT * FROM $BON_COMMANDE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') order by strftime('%Y-%m-%d %H-%M',DEV_Date)")
    val noSynced: Flow<List<BonCommande>>

    @Query("UPDATE $BON_COMMANDE_TABLE SET DEV_CodeClient = :code_client where DEV_CodeClient = :oldCodeClient")
    fun updateCodeClient(code_client: String, oldCodeClient: String)


    @Query("UPDATE $BON_COMMANDE_TABLE SET DEV_Client_Name = :clientName where DEV_CodeClient = :codeclient")
    fun updateNameClient(codeclient: String, clientName: String)

    @Query("SELECT ifnull(MAX(cast(substr(DEV_Num,length(:prefix) + 1 ,length('DEV_Num'))as integer)),0)+1 FROM $BON_COMMANDE_TABLE WHERE substr(DEV_Num, 0 ,length(:prefix)+1) = :prefix")
    fun getNewCode(prefix: String): Flow<String>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: BonCommande)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<BonCommande>)

    @get:Query("SELECT count(*) FROM $BON_COMMANDE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val countNonSync: Flow<Int>

    @Query("UPDATE $BON_COMMANDE_TABLE SET Status = :status, DEV_DDm =:devDdm  where DEV_Code_M = :bonCommandeM")
    fun updateStatus(status : String, bonCommandeM: String, devDdm: String)

    @get:Query("SELECT count(*) FROM $BON_COMMANDE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMubtale: Flow<Int>

    @Query("UPDATE $BON_COMMANDE_TABLE SET isSync = 1, Status= 'SELECTED', DEV_Num = :bonCommandeNum where DEV_Code_M = :bonCommandeNumM")
    fun setSynced(bonCommandeNum : String, bonCommandeNumM: String)

    @Transaction
    @Query("SELECT * FROM $BON_COMMANDE_TABLE " +
            "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon " +
            "where $BON_COMMANDE_TABLE.isSync=0 and $BON_COMMANDE_TABLE.DEV_info3 Like '' and ($BON_COMMANDE_TABLE.Status='INSERTED'  or $BON_COMMANDE_TABLE.Status='UPDATED') order by DEV_Num desc")
    fun getNotSynced(): Flow<Map<BonCommande, List<LigneBonCommande>>>


    @Query("DELETE FROM $BON_COMMANDE_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $BON_COMMANDE_TABLE where DEV_Code_M= :codeM/* and BON_LIV_Exerc=:exercie*/")
    fun deleteByCodeM(codeM: String)

    @Query("SELECT *  FROM $BON_COMMANDE_TABLE where DEV_Code_M= :codeCommande and BON_LIV_Exerc=:exercie")
    fun getListByCodeM(codeCommande: String, exercie: String): Flow<BonCommande>

    @Query("SELECT *  FROM $BON_COMMANDE_TABLE where DEV_Num= :codeCommande and BON_LIV_Exerc=:exercie")
    fun getByCode(codeCommande: String, exercie: String): Flow<BonCommande>


    @get:Query("SELECT count(*) FROM $BON_COMMANDE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountNonMubtale: Flow<Int>

    @Query("SELECT count(*) FROM $BON_COMMANDE_TABLE where DEV_Station =:station AND DEV_Etat like 'BCC_client'")
    fun getAllCountBySession(station: String): Flow<Int>

    @Query("SELECT count(DISTINCT $BON_COMMANDE_TABLE.DEV_Num) FROM $BON_COMMANDE_TABLE " +
            "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon " +
            "where DEV_Station =:station and DEV_Etat LIKE 'BCC_client'")
    fun getAllByStationCount(station: String): Flow<Int>

    @Query("SELECT count(*) FROM $BON_COMMANDE_TABLE")
    fun getCount(): Flow<Int>

    @Query("SELECT count(*) FROM $BON_COMMANDE_TABLE where DEV_Station =:station")
    fun getAllCountBySessionMutable(station: String): Flow<Int>

    @Query("UPDATE $BON_COMMANDE_TABLE SET DEV_Observation = :devObservation where DEV_Num = :devNum  and BON_LIV_Exerc=:exercie")
    fun updateObservation(devObservation: String, devNum: String, exercie: String)




    @Transaction
    @Query(
        "SELECT * FROM $BON_COMMANDE_TABLE " +
                "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon" +
                " WHERE $BON_COMMANDE_TABLE.DEV_Client_Name  LIKE '%' || :searchString || '%' " +
                " AND DEV_Etat LIKE 'BCC_client' and DEV_Station =:station" +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 1 THEN (CAST (DEV_MntTTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 2 THEN (CAST (DEV_MntTTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_COMMANDE_TABLE.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_COMMANDE_TABLE.DEV_Date) END DESC "
    )
    fun filterByNomClient(searchString: String, sortBy: String, isAsc: Int, station : String): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>>

    @Transaction
    @Query(
        "SELECT * FROM $BON_COMMANDE_TABLE " +
                "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon" +
                " WHERE $BON_COMMANDE_TABLE.DEV_Num LIKE '%' || :searchString || '%' " +
                 "  AND DEV_Etat LIKE 'BCC_client' and DEV_Station =:station"+


                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 1 THEN (CAST (DEV_MntTTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 2 THEN (CAST (DEV_MntTTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_COMMANDE_TABLE.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_COMMANDE_TABLE.DEV_Date) END DESC "
    )
    fun filterByBonCommandeNum(searchString: String,  sortBy: String, isAsc: Int, station : String): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>>


    @Transaction
    @Query(
        "SELECT * FROM $BON_COMMANDE_TABLE " +
                "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon" +
                " WHERE $BON_COMMANDE_TABLE.DEV_CodeClient LIKE '%' || :searchString || '%'" +
               "  AND DEV_Etat LIKE 'BCC_client' and DEV_Station =:station" +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 1 THEN (CAST (DEV_MntTTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 2 THEN (CAST (DEV_MntTTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_COMMANDE_TABLE.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_COMMANDE_TABLE.DEV_Date) END DESC "
    )
    fun filterByCodeClient(searchString: String, sortBy: String, isAsc: Int, station : String): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>>

    @Transaction
    @Query(
        "SELECT * FROM $BON_COMMANDE_TABLE " +
                "JOIN $LIGNE_BON_COMMANDE_TABLE ON $BON_COMMANDE_TABLE.DEV_Num = $LIGNE_BON_COMMANDE_TABLE.LG_DEV_NumBon " +
                " WHERE DEV_Etat LIKE 'BCC_client' and DEV_Station =:station" +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 1 THEN (CAST (DEV_MntTTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 2 THEN (CAST (DEV_MntTTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_COMMANDE_TABLE.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$BON_COMMANDE_TABLE.DEV_Date) END DESC "
    )
    fun getAllFiltred(isAsc: Int,  sortBy: String, station : String): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>>

}
