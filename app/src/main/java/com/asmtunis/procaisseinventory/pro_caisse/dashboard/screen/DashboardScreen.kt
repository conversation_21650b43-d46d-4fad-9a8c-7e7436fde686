package com.asmtunis.procaisseinventory.pro_caisse.dashboard.screen

import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserRoute
import com.asmtunis.procaisseinventory.nav_components.ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.DashboardNavigation
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.DashboardScreenViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel

@Composable
fun DashboardScreen(
    navigate: (route: Any) -> Unit,
    networkErrorsVM: NetworkErrorsViewModel,
    intent: Intent,
    settingViewModel: SettingViewModel,
    navDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    mainViewModel: MainViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    networkViewModel: NetworkViewModel,
    dashboardScreenVM: DashboardScreenViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels
) {
    val context = LocalContext.current
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val utilisateur = mainViewModel.utilisateur
    val exerciceCode = dataViewModel.getExercice()
    val station = utilisateur.Station
    val sCIdSCaisse = navDrawerViewModel.sessionCaisse.sCIdSCaisse

    // Handle notification navigation
    LaunchedEffect(key1 = Unit) {
        if (intent.getBooleanExtra(Globals.FROM_NOTIFICATION, false)) {
            getProCaisseDataViewModel.getDeplacementOutByUser(
                baseConfig = selectedBaseconfig,
                listImmobilisation = mainViewModel.immobilisationList
            )

            navDrawerViewModel.proCaisseListMenu.find { it.id == ID.DEPLACEMENT_OUT_BYUSER_ID }?.let { 
                navDrawerViewModel.onSelectedMenuChange(it) 
            }

            navigate(DeplacementOutByUserRoute)
        }

        mainViewModel.onSelectedBaseconfigChange(baseConfig = selectedBaseconfig)
    }

    // Initialize dashboard data
    LaunchedEffect(key1 = station, key2 = sCIdSCaisse) {
        android.util.Log.d("DashboardScreen", "LaunchedEffect triggered - station: '$station', sCIdSCaisse: '$sCIdSCaisse'")
        mainViewModel.onSelectedBaseconfigChange(baseConfig = selectedBaseconfig)

        // Debug: Check database content
        dashboardScreenVM.checkDatabaseContent()

        // Ensure essential data is synced for offline-first operations
        // This triggers sync if marque/famille data is missing
        if (mainViewModel.marqueList.isEmpty() || mainViewModel.listFamille.isEmpty()) {
            getSharedDataViewModel.getCommenSharedData(baseConfig = selectedBaseconfig)
        }

        dashboardScreenVM.onListNTopClientsChange(idSCaisse = sCIdSCaisse, number = 3)
        dashboardScreenVM.onCAChangeByStation(station = station)
        dashboardScreenVM.onBlNbrChangeByStation(station = station)

        dashboardScreenVM.getReglementListByStation(
            station = station,
            regRemarque = com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.PaymentConst.CREDIT_PAYEMENT_REMARK
        )
        dashboardScreenVM.onMntCreditChangeByStation(station = station)
    }

    // Use the new DashboardNavigation with colorful cards
    DashboardNavigation(
        navigate = { navigate(it) },
        navDrawerViewmodel = navDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel,
        onLogoutClick = {
            // Use the same logout logic as navigation drawer
            navDrawerViewModel.onLogoutBtnClick(
                selectedBaseconfig = selectedBaseconfig,
                resetDataStore = {
                    dataViewModel.resetDataStore()
                },
                navigate = {
                    navigate(it)
                }
            )
        },
        syncCompletionViewModel = null
    )
}
