package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.ImageUtils
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.TypeVc
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class VeilleConcurentielViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {

    init {
        getTypeCommunication()
    }


    var showCustomFilter: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }
    var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }









    fun onImageDeleted(value: ImagePieceJoint) {
        if(value.codeIMG!=""){
        //    currentImageList.remove(value)


            viewModelScope.launch(dispatcher) {
                if(value.isSync)
                proCaisseLocalDb.imageVC.setDeleted(value.codeIMG)
                else if(value.status != ItemStatus.DELETED.status) proCaisseLocalDb.imageVC.deleteByCodeTypeVc(value.codeTypeVC)
            }

        }
            else{
        //    currentImageList.remove(value)
        }

    }




    var openArticleLocalExpanded: Boolean by mutableStateOf(false)
        private set
    fun onOpenArticleLocalChange(value: Boolean) {
        openArticleLocalExpanded = value
    }

    var selectedArticle: String by mutableStateOf("")
        private set
    fun onSelectedArticleChange(value: String) {
        selectedArticle = value
    }


    var typeCommunicationVCList: List<TypeCommunicationVC> by mutableStateOf(emptyList())
        private set
    private fun getTypeCommunication() {
        viewModelScope.launch {
            proCaisseLocalDb.typeCommunicationVC.getAll().collectLatest {
                if(it==null) return@collectLatest
                typeCommunicationVCList = it


                //  if (Globals.BASE_URL != "") syncClients(it)
            }
        }
    }

    var articleFilter: String by mutableStateOf("")
        private set

    fun onArticleFilterChange(value: String) {
        articleFilter = value
    }










    var showSearchView: Boolean by mutableStateOf(false)
        private set
    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }




    var typeCommunicationExpanded: Boolean by mutableStateOf(false)
        private set
    fun onTypeCommunicationExpandedChange(value: Boolean) {
        typeCommunicationExpanded = value
    }


    var fournisseurExpanded: Boolean by mutableStateOf(false)
        private set
    fun onFournisseurExpandedChange(value: Boolean) {
        fournisseurExpanded = value
    }


    fun saveListImage(listImage: List<ImagePieceJoint>) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.imageVC.upsertAll(listImage)
        }
    }




    fun saveImageList(
        context: Context,
        userID: String,
        imageCodeM : String,
        vcCodeM : String,
        imageUriList: List<Uri>,
    ) {

            val listImageVC = mutableListOf<ImagePieceJoint>()

            for (i in imageUriList.indices) {
                val imageVc = ImagePieceJoint(
                    codeIMG = imageCodeM+ "_" + (i + 1).toString(),
                    codeMob = imageCodeM+ "_" + (i + 1).toString(),
                    typeVC = TypeVc.VCPRIX.typeVC,
                    dateOp = getCurrentDateTime(),
                    codeTypeVC = vcCodeM,
                    image = ImageUtils.uriToBase64(context, imageUriList[i]),
                    codeUser = userID.toInt(),
                    cheminImg = "",
                    imgUrl = imageUriList[i].toString()
                )
                imageVc.status = ItemStatus.INSERTED.status
                imageVc.isSync = false


                listImageVC.add(imageVc)
            }
          //  cameraViewModel.addImageUri(imgUri = EMPTY_IMAGE_URI, context = context)
            saveListImage(listImageVC)


    }
}
