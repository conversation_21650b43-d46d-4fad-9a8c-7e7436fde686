package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.model.NestedItem
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.InvPatBatchResponse
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.dto.LigneBonCommandeForSync
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.dto.toLigneBonCommandeForSync

import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
class SyncBonCommandeViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val dataStoreRepository: DataStoreRepository,
    private val listenNetwork: ListenNetwork
    // app: Application
) : ViewModel() {

    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    init {
        getNotSyncBonCommande()

    }


    var responseAddBonCommandeState: RemoteResponseState<List<InvPatBatchResponse>>  by mutableStateOf(RemoteResponseState())
        private set


    var bonCommandeNotSync: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set
    var notSyncBonCommandeObj : String by mutableStateOf("")
        private set


    private fun getNotSyncBonCommande() {
        viewModelScope.launch {
            val bonCommandeNotSyncFlow =  proCaisseLocalDb.bonCommande.notSynced().distinctUntilChanged()


            combine(networkFlow, bonCommandeNotSyncFlow, autoSyncFlow) { isConnected, bonCommandeNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                bonCommandeNotSyncList?.ifEmpty { emptyMap() }?: emptyMap()
            }.collect {

                if (it.isEmpty()) {
                    bonCommandeNotSync = emptyMap()
                    return@collect
                }
                bonCommandeNotSync = it
                if(connected && autoSyncState) syncBonCommande()
            }
        }
    }



    fun syncBonCommande(selectedBonCommande: BonCommande = BonCommande()) {
        viewModelScope.launch(dispatcherIO) {
            val listVisiteWithLinesDn = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            bonCommandeNotSync.forEach { (key, value) ->
                run {
                    listVisiteWithLinesDn.add(
                        NestedItem(
                            parent  = key,
                            children  = value
                        )
                    )
                }
            }

            if(selectedBonCommande != BonCommande()) {
                listVisiteWithLinesDn.removeIf { it.parent?.devCodeM != selectedBonCommande.devCodeM }
            }

            // Auto-fix common data issues before validation
            autoFixDataIssues(listVisiteWithLinesDn)

            // Validate data before sending to server
            val validationResult = validateBonCommandeData(listVisiteWithLinesDn)
            if (!validationResult.isValid) {
                responseAddBonCommandeState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "Data validation failed: ${validationResult.errors.joinToString(", ")}"
                )
                return@launch
            }

            // Convert to enhanced ligne data with article properties
            val enhancedListVisiteWithLinesDn = convertToEnhancedLigneData(listVisiteWithLinesDn)

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(enhancedListVisiteWithLinesDn)
            )

            notSyncBonCommandeObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.bonCommande.addBatchBonCommande(notSyncBonCommandeObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        //val ddm =  DateUtils.getCurrentDateTime() TODO MAYBE UPDATE DDM ALSO §
                        //"message":"Bon Commande Existe Deja","code":10701
                        for (i in result.data!!.indices) {
                            proCaisseLocalDb.bonCommande.setSynced(
                                bonCommandeNum = result.data[i].dEVNum,
                                bonCommandeNumM = result.data[i].dEVCodeM
                            )

                            proCaisseLocalDb.ligneBonCommande.setSynced(
                                newNum = result.data[i].dEVNum,
                                oldNum = result.data[i].dEVCodeM
                            )
                        }

                        responseAddBonCommandeState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        responseAddBonCommandeState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        val errorMessage = when {
                            result.message?.contains("ART_TVA") == true -> {
                                "Article data is incomplete. Please ensure all articles have valid TVA information and try again."
                            }
                            result.message?.contains("Server Error") == true -> {
                                result.message
                            }
                            result.message?.contains("property") == true && result.message.contains("non-object") -> {
                                "Data validation error on server. Please check your data and try again."
                            }
                            else -> result.message ?: "Unknown error occurred"
                        }
                        responseAddBonCommandeState = RemoteResponseState(data = null, loading = false, error = errorMessage, message = selectedBonCommande.dEVNum)
                    }

                    else -> {
                        responseAddBonCommandeState = RemoteResponseState(data = null, loading = false, error = "Unknow Error")

                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }

    /**
     * Validate BonCommande data before sending to server
     * This helps prevent server errors like "ART_TVA of non-object"
     */
    private fun validateBonCommandeData(
        listVisiteWithLinesDn: List<NestedItem<BonCommande, List<LigneBonCommande>>>
    ): ValidationResult {
        val errors = mutableListOf<String>()

        for (item in listVisiteWithLinesDn) {
            val bonCommande = item.parent
            val lignes = item.children ?: emptyList()

            // Validate BonCommande
            if (bonCommande == null) {
                errors.add("BonCommande is null")
                continue
            }

            if (bonCommande.devCodeM.isBlank()) {
                errors.add("BonCommande code is missing")
            }

            if (bonCommande.dEVCodeClient.isNullOrBlank()) {
                errors.add("Client code is missing for BonCommande ${bonCommande.devCodeM}")
            }

            // Validate LigneBonCommande
            if (lignes.isEmpty()) {
                errors.add("No lines found for BonCommande ${bonCommande.devCodeM}")
                continue
            }

            for ((index, ligne) in lignes.withIndex()) {
                val linePrefix = "Line ${index + 1} in BonCommande ${bonCommande.devCodeM}"

                if (ligne.lGDEVCodeArt.isBlank()) {
                    errors.add("$linePrefix: Article code is missing")
                }

                if (ligne.lGDEVQte.isNullOrBlank() || ligne.lGDEVQte == "0") {
                    errors.add("$linePrefix: Quantity is missing or zero")
                }

                if (ligne.lGDEVPUHT.isNullOrBlank()) {
                    errors.add("$linePrefix: Unit price is missing")
                }

                // Validate TVA - this is critical for the ART_TVA error
                if (ligne.lGDEVTva.isNullOrBlank()) {
                    errors.add("$linePrefix: TVA is missing for article ${ligne.lGDEVCodeArt}")
                }

                try {
                    val tva = ligne.lGDEVTva?.toDoubleOrNull()
                    if (tva == null) {
                        errors.add("$linePrefix: TVA value is not a valid number")
                    } else if (tva < 0) {
                        errors.add("$linePrefix: TVA cannot be negative")
                    }
                } catch (e: Exception) {
                    errors.add("$linePrefix: Invalid TVA format")
                }
            }
        }

        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }

    /**
     * Auto-fix common data issues that cause server errors
     * This function modifies the data in-place to prevent sync failures
     */
    private suspend fun autoFixDataIssues(
        listVisiteWithLinesDn: List<NestedItem<BonCommande, List<LigneBonCommande>>>
    ) {
        for (item in listVisiteWithLinesDn) {
            val lignes = item.children ?: emptyList()

            for (ligne in lignes) {
                var needsUpdate = false

                // Fix missing TVA - this is the main cause of "ART_TVA of non-object" error
                if (ligne.lGDEVTva.isNullOrBlank() || ligne.lGDEVTva == "null") {
                    ligne.lGDEVTva = "0.0"
                    needsUpdate = true
                }

                // Fix invalid TVA values
                try {
                    val tvaValue = ligne.lGDEVTva?.toDoubleOrNull()
                    if (tvaValue == null || tvaValue < 0 || !tvaValue.isFinite()) {
                        ligne.lGDEVTva = "0.0"
                        needsUpdate = true
                    }
                } catch (e: Exception) {
                    ligne.lGDEVTva = "0.0"
                    needsUpdate = true
                }

                // Fix other critical null values
                if (ligne.lGDEVQte.isNullOrBlank()) {
                    ligne.lGDEVQte = "1.0"
                    needsUpdate = true
                }

                if (ligne.lGDEVPUHT.isNullOrBlank()) {
                    ligne.lGDEVPUHT = "0.0"
                    needsUpdate = true
                }

                if (ligne.lGDEVNetht.isNullOrBlank()) {
                    ligne.lGDEVNetht = "0.0"
                    needsUpdate = true
                }

                if (ligne.lGDEVRemise.isNullOrBlank()) {
                    ligne.lGDEVRemise = "0.0"
                    needsUpdate = true
                }

                // Update in database if changes were made
                if (needsUpdate) {
                    proCaisseLocalDb.ligneBonCommande.upsert(ligne)
                }
            }
        }
    }

    /**
     * Convert regular ligne data to enhanced ligne data with article properties
     * This prevents server errors like "Trying to get property 'ART_TVA' of non-object"
     */
    private suspend fun convertToEnhancedLigneData(
        listVisiteWithLinesDn: List<NestedItem<BonCommande, List<LigneBonCommande>>>
    ): List<NestedItem<BonCommande, List<LigneBonCommandeForSync>>> {
        return listVisiteWithLinesDn.map { item ->
            val bonCommande = item.parent
            val lignes = item.children ?: emptyList()

            // Convert each ligne to enhanced ligne with article data
            val enhancedLignes = lignes.map { ligne ->
                // Fetch article data for this ligne
                val article = try {
                    proCaisseLocalDb.articles.getByArtCode(ligne.lGDEVCodeArt).first()
                } catch (e: Exception) {
                    null // Article not found, will use defaults
                }

                // Convert to enhanced ligne
                ligne.toLigneBonCommandeForSync(article)
            }

            NestedItem(
                parent = bonCommande,
                children = enhancedLignes
            )
        }
    }
}

/**
 * Data class for validation results
 */
data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String>
)
