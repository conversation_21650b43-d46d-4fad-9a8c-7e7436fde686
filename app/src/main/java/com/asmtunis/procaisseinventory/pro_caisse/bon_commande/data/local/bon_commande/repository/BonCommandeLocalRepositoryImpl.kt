package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.dao.BonCommandeDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.dao.BonCommandeDAOWrapper
import kotlinx.coroutines.flow.Flow


class BonCommandeLocalRepositoryImpl(
        private val bonCommandeDAO: BonCommandeDAO,
        private val bonCommandeDAOWrapper: BonCommandeDAOWrapper
    ) : BonCommandeLocalRepository {
    override fun upsertAll(value: List<BonCommande>) = bonCommandeDAOWrapper.insertAll(value)

    override fun upsert(value: BonCommande) = bonCommandeDAOWrapper.insert(value)
    override fun updateNameClient(codeclient: String, clientName: String)  = bonCommandeDAO.updateNameClient(codeclient = codeclient, clientName = clientName)

    override fun updateStatus(status: String, bonCommandeM: String, devDdm: String) = bonCommandeDAOWrapper.updateStatus(status = status, bonCommandeM = bonCommandeM, devDdm = devDdm)
    override fun updateObservation(devObservation: String, devNum: String, exercie: String) = bonCommandeDAO.updateObservation(devObservation = devObservation, devNum = devNum, exercie = exercie)

    override fun setSynced(bonCommandeNum: String, bonCommandeNumM: String) = bonCommandeDAOWrapper.setSynced(bonCommandeNum = bonCommandeNum, bonCommandeNumM = bonCommandeNumM)

    override fun notSynced(): Flow<Map<BonCommande, List<LigneBonCommande>>?> = bonCommandeDAOWrapper.notSyncedSafe()

    override fun deleteAll() = bonCommandeDAOWrapper.deleteAll()
    override fun deleteByCodeM(codeM: String) = bonCommandeDAOWrapper.deleteByCodeM(codeM)

    override fun getAllByStation(station: String): Flow<Map<BonCommande, List<LigneBonCommande>>> = bonCommandeDAOWrapper.getAllByStationSafe(station)
    override fun getAll(): Flow<Map<BonCommande, List<LigneBonCommande>>?> = bonCommandeDAO.getAll()

    override fun getByCodeCltAndStation(codeClient: String, station : String): Flow<Map<BonCommande, List<LigneBonCommande>>> = bonCommandeDAOWrapper.getByCodeCltAndStationSafe(codeClient = codeClient, station = station)

 
    override fun getAllFiltred(
        isAsc: Int,
        sortBy: String,
        station : String
    ): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>> = bonCommandeDAO. getAllFiltred(
        isAsc = isAsc,
        sortBy = sortBy,
        station= station
    )

    override fun filterByCodeClient(
        searchString: String,
        sortBy: String,
        isAsc: Int,
        station : String
    ): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>> = bonCommandeDAO.filterByCodeClient(
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        station= station
    )

    override fun filterByBonCommandeNum(
        searchString: String,
        sortBy: String,
        isAsc: Int,
        station : String
    ): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>> = bonCommandeDAO.filterByBonCommandeNum(
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        station= station
    )

    override fun filterByNomClient(
        searchString: String,
        sortBy: String,
        isAsc: Int,
        station : String
    ): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>>  = bonCommandeDAO.filterByNomClient(
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        station= station
    )
}