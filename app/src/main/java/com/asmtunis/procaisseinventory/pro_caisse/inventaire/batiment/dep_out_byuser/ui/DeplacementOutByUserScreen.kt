package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui

import NavDrawer
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserDetailRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.view_model.DeplacementOutByUserViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels

@Composable
fun DeplacementOutByUserScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    deplacementOutByUserViewModel: DeplacementOutByUserViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    settingViewModel: SettingViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,

    ) {
    val uiWindowState = settingViewModel.uiWindowState


    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)

  val isConnected = networkViewModel.isConnected

    val allBatimentListstate = deplacementOutByUserViewModel.depOutByUserListstate

    // Set current user for station filtering
    deplacementOutByUserViewModel.setCurrentUtilisateur(mainViewModel.utilisateur)








    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navigationDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,

        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        DeplacementOutByUserScreenContent(
            popBackStack = {
                // Navigate to shortcuts screen instead of back
                navigate(com.asmtunis.procaisseinventory.core.navigation.HomePageRoute)
            },
            isConnected = isConnected,
            navIcon = R.drawable.menu,
            drawer = drawer,
            showNavIcon = true, // Always show navigation icon
            title = stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title),
            immobilisationList = mainViewModel.immobilisationList,
            deplacementOutByUserViewModel = deplacementOutByUserViewModel,
            allBatimentListstate = allBatimentListstate,
            dataViewModel = dataViewModel,
            getProCaisseDataViewModel = getProCaisseDataViewModel,
            onItemClick = { item->
                deplacementOutByUserViewModel.restSelectedDeplacementOutByUser()

                deplacementOutByUserViewModel.onSelectedDeplacementOutByUserChange(
                    allBatimentListstate.lists.filter { it.key == item },
                )

                navigate(DeplacementOutByUserDetailRoute)
            }
          )
    }
}


