package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.dao

import android.util.Log
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BonCommandeDAOWrapper @Inject constructor(
    private val bonCommandeDAO: BonCommandeDAO
) {
    companion object {
        private const val TAG = "BonCommandeDAOWrapper"
        private const val DEFAULT_PAGE_SIZE = 50
        private const val MAX_SAFE_LIMIT = 100
    }

    /**
     * Safe version of getAllByStation with error handling and automatic pagination
     */
    fun getAllByStationSafe(station: String): Flow<Map<BonCommande, List<LigneBonCommande>>> {
        return flow {
            try {
                // First try with limited results
                bonCommandeDAO.getAllByStation(station)
                    .catch { exception ->
                        
                        // If cursor error, try with smaller chunks
                        if (exception.message?.contains("CursorWindow") == true) {
                            emitPaginatedResults(station)
                        } else {
                            // For other errors, emit empty map
                            emit(emptyMap<BonCommande, List<LigneBonCommande>>())
                        }
                    }
                    .collect { result ->
                        emit(result)
                    }
            } catch (e: Exception) {
                emit(emptyMap<BonCommande, List<LigneBonCommande>>())
            }
        }
    }

    /**
     * Paginated version that loads data in chunks to avoid cursor overflow
     */
    fun getAllByStationPaginated(
        station: String, 
        pageSize: Int = DEFAULT_PAGE_SIZE
    ): Flow<Map<BonCommande, List<LigneBonCommande>>> {
        return flow {
            try {
                val safePageSize = minOf(pageSize, MAX_SAFE_LIMIT)
                var offset = 0
                val allResults = mutableMapOf<BonCommande, List<LigneBonCommande>>()

                do {
                    val pageResults = mutableMapOf<BonCommande, List<LigneBonCommande>>()
                    
                    bonCommandeDAO.getAllByStationPaginated(station, safePageSize, offset)
                        .catch { exception ->
                            // Continue with what we have so far
                        }
                        .collect { result ->
                            pageResults.putAll(result)
                        }

                    allResults.putAll(pageResults)
                    offset += safePageSize

                    // Break if we got less than expected (end of data)
                } while (pageResults.size >= safePageSize)

                emit(allResults)
            } catch (e: Exception) {
                emit(emptyMap<BonCommande, List<LigneBonCommande>>())
            }
        }
    }

    /**
     * Get count safely with error handling
     */
    fun getAllByStationCountSafe(station: String): Flow<Int> {
        return bonCommandeDAO.getAllByStationCount(station)
            .catch { exception ->
                emit(0)
            }
    }

    /**
     * Helper method to emit paginated results when cursor error occurs
     */
    private suspend fun FlowCollector<Map<BonCommande, List<LigneBonCommande>>>.emitPaginatedResults(station: String) {
        try {
            var offset = 0
            val pageSize = 25 // Smaller page size for error recovery
            val allResults = mutableMapOf<BonCommande, List<LigneBonCommande>>()

            do {
                val pageResults = mutableMapOf<BonCommande, List<LigneBonCommande>>()
                
                bonCommandeDAO.getAllByStationPaginated(station, pageSize, offset)
                    .catch { 
                    }
                    .collect { result ->
                        pageResults.putAll(result)
                    }

                allResults.putAll(pageResults)
                offset += pageSize

            } while (pageResults.size >= pageSize && offset < 1000) // Safety limit

            emit(allResults)
        } catch (e: Exception) {
            emit(emptyMap<BonCommande, List<LigneBonCommande>>())
        }
    }

    // Delegate other methods to the original DAO
    fun insert(item: BonCommande) = bonCommandeDAO.insert(item)
    fun insertAll(items: List<BonCommande>) = bonCommandeDAO.insertAll(items)
    fun deleteAll() = bonCommandeDAO.deleteAll()
    fun deleteByCodeM(codeM: String) = bonCommandeDAO.deleteByCodeM(codeM)
    fun updateStatus(status: String, bonCommandeM: String, devDdm: String) = 
        bonCommandeDAO.updateStatus(status, bonCommandeM, devDdm)
    fun setSynced(bonCommandeNum: String, bonCommandeNumM: String) = 
        bonCommandeDAO.setSynced(bonCommandeNum, bonCommandeNumM)
    
    // Safe versions of other potentially problematic methods
    fun getByCodeCltAndStationSafe(codeClient: String, station: String): Flow<Map<BonCommande, List<LigneBonCommande>>> {
        return bonCommandeDAO.getByCodeCltAndStation(codeClient, station)
            .catch { exception ->
                emit(emptyMap<BonCommande, List<LigneBonCommande>>())
            }
    }

    fun notSyncedSafe(): Flow<Map<BonCommande, List<LigneBonCommande>>> {
        return bonCommandeDAO.getNotSynced()
            .catch { exception ->
                emit(emptyMap<BonCommande, List<LigneBonCommande>>())
            }
    }
}
