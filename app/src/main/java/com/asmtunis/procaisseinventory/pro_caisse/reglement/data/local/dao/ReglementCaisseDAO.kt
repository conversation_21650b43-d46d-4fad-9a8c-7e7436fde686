package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.REGLEMENT_CAISSE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.domaine.ReglementWithChequeAndTicketResto
import kotlinx.coroutines.flow.Flow

@Dao
interface ReglementCaisseDAO {
    @get:Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE order by  CAST(REGC_DateReg AS DATE )  desc ")
    val all: Flow<List<ReglementCaisse>>

    @get:Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE order by  CAST(REGC_DateReg AS DATE )  desc ")
    val allMutable: Flow<List<ReglementCaisse>>

    // @Query("SELECT * FROM ReglementCaisse where REGC_IdSCaisse= :session order by  CAST(REGC_DateReg AS DATE )  desc ")
    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE where (CASE WHEN :station IS NULL OR :station = '' THEN 1=1 ELSE REGC_IdStation = :station END) and (CASE WHEN :caisse IS NULL OR :caisse = '' THEN 1=1 ELSE REGC_IdSCaisse = :caisse END) order by strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) desc")
    fun getBySession(
        station: String,
        caisse: String,
    ): Flow<List<ReglementCaisse>?>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE where REGC_Station = :station order by strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) desc")
    fun getByStation(station: String): Flow<List<ReglementCaisse>?>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE where REGC_NumTicketPart= :numTicketPart")
    fun getbyNumTicketPart(numTicketPart: String?): Flow<ReglementCaisse>

    // @Query("SELECT * FROM ReglementCaisse where REGC_IdSCaisse= :session and REGC_Station= :station  and REGC_Remarque= :regRemarque")
    // LiveData<List<ReglementCaisse>> getbyRegRemarque(String session, String station, String regRemarque);
    @Query(
        "SELECT ifnull(MAX(cast(substr(REGC_Code,length(:prefix) + 1 ,length('REGC_Code'))as integer)),0)+1 FROM   $REGLEMENT_CAISSE_TABLE WHERE substr(REGC_Code, 0 ,length(:prefix)+1) = :prefix",
    )
    fun getNewCode(prefix: String?): Flow<String>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE REGC_MntEspece > 0 and REGC_NumTicket =:rEGCNumTicket")
    fun getByCash(rEGCNumTicket: Int): Flow<ReglementCaisse>

    @Query("SELECT SUM(ifnull(REGC_MntEspece,0))  FROM $REGLEMENT_CAISSE_TABLE where REGC_IdStation= :station and REGC_IdSCaisse= :caisse")
    fun getMntEspece(
        station: String,
        caisse: String,
    ): Flow<String?>

    @Query(
        "SELECT SUM(ifnull(REGC_Montant,0)) FROM $REGLEMENT_CAISSE_TABLE where REGC_IdSCaisse= :session and REGC_Station= :station  and REGC_Remarque= :regRemarque",
    )
    fun getbyRegRemarque(
        session: String,
        station: String,
        regRemarque: String,
    ): Flow<String?>

    @Query("SELECT SUM(ifnull(REGC_MntCheque,0))  FROM $REGLEMENT_CAISSE_TABLE where REGC_IdStation= :station and REGC_IdSCaisse= :caisse")
    fun getMntCheque(
        station: String,
        caisse: String,
    ): Flow<String?>

    @Query("SELECT SUM(ifnull(REGC_MntTraite,0)) FROM $REGLEMENT_CAISSE_TABLE where REGC_IdStation= :station and REGC_IdSCaisse= :caisse")
    fun getMntTraite(
        station: String,
        caisse: String,
    ): Flow<String?>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE REGC_CodeClient = :codeClt")
    fun getReglementByClient(codeClt: String): Flow<List<ReglementCaisse>>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE REGC_Code_M =:rEGC_Code_M")
    fun getByREGCM(rEGC_Code_M: String): Flow<ReglementCaisse>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE   REGC_NumTicket =:rEGCNumTicket")
    fun getAllByTicket(rEGCNumTicket: Int): Flow<List<ReglementCaisse>>

    @Query(
        "SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE  REGC_NumTicket =:rEGCNumTicket and isSync= 0 and  (Status='INSERTED'  or Status='UPDATED')",
    )
    fun getAllByTicketNotSynced(rEGCNumTicket: Int): Flow<List<ReglementCaisse>>

    @Transaction
    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE   isSync= 0 and canSync = 1 and  (Status='INSERTED'  or Status='INSERTED_REG_FROM_REGLEMENT')")
    fun getAllNotSynced(): Flow<List<ReglementWithChequeAndTicketResto>?>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE   REGC_NumTicket =:rEGCNumTicket")
    fun getByTicket(rEGCNumTicket: Int): Flow<ReglementCaisse>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE   REGC_Code_M =:rEGCNumTicket")
    fun getByTicketM(rEGCNumTicket: String): Flow<ReglementCaisse>

    @Query("SELECT sum(REGC_Montant) FROM $REGLEMENT_CAISSE_TABLE WHERE   REGC_CodeClient=:codeC and REGC_Exercice =:exercice")
    fun getSumMnttcByClient(
        codeC: String,
        exercice: String,
    ): Flow<Double>

    @get:Query("SELECT SUM(REGC_Montant) FROM $REGLEMENT_CAISSE_TABLE WHERE   REGC_NumTicket = 0")
    val sumReglementCredit: Flow<Double>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE   REGC_NumTicket =:rEGCNumTicket and isSync=0 and  (Status='INSERTED'  or Status='UPDATED')",)
    fun getByTicketNotSynced(rEGCNumTicket: Int): Flow<ReglementCaisse>

    @Query("Update  $REGLEMENT_CAISSE_TABLE set  isSync=1 where isSync=0 ")
    fun updateAllReglementCaisse()

    @Query("Update  $REGLEMENT_CAISSE_TABLE set  REGC_NumTicket=:newCode where REGC_NumTicket=:oldCode and REGC_IdCaisse=:caisse and REGC_Exercice=:regcExercice",)
    fun updateNumTicket(
        oldCode: Int,
        newCode: Int,
        caisse: String,
        regcExercice: String,
    )

    @get:Query("SELECT count(*) FROM $REGLEMENT_CAISSE_TABLE where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='INSERTED_REG_FROM_REGLEMENT')",)
    val noSyncCount: Flow<Int>

    @get:Query("SELECT count(*) FROM $REGLEMENT_CAISSE_TABLE where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='INSERTED_REG_FROM_REGLEMENT')",)
    val noSyncCountMuable: Flow<Int>

    @get:Query("SELECT count(*) FROM $REGLEMENT_CAISSE_TABLE where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='INSERTED_REG_FROM_REGLEMENT')",)
    val noSyncCountNonMutable: Flow<Int>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE REGC_MntCarteBancaire > 0 and REGC_NumTicket =:rEGCNumTicket")
    fun getByCheck(rEGCNumTicket: Int): Flow<List<ReglementCaisse>>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE REGC_MntTraite > 0 and REGC_NumTicket =:rEGCNumTicket")
    fun getByTicketResto(rEGCNumTicket: Int): Flow<List<ReglementCaisse>>

    @get:Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE")
    val allByType: Flow<List<ReglementCaisse>>

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE REGC_Code = :code ")
    fun getOneByCode(code: String): Flow<ReglementCaisse>

    @Query("UPDATE $REGLEMENT_CAISSE_TABLE SET REGC_Code=:regCode , Status = 'SELECTED' , IsSync = 1 where REGC_Code_M = :regCodeM")
    fun updateRegCodeAndState(
        regCode: String,
        regCodeM: String,
    )

    @Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE WHERE REGC_Code_M = :code ")
    fun getOneByCodeM(code: String): Flow<ReglementCaisse>

    @get:Query("SELECT * FROM $REGLEMENT_CAISSE_TABLE LIMIT 1")
    val one: Flow<ReglementCaisse>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: ReglementCaisse)

    @Query("UPDATE $REGLEMENT_CAISSE_TABLE SET REGC_CodeClient = :code_client where REGC_CodeClient = :oldCodeClient")
    fun updateCodeClient(
        code_client: String,
        oldCodeClient: String,
    )

    @Query("select * from $REGLEMENT_CAISSE_TABLE where REGC_CodeClient = :oldCodeClient")
    fun getByCodeClient(oldCodeClient: String): Flow<List<ReglementCaisse>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<ReglementCaisse>)

    @Query("DELETE FROM $REGLEMENT_CAISSE_TABLE where Status='SELECTED'")
    fun deleteAllExeptNotSync()

    @Query("DELETE FROM $REGLEMENT_CAISSE_TABLE")
    fun deleteAll()

    @Query(
        "DELETE FROM $REGLEMENT_CAISSE_TABLE where (REGC_Code=:code or REGC_Code_M=:code) and REGC_Exercice=:exercice and REGC_IdSCaisse=:idCaisse",
    )
    fun deleteByCode(
        code: String,
        exercice: String,
        idCaisse: String,
    )

    @Transaction
    @Query(
        "SELECT * FROM $REGLEMENT_CAISSE_TABLE " +
            "WHERE REGC_Code LIKE '%' || :searchString || '%' " +
            "AND REGC_Station = :station " +
            "and ( CASE WHEN :filterByTypePayment !=  '' THEN ( " +
            "  CASE WHEN :filterByTypePayment ==  'Espéce' THEN REGC_MntEspece != '0' ELSE REGC_MntEspece != '0' or  REGC_MntEspece == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Chéque' THEN REGC_MntCheque!= '0' ELSE REGC_MntCheque != '0' or  REGC_MntCheque == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Traite' THEN REGC_MntTraite != '0' ELSE REGC_MntTraite != '0' or  REGC_MntTraite == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Carte Bancaire' THEN REGC_MntCarteBancaire != '0' ELSE REGC_MntCarteBancaire != '0' or  REGC_MntCarteBancaire == '0'END) " +
            "ELSE REGC_MntEspece !=:filterByTypePayment and REGC_MntCarteBancaire !=:filterByTypePayment and REGC_MntCheque !=:filterByTypePayment and REGC_MntTraite !=:filterByTypePayment END " +

            "and  CASE WHEN :filterByTypeReglement !=  ''THEN REGC_Remarque=:filterByTypeReglement ELSE REGC_Remarque !=:filterByTypeReglement  END) " +

            " ORDER BY " +
            "CASE WHEN :sort_by = 'REGC_Code'  AND :isAsc = 1 THEN REGC_Code END ASC, " +
            "CASE WHEN :sort_by = 'REGC_Code'  AND :isAsc = 2 THEN REGC_Code END DESC, " +
            "CASE WHEN :sort_by = 'REGC_Montant'  AND :isAsc = 1 THEN (CAST (REGC_Montant AS REAL)) END ASC, " +
            "CASE WHEN :sort_by = 'REGC_Montant'  AND :isAsc = 2 THEN (CAST (REGC_Montant AS REAL)) END DESC, " +
            "CASE WHEN :sort_by = 'REGC_DateReg'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) END ASC, " +
            "CASE WHEN :sort_by = 'REGC_DateReg'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) END DESC ",
    )
    fun filterByRegCode(
        searchString: String,
        sort_by: String,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        isAsc: Int,
        station: String,
    ): Flow<List<ReglementCaisseWithTicketAndClient>>

    @Transaction
    @Query(
        "SELECT * FROM $REGLEMENT_CAISSE_TABLE " +
            "WHERE REGC_NumTicket LIKE '%' ||  :searchString || '%' " +
            "AND REGC_Station = :station " +
            "and ( CASE WHEN :filterByTypePayment !=  '' THEN ( " +
            "  CASE WHEN :filterByTypePayment ==  'Espéce' THEN REGC_MntEspece != '0' ELSE REGC_MntEspece != '0' or  REGC_MntEspece == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Chéque' THEN REGC_MntCheque!= '0' ELSE REGC_MntCheque != '0' or  REGC_MntCheque == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Traite' THEN REGC_MntTraite != '0' ELSE REGC_MntTraite != '0' or  REGC_MntTraite == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Carte Bancaire' THEN REGC_MntCarteBancaire != '0' ELSE REGC_MntCarteBancaire != '0' or  REGC_MntCarteBancaire == '0'END) " +
            "ELSE REGC_MntEspece !=:filterByTypePayment and REGC_MntCarteBancaire !=:filterByTypePayment and REGC_MntCheque !=:filterByTypePayment and REGC_MntTraite !=:filterByTypePayment END " +

            "and  CASE WHEN :filterByTypeReglement !=  ''THEN REGC_Remarque=:filterByTypeReglement ELSE REGC_Remarque !=:filterByTypeReglement  END) " +

            " ORDER BY " +
            "CASE WHEN :sort_by = 'REGC_Code'  AND :isAsc = 1 THEN REGC_Code END ASC, " +
            "CASE WHEN :sort_by = 'REGC_Code'  AND :isAsc = 2 THEN REGC_Code END DESC, " +
            "CASE WHEN :sort_by = 'REGC_Montant'  AND :isAsc = 1 THEN (CAST (REGC_Montant AS REAL)) END ASC, " +
            "CASE WHEN :sort_by = 'REGC_Montant'  AND :isAsc = 2 THEN (CAST (REGC_Montant AS REAL)) END DESC, " +
            "CASE WHEN :sort_by = 'REGC_DateReg'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) END ASC, " +
            "CASE WHEN :sort_by = 'REGC_DateReg'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) END DESC ",
    )
    fun filterByRegNumTicket(
        searchString: String,
        sort_by: String,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        isAsc: Int,
        station: String,
    ): Flow<List<ReglementCaisseWithTicketAndClient>>

    @Transaction
    @Query(
        "SELECT * FROM $REGLEMENT_CAISSE_TABLE " +
            "WHERE (REGC_NomPrenom LIKE '%' || :searchString || '%'  OR REGC_CodeClient LIKE '%' || :searchString || '%') " +
            "AND REGC_Station = :station " +
            "and ( CASE WHEN :filterByTypePayment !=  '' THEN ( " +
            "  CASE WHEN :filterByTypePayment ==  'Espéce' THEN REGC_MntEspece != '0' ELSE REGC_MntEspece != '0' or  REGC_MntEspece == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Chéque' THEN REGC_MntCheque!= '0' ELSE REGC_MntCheque != '0' or  REGC_MntCheque == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Traite' THEN REGC_MntTraite != '0' ELSE REGC_MntTraite != '0' or  REGC_MntTraite == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Carte Bancaire' THEN REGC_MntCarteBancaire != '0' ELSE REGC_MntCarteBancaire != '0' or  REGC_MntCarteBancaire == '0'END) " +
            "ELSE REGC_MntEspece !=:filterByTypePayment and REGC_MntCarteBancaire !=:filterByTypePayment and REGC_MntCheque !=:filterByTypePayment and REGC_MntTraite !=:filterByTypePayment END " +

            "and  CASE WHEN :filterByTypeReglement !=  ''THEN REGC_Remarque=:filterByTypeReglement ELSE REGC_Remarque !=:filterByTypeReglement  END) " +

            " ORDER BY " +
            "CASE WHEN :sort_by = 'REGC_Code'  AND :isAsc = 1 THEN REGC_Code END ASC, " +
            "CASE WHEN :sort_by = 'REGC_Code'  AND :isAsc = 2 THEN REGC_Code END DESC, " +
            "CASE WHEN :sort_by = 'REGC_Montant'  AND :isAsc = 1 THEN (CAST (REGC_Montant AS REAL)) END ASC, " +
            "CASE WHEN :sort_by = 'REGC_Montant'  AND :isAsc = 2 THEN (CAST (REGC_Montant AS REAL)) END DESC, " +
            "CASE WHEN :sort_by = 'REGC_DateReg'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) END ASC, " +
            "CASE WHEN :sort_by = 'REGC_DateReg'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) END DESC ",
    )
    fun filterByClient(
        searchString: String,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        sort_by: String?,
        isAsc: Int?,
        station: String,
    ): Flow<List<ReglementCaisseWithTicketAndClient>>

    @Transaction
    @Query(
        "SELECT * FROM $REGLEMENT_CAISSE_TABLE " +
            " WHERE REGC_Station = :station " +
            " AND ( CASE WHEN :filterByTypePayment !=  '' THEN ( " +
            "  CASE WHEN :filterByTypePayment ==  'Espéce' THEN REGC_MntEspece != '0' ELSE REGC_MntEspece != '0' or  REGC_MntEspece == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Chéque' THEN REGC_MntCheque!= '0' ELSE REGC_MntCheque != '0' or  REGC_MntCheque == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Traite' THEN REGC_MntTraite != '0' ELSE REGC_MntTraite != '0' or  REGC_MntTraite == '0'END " +
            "and  CASE WHEN :filterByTypePayment ==  'Carte Bancaire' THEN REGC_MntCarteBancaire != '0' ELSE REGC_MntCarteBancaire != '0' or  REGC_MntCarteBancaire == '0'END) " +
            "ELSE REGC_MntEspece !=:filterByTypePayment and REGC_MntCarteBancaire !=:filterByTypePayment and REGC_MntCheque !=:filterByTypePayment and REGC_MntTraite !=:filterByTypePayment END " +

            "and  CASE WHEN :filterByTypeReglement !=  ''THEN REGC_Remarque=:filterByTypeReglement ELSE REGC_Remarque !=:filterByTypeReglement  END) " +

            " ORDER BY " +
            "CASE WHEN :sort_by = 'REGC_Code'  AND :isAsc = 1 THEN REGC_Code END ASC, " +
            "CASE WHEN :sort_by = 'REGC_Code'  AND :isAsc = 2 THEN REGC_Code END DESC, " +
            "CASE WHEN :sort_by = 'REGC_Montant'  AND :isAsc = 1 THEN (CAST (REGC_Montant AS REAL)) END ASC, " +
            "CASE WHEN :sort_by = 'REGC_Montant'  AND :isAsc = 2 THEN (CAST (REGC_Montant AS REAL)) END DESC, " +
            "CASE WHEN :sort_by = 'REGC_DateReg'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) END ASC, " +
            "CASE WHEN :sort_by = 'REGC_DateReg'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',REGC_DateReg) END DESC ",
    )
    fun getAllFiltred(
        isAsc: Int,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        sort_by: String,
        station: String,
    ): Flow<List<ReglementCaisseWithTicketAndClient>>
}
