package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.DrawerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUserWithImmobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.view_model.DeplacementOutByUserViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible

@Composable
fun DeplacementOutByUserScreenContent(
    popBackStack: () -> Unit,
    isConnected: Boolean,
    immobilisationList: List<Immobilisation>,
    showNavIcon: Boolean = true,
    navIcon: Int,
    showOnlyWaiting: Boolean = false,
    title: String,
    drawer: DrawerState,
    dataViewModel: DataViewModel,
    allBatimentListstate: DeplacementOutByUserListState,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    deplacementOutByUserViewModel: DeplacementOutByUserViewModel,
    onItemClick: (DeplacementOutByUserWithImmobilisation) -> Unit
) {
    val deplacementOutByUser: MutableList<DeplacementOutByUserWithImmobilisation> = arrayListOf()
    val ligneBonCommande: MutableList<LigneBonCommande> = arrayListOf()

    val context = LocalContext.current




    val listState = rememberLazyListState()


    val listOrder = allBatimentListstate.listOrder
    val listFilter = allBatimentListstate.search
    val filterList = context.resources.getStringArray(R.array.depout_byuser_filter)

    val isVisible = floatingBtnIsVisible(listeSize = allBatimentListstate.lists.size, canScrollForward = listState.canScrollForward)
    val scope = rememberCoroutineScope()
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig


    val searchTextState = deplacementOutByUserViewModel.searchTextState
    allBatimentListstate.lists.forEach { (key, value) ->
        run {
            deplacementOutByUser.add(key)
            ligneBonCommande.addAll(value)
        }
    }

    LaunchedEffect(
        key1 = searchTextState.text,
        key2 = allBatimentListstate.lists,
        key3 = allBatimentListstate.search,
    ) {
        deplacementOutByUserViewModel.filterInvPatrimoine(
            deplacementOutByUserFilterListState = allBatimentListstate,
        )
    }

    LaunchedEffect(key1 = showOnlyWaiting) {
        if(showOnlyWaiting) {
            // Deplacement In screen - show only "En Instance" records
            deplacementOutByUserViewModel.onEvent(
                ListEvent.FirstCustomFilter("1")
            )
        } else {
            // Navigation Drawer screen - show ALL records (both "En Instance" and "Validée")
            deplacementOutByUserViewModel.onEvent(
                ListEvent.FirstCustomFilter("")
            )
        }
    }


    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = isConnected,
                onNavigationClick = {
                    // Always call popBackStack since we're using custom menu icon to navigate to shortcuts
                    popBackStack()
                },
                showNavIcon = !deplacementOutByUserViewModel.showSearchView && showNavIcon,
                navIcon = navIcon,
                title = title,
                titleVisibilty = !deplacementOutByUserViewModel.showSearchView,
                actions = {
                    SearchSectionComposable(
                        label =
                        context.getString(
                            R.string.filter_by,
                            when (listFilter) {
                                is ListSearch.FirstSearch -> filterList[0]
                                is ListSearch.SecondSearch -> filterList[1]
                                else -> filterList[2]
                            },
                        ),
                        searchVisibility = deplacementOutByUserViewModel.showSearchView,
                        searchTextState = searchTextState,
                        onSearchValueChange = {
                            deplacementOutByUserViewModel.onSearchValueChange(TextFieldValue(it))
                        },
                        onShowSearchViewChange = {
                            deplacementOutByUserViewModel.onShowSearchViewChange(it)
                        },
                        onShowCustomFilterChange = {
                            deplacementOutByUserViewModel.onShowCustomFilterChange(it)
                        },
                    )
                },
            )
        },
        //    containerColor = colorResource(id = R.color.black),
    ) { padding ->

        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
            Modifier
                //  .background(colorResource(id = R.color.black))
                .fillMaxSize()
                .padding(padding),
        ) {
            if (deplacementOutByUserViewModel.showCustomFilter) {
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.depout_byuser_order),
                    onShowCustomFilterChange = {
                        deplacementOutByUserViewModel.onShowCustomFilterChange(false)
                    },
                    onEvent = {
                        deplacementOutByUserViewModel.onEvent(
                            event = it,
                        )
                    },
                    customFilterContent = {
                        if(showOnlyWaiting) return@FilterContainer
                        var showCustomFilterSeachSection by rememberSaveable { mutableStateOf(false) }
                        val density = LocalDensity.current
                        Column(
                            verticalArrangement = Arrangement.Center,
                            horizontalAlignment = Alignment.Start,
                        ) {

                            val currentFilterValueLable = if(allBatimentListstate.onlyWaiting.isEmpty())
                                stringResource(id = R.string.tous)
                            else {
                                if(allBatimentListstate.onlyWaiting == "1") "En Instance" else "Validé"
                            }
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        showCustomFilterSeachSection = !showCustomFilterSeachSection

                                    },
                                text = "$title ($currentFilterValueLable)",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.outline,
                                maxLines = 1
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            AnimatedVisibility(
                                visible =  showCustomFilterSeachSection,
                                enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
                                exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
                            ) {

                                SingleChoiceSegmentedButtonRow(
                                    modifier = Modifier.fillMaxWidth()
                                ) {

                                    SegmentedButton(
                                        shape = SegmentedButtonDefaults.itemShape(index = 0, count = 3),
                                        onClick = {
                                            deplacementOutByUserViewModel.onEvent(
                                            ListEvent.FirstCustomFilter("")
                                        )
                                                  },
                                        selected = allBatimentListstate.onlyWaiting.isEmpty()
                                    ) {
                                        Text(
                                            modifier = Modifier.horizontalScroll(rememberScrollState()),
                                            text= stringResource(id = R.string.tous),
                                            style = MaterialTheme.typography.bodySmall,
                                            softWrap = false,
                                            maxLines = 1
                                        )
                                    }

                                    SegmentedButton(
                                        shape = SegmentedButtonDefaults.itemShape(index = 1, count = 3),
                                        onClick = {
                                            deplacementOutByUserViewModel.onEvent(
                                            ListEvent.FirstCustomFilter("1")
                                        )
                                                  },
                                        selected = allBatimentListstate.onlyWaiting == "1"
                                    ) {
                                        Text(
                                            modifier = Modifier.horizontalScroll(rememberScrollState()),
                                            text= stringResource(id = R.string.en_instance),
                                            style = MaterialTheme.typography.bodySmall,
                                            softWrap = false,
                                            maxLines = 1
                                        )
                                    }


                                    SegmentedButton(
                                        shape = SegmentedButtonDefaults.itemShape(index = 2, count = 3),
                                        onClick = {
                                            deplacementOutByUserViewModel.onEvent(
                                            ListEvent.FirstCustomFilter("2")
                                        )
                                                  },
                                        selected = allBatimentListstate.onlyWaiting == "2"
                                    ) {
                                        Text(
                                            modifier = Modifier.horizontalScroll(rememberScrollState()),
                                            text= stringResource(id = R.string.validee),
                                            style = MaterialTheme.typography.bodySmall,
                                            softWrap = false,
                                            maxLines = 1
                                        )
                                    }
                                }
                            }
                        }
                    }
                )
            }

            if(getProCaisseDataViewModel.deplacementOutByUserResponseState.loading) {
                LottieAnim(lotti = R.raw.loading, size = 250.dp)
            }
            else {
                if (allBatimentListstate.lists.isNotEmpty()) {
                    DeplacementOutByUserList(
                        isConnected = isConnected,
                        immobilisationList = immobilisationList,
                        selectedBaseconfig = selectedBaseconfig,
                        getProCaisseDataViewModel = getProCaisseDataViewModel,
                        listState = listState,
                        filteredZoneConsomation = allBatimentListstate.lists,
                        ligneBonCommande = ligneBonCommande,
                        deplacementOutByUser = deplacementOutByUser,
                        onItemClick = {
                            onItemClick(it)
                        }
                    )
                } else {
                    LottieAnim(lotti = R.raw.emptystate, size = 250.dp)

                    if(searchTextState.text.isBlank()) {
                        OutlinedButton(onClick = {
                            getProCaisseDataViewModel.getDeplacementOutByUser(
                                baseConfig = selectedBaseconfig,
                                listImmobilisation = immobilisationList
                            )
                        }) {

                            Text(text = stringResource(id = R.string.get_data))
                        }
                    }
                }
            }

        }
    }
}