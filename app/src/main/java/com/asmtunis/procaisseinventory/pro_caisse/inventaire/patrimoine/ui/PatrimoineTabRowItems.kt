package com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui

import androidx.compose.ui.text.input.TextFieldValue
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.utils.StringUtils.nbrTitleTabs
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.filter.InventairePatrimoineFilterListState
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.InventaireViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.TabRowItem

fun getPatrimoineTabRowItems(
    filteredDepOutList: Map<BonCommande, List<LigneBonCommande>>,
    filteredDepInList: Map<BonCommande, List<LigneBonCommande>>,
    filteredInventaireList: Map<BonCommande, List<LigneBonCommande>>,
    filteredAffectationList: Map<BonCommande, List<LigneBonCommande>>,
    depOutListNotFiltred: Map<BonCommande, List<LigneBonCommande>>,
    depInListNotFiltred: Map<BonCommande, List<LigneBonCommande>>,
    inventaireListNotFiltred: Map<BonCommande, List<LigneBonCommande>>,
    affectationListNotFiltred: Map<BonCommande, List<LigneBonCommande>>,
    invPatViewModel: InventaireViewModel,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    navigate: (route: Any) -> Unit,
    getClientName: (cltName: String?, cltCode: String?) -> String,
    // Optional parameters for lazy loading
    utilisateur: Utilisateur? = null,
    devEtat: String? = null,
    searchTextState: TextFieldValue? = null,
    invePatrimoineFilterListState: InventairePatrimoineFilterListState? = null
): List<TabRowItem> = listOf(
    TabRowItem(
        title = TypePat.AFFECTATION.typePat+  nbrTitleTabs(allSize = affectationListNotFiltred.size, filteredSize = filteredAffectationList.size) ,
        id = TypePat.AFFECTATION.typePat,
        screen = {
            InvPatList(
                navigate = { navigate(it) },
                invPatrimoineViewModel = invPatViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                inPatList = filteredAffectationList,
                getClientName = { cltName,cltCode ->
                   getClientName(cltName, cltCode)
                },
                // Pass lazy loading parameters
                utilisateur = utilisateur,
                devEtat = devEtat,
                searchTextState = searchTextState,
                invePatrimoineFilterListState = invePatrimoineFilterListState
            )
        }
        // icon = Icons.Rounded.Place,
    ),
    TabRowItem(
        title = TypePat.INVENTAIRE.typePat + nbrTitleTabs(allSize = inventaireListNotFiltred.size, filteredSize = filteredInventaireList.size),
        id = TypePat.INVENTAIRE.typePat,
        screen = {
            InvPatList(
                navigate = { navigate(it) },
                invPatrimoineViewModel = invPatViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                inPatList = filteredInventaireList,
                getClientName = { cltName,cltCode ->
                    getClientName(cltName, cltCode)
                },
                // Pass lazy loading parameters
                utilisateur = utilisateur,
                devEtat = devEtat,
                searchTextState = searchTextState,
                invePatrimoineFilterListState = invePatrimoineFilterListState
            )
        }
    ),
    TabRowItem(
        title = TypePat.DEP_IN.typePat + nbrTitleTabs(allSize = depInListNotFiltred.size, filteredSize = filteredDepInList.size),
        id = TypePat.DEP_IN.typePat,
        screen = {
            InvPatList(
                navigate= { navigate(it) },
                invPatrimoineViewModel = invPatViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                inPatList = filteredDepInList,
                getClientName = { cltName,cltCode ->
                    getClientName(cltName, cltCode)
                },
                // Pass lazy loading parameters
                utilisateur = utilisateur,
                devEtat = devEtat,
                searchTextState = searchTextState,
                invePatrimoineFilterListState = invePatrimoineFilterListState
            )
        }
        //  icon = Icons.Rounded.Star,
    ),
    TabRowItem(
        title = TypePat.DEP_OUT.typePat + nbrTitleTabs(allSize = depOutListNotFiltred.size, filteredSize = filteredDepOutList.size),
        id = TypePat.DEP_OUT.typePat,
        screen = {
            InvPatList(
                navigate = { navigate(it) },
                invPatrimoineViewModel = invPatViewModel,
                selectArtMobilityVM = selectArtMobilityVM,
                inPatList = filteredDepOutList,
                getClientName = { cltName,cltCode ->
                    getClientName(cltName, cltCode)
                },
                // Pass lazy loading parameters
                utilisateur = utilisateur,
                devEtat = devEtat,
                searchTextState = searchTextState,
                invePatrimoineFilterListState = invePatrimoineFilterListState
            )
        }
        //  icon = Icons.Rounded.Star,
    )
)