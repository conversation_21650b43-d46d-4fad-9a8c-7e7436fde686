package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.ligne_bon_commande.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.LIGNE_BON_COMMANDE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import kotlinx.coroutines.flow.Flow


@Dao
interface LigneBonCommandeDAO {
    @get:Query("SELECT * FROM $LIGNE_BON_COMMANDE_TABLE")
    val all: Flow<List<LigneBonCommande>>

    @Query("SELECT * FROM $LIGNE_BON_COMMANDE_TABLE where LG_DEV_Code_M =:code")
    fun getByBCCodeM(code: String): Flow<List<LigneBonCommande>>

    @Query("SELECT * FROM $LIGNE_BON_COMMANDE_TABLE where LG_DEV_Code_M =:code")
    fun getOneByBCCodeM(code: String): Flow<LigneBonCommande>

    @Query("SELECT * FROM $LIGNE_BON_COMMANDE_TABLE where LG_DEV_NumBon =:code")
    fun getByBCCode(code: String): List<LigneBonCommande>



    @get:Query("SELECT * FROM $LIGNE_BON_COMMANDE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSynced: Flow<List<LigneBonCommande>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: LigneBonCommande)

    @Query("UPDATE $LIGNE_BON_COMMANDE_TABLE SET LG_DEV_NumBon = :newNumCommande where LG_DEV_NumBon = :oldNumCommande")
    fun updateCodeBonCommande(newNumCommande: String, oldNumCommande: String)

    @Query("UPDATE $LIGNE_BON_COMMANDE_TABLE SET isSync = 1, Status= 'SELECTED', LG_DEV_NumBon = :newNum where LG_DEV_Code_M = :oldNum")
    fun setSynced(newNum: String, oldNum: String)

    @Query("UPDATE $LIGNE_BON_COMMANDE_TABLE SET Status= 'INSERTED' where LG_DEV_Code_M = :codeM")
    fun setToInserted(codeM: String)

    @Transaction
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<LigneBonCommande>)

    @Query("DELETE FROM $LIGNE_BON_COMMANDE_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $LIGNE_BON_COMMANDE_TABLE where LG_DEV_NumBon =:code")
    fun deleteByLgDevNumBon(code: String)

    @Query("DELETE FROM $LIGNE_BON_COMMANDE_TABLE where LG_DEV_Code_M =:codeM and LG_DEV_CodeArt =:artCode")
    fun deleteByCodeMAndArtCode(codeM: String, artCode: String)


    @Query("DELETE FROM $LIGNE_BON_COMMANDE_TABLE where LG_DEV_Code_M =:codeM")
    fun deleteByCodeM(codeM: String)

    @Query("DELETE FROM $LIGNE_BON_COMMANDE_TABLE where LG_DEV_Code_M =:code")
    fun deleteByCmdM(code: String)

    @Query("DELETE FROM $LIGNE_BON_COMMANDE_TABLE where LG_DEV_NumBon= :codeCommande and LG_DEV_Exerc=:exercie ")
    fun deleteById(codeCommande: String, exercie: String)

    // Méthodes pour traiter la pagination et l'insertion des lignes bon commande
    @Transaction
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLignesFromPagination(lignes: List<LigneBonCommande>)

    @Query("DELETE FROM $LIGNE_BON_COMMANDE_TABLE WHERE LG_DEV_NumBon IN (:bonCommandeNums)")
    suspend fun deleteLignesByBonCommandeNums(bonCommandeNums: List<String>)

    @Transaction
    suspend fun replaceLignesForBonCommandes(bonCommandeNums: List<String>, lignes: List<LigneBonCommande>) {
        deleteLignesByBonCommandeNums(bonCommandeNums)
        insertLignesFromPagination(lignes)
    }
}
