package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.zone_consomation

import NavDrawer
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.InventaireBatimentViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.BareCodeScannerIcon
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import kotlinx.coroutines.launch

@Composable
fun ZoneConsomationScreen(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    popBackStack: () -> Unit,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    batimentViewModel: InventaireBatimentViewModel,
    mainViewModel: MainViewModel,
    barCodeViewModel: BarCodeViewModel,
    dataViewModel: DataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    networkViewModel: NetworkViewModel,


    syncInventoryViewModel: SyncInventoryViewModel,

    settingViewModel: SettingViewModel
) {
    val uiWindowState = settingViewModel.uiWindowState

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val listState = rememberLazyListState()

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)



    val displayAllBatiment = batimentViewModel.displayAffectedBatiment

    val allBatimentListstate = batimentViewModel.allBatimentListstate

    // else batimentViewModel.state
    val listOrder = allBatimentListstate.listOrder
    val filterList = context.resources.getStringArray(R.array.zone_consomation_filter)
    val listFilter = allBatimentListstate.filter

    val isVisible = floatingBtnIsVisible(listeSize = allBatimentListstate.lists.size, canScrollForward = listState.canScrollForward)

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val barCodeInfo = barCodeViewModel.barCodeInfo
    val isConnected = networkViewModel.isConnected
    val searchTextState = batimentViewModel.searchTextState
    val showSearchView = batimentViewModel.showSearchView
    LaunchedEffect(key1 = Unit) {
        batimentViewModel.onSelectedZoneConsomationChange(Immobilisation(), "1")
    }

    LaunchedEffect(key1 = barCodeInfo) {
        if (barCodeInfo.value == "") return@LaunchedEffect


        val scannedBatiment = allBatimentListstate.lists.firstOrNull { it.cliImoCB ==  barCodeInfo.value}
        if(scannedBatiment != null) {
            batimentViewModel.onSelectedZoneConsomationChange(scannedBatiment, "2")
            batimentViewModel.resetImmobilisationTreeList()
            batimentViewModel.resetSearch()
            batimentViewModel.getParentImobilisation(codeParent = scannedBatiment.cliImoCodeP ?: "",)

            barCodeViewModel.onBarCodeInfo(barCode = BareCode())
            navigate(ZoneConsomationDetailRoute)
        }
        else {
            showToast(
                context = context,
                toaster = toaster,
                message = context.resources.getString(R.string.batiment_introuvable) + " \n"+  context.resources.getString(R.string.bar_code_value, barCodeInfo.value),
                type =  ToastType.Success,
            )
            barCodeViewModel.onBarCodeInfo(barCode = BareCode())
        }

    }

    LaunchedEffect(
        key1 = searchTextState.text,
        key2 = allBatimentListstate.lists,
        key3 = allBatimentListstate.filter,
    ) {
        batimentViewModel.onTyEmpImNomChange(Constant.ZONE_CONSOMATION)
        batimentViewModel.onDisplayAffectedBatimentChange(true)
        batimentViewModel.filterZoneConsommation(allBatimentListstate)
    }
   /* LaunchedEffect(key1 = displayAllBatiment) {
        batimentViewModel.filterZoneConsommation(allBatimentListstate)
    }*/

    BackHandler(enabled = true) {
        navigatePopUpTo(ZoneConsomationRoute, ZoneConsomationRoute, true)
    }

    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navigationDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,

        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            snackbarHost = { SnackbarHost(snackbarHostState) },
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = {
                        // Navigate to shortcuts screen instead of opening drawer
                        navigate(com.asmtunis.procaisseinventory.core.navigation.HomePageRoute)
                    },
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    title = stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title),
                    titleVisibilty = !showSearchView,
                    actions = {
                        SearchSectionComposable(
                            label =
                                context.getString(
                                    R.string.filter_by,
                                    when (listFilter) {
                                        is ListSearch.FirstSearch -> filterList[0]
                                        is ListSearch.SecondSearch -> filterList[1]
                                        else -> filterList[2]
                                    },
                                ),
                            searchVisibility = showSearchView,
                            searchTextState = searchTextState,
                            onSearchValueChange = {
                                batimentViewModel.onSearchValueChange(TextFieldValue(it))
                            },
                            onShowSearchViewChange = {
                                batimentViewModel.resetSearch()
                                batimentViewModel.onShowSearchViewChange(it)
                            },
                            onShowCustomFilterChange = {
                                batimentViewModel.onShowCustomFilterChange(it)
                            },
                        )
                    }
                )
            },
            floatingActionButton = {
                val density = LocalDensity.current

                AnimatedVisibility(
                    visible = isVisible ,
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {
                    FloatingActionButton(
                        onClick = {

                        }
                    ) {
                        BareCodeScannerIcon(
                            haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                            toaster = toaster,
                            onClick = {
                                openBareCodeScanner(
                                    navigate = { navigate(it) },
                                    onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                                )
                            }
                        )
                    }
                }
            }
        ) { padding ->

            if (batimentViewModel.showCustomFilter) {
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.zone_consomation_order),
                    onShowCustomFilterChange = {
                        batimentViewModel.onShowCustomFilterChange(false)
                    },
                    onEvent = {
                        batimentViewModel.onEvent(event = it)
                    },
                )
            }
            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier =
                Modifier
                    //  .background(colorResource(id = R.color.black))
                    .fillMaxSize()
                    .padding(padding)
                    .padding(top = 12.dp),
            ) {

               if (getProCaisseDataViewModel.immobilisationState.loading) {
                    LottieAnim(lotti = R.raw.loading, size = 250.dp)
                }
                else {
                    if (allBatimentListstate.lists.isNotEmpty()) {
                        ZoneConsomationList(
                            navigate = { navigate(it) },
                            selectedBaseconfig = selectedBaseconfig,
                            isConnected = isConnected,
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            listState = listState,
                            batimentViewModel = batimentViewModel,
                            filteredZoneConsomation = allBatimentListstate.lists,
                        )
                    } else {
                       //     batimentViewModel.filterZoneConsommation(allBatimentListstate)
                        LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                    }
                }

            }
        }
    }
}

@Composable
fun ZoneConsomationList(
    navigate: (route: Any) -> Unit,
    selectedBaseconfig: BaseConfig,
    isConnected: Boolean,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    listState: LazyListState,
    batimentViewModel: InventaireBatimentViewModel,
    filteredZoneConsomation: List<Immobilisation>,
) {

    val isRefreshing  = getProCaisseDataViewModel.immobilisationState.loading

    PullToRefreshLazyColumn(
        items = filteredZoneConsomation,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !filteredZoneConsomation.any { !it.isSync } && isConnected,
        onRefresh = { getProCaisseDataViewModel.getImmobilisation(baseConfig = selectedBaseconfig) },
        key = { zoneConsomation -> zoneConsomation.cLICode },
        content = { zoneConsomation ->
            OutlinedCard(
                modifier = Modifier.padding(start = 9.dp,end = 9.dp, top = 12.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                    modifier =
                    Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .padding(start = 9.dp, top = 12.dp)
                        .clickable {
                            batimentViewModel.resetImmobilisationTreeList()
                            batimentViewModel.resetSearch()
                            batimentViewModel.getParentImobilisation(codeParent = zoneConsomation.cliImoCodeP ?: "")
                            batimentViewModel.onSelectedZoneConsomationChange(zoneConsomation, "3")

                            navigate(ZoneConsomationDetailRoute)
                        },
                    // .background("#063041".color)
                ) {

                 if(!zoneConsomation.isSync) { LottieAnim(lotti = R.raw.connection_error, size = 80.dp) }


                    Column(
                        modifier =
                        Modifier
                            //  .background(colorResource(id =if(state.lists[index].solde.toDouble() < 0.0) R.color.teal_700 else R.color.white ))
                            .wrapContentSize(),
                        // .padding(padding) ,
                        verticalArrangement = Arrangement.SpaceEvenly,
                        horizontalAlignment = Alignment.Start,
                    ) {
                        Text(
                            text = buildAnnotatedString {
                                withStyle(
                                    SpanStyle(
                                        fontSize = MaterialTheme.typography.titleSmall.fontSize,
                                        fontWeight = MaterialTheme.typography.titleSmall.fontWeight,
                                    )
                                ) {
                                    append(zoneConsomation.cLINomPren)
                                }

                                append(" (")
                                withStyle(
                                    SpanStyle(
                                        fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                        fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                                    )
                                ) {
                                    append(zoneConsomation.cLICode)
                                }
                                append(")")
                            },

                            maxLines = 2,
                        )


                        /*   Text(
                               text = stringResource(id = R.string.code_value, filteredZoneConsomation[index].cLICode),
                               fontSize = 14.sp,
                               fontWeight = FontWeight.SemiBold,

                           )*/

                        Spacer(modifier = Modifier.height(9.dp))
                        Text(
                            modifier = Modifier.fillMaxWidth().padding(end = 12.dp),
                            textAlign = TextAlign.End,
                            text = stringResource(id = R.string.bar_code_value, zoneConsomation.cliImoCB?: "N/A"),
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                            color =
                            if (zoneConsomation.cliImoCB.isNullOrEmpty()) {
                                MaterialTheme.colorScheme.error
                            } else {
                                Color.Unspecified
                            },
                        )
                    }
                }
            }
        },
    )

}
