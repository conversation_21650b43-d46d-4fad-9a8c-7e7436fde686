package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.local.repository


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVCWithImages
import kotlinx.coroutines.flow.Flow




interface AutreVCLocalRepository {
    fun upsertAll(value: List<AutreVC>)
    fun upsert(value: AutreVC)
    fun deleteAll()
    fun deleteByCode(codeAutre: String)
    fun getAll(): Flow<List<AutreVC>>
    fun setDeleted(code: String, codeMobile: String)
    fun restDeleted(code: String,status : String, isSync:Boolean)
    fun noSyncedToAddOrUpdate(): Flow<List<AutreVC>>
    fun noSyncedToDelete(): Flow<List<AutreVC>>

    fun updateCloudCode(code: String, codeM: String)
    fun filterByNum(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<AutreVCWithImages>>
    fun filterByAutre(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<AutreVCWithImages>>

    fun filterByAutreNote(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<AutreVCWithImages>>


    fun getAllFiltred(
        isAsc: Int,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String
    ): Flow<List<AutreVCWithImages>>
}