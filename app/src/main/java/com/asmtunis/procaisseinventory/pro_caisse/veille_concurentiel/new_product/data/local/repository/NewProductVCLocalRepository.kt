package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVCWithImages
import kotlinx.coroutines.flow.Flow



interface NewProductVCLocalRepository {
    fun upsertAll(value: List<NewProductVC>)
    fun upsert(value: NewProductVC)
    fun deleteAll()
    fun delete(value: NewProductVC)
    fun deleteByCode(code: String)

    fun getNoSyncedToDelete(): Flow<List<NewProductVC>>
    fun getAll(): Flow<List<NewProductVC>>
    fun noSyncedToAddOrUpdate(): Flow<List<NewProductVC>>


    fun updateCloudCode(code: String, codeMobile: String)
    fun restDeleted(code: String,status : String, isSync:Boolean)
    fun setDeleted(code: String, codeMobile: String)

    fun filterByNum(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<NewProductVCWithImages>>
    fun filterByPrixProduct(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<NewProductVCWithImages>>

    fun filterByNewProduct(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<NewProductVCWithImages>>


    fun getAllFiltred(
        isAsc: Int,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String
    ): Flow<List<NewProductVCWithImages>>
}
