package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.dto

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande

/**
 * Extension functions to convert API DTOs to domain models
 */

/**
 * Convert BonCommandeWithLignes to BonCommande entity
 */
fun BonCommandeWithLignes.toBonCommande(): BonCommande {
    return BonCommande(
        devCodeM = this.devCodeM ?: "",
        dEVNum = this.dEVNum ?: "",
        dEVExerc = this.dEVExerc,
        dEVDate = this.dEVDate,
        dEVCodeClient = this.dEVCodeClient,
        dEVStationOrigine = this.dEVStationOrigine,
        dEVEtat = this.dEVEtat,
        dEVStation = this.dEVStation,
        dEVUser = this.dEVUser,
        dEVMntht = this.dEVMntht,
        dEVMntNetHt = this.dEVMntNetHt,
        dEVMntTva = this.dEVMntTva,
        dEVMntTTC = this.dEVMntTTC,
        dEVTauxRemise = this.dEVTauxRemise,
        dEVRemise = this.dEVRemise,
        dEVRegler = this.dEVRegler,
        dEVExport = this.dEVExport,
        dEVDDm = this.dEVDDm,
        dEVExoNum = this.dEVExoNum,
        dEVExoVal = this.dEVExoVal,
        dEVTimbre = this.dEVTimbre,
        dEVExonoration = this.dEVExonoration,
        dEVChauffeur = this.dEVChauffeur,
        dEVVehicule = this.dEVVehicule,
        dEVObservation = this.dEVObservation,
        dEVClient = this.dEVClient,
        dEVMntFodec = this.dEVMntFodec,
        dEVMntDC = this.dEVMntDC,
        dEVEtatBon = this.dEVEtatBon,
        bONLIVNum = this.bONLIVNum,
        bONLIVExerc = this.bONLIVExerc,
        dEV_info3 = this.dEV_info3,
        dEVTyMvtCode = this.dEVTyMvtCode,
        devCodeSF = this.devCodeSF,
        devCodeSO = this.devCodeSO,
        dEVClientName = this.dEVClientName,
        code = this.code,
        msg = this.msg
    )
}

/**
 * Extract LigneBonCommande data from BonCommandeWithLignes and ensure proper references
 */
fun BonCommandeWithLignes.extractLignesFromBonCommande(): List<LigneBonCommande> {
    return this.lignesDevis?.map { ligne ->
        ligne.copy(
            // Ensure proper relationship - use API values if present, otherwise use parent values
            lGDEVNumBon = if (ligne.lGDEVNumBon.isNotEmpty()) ligne.lGDEVNumBon else (this.dEVNum ?: ""),
            lGDEVExerc = if (ligne.lGDEVExerc.isNotEmpty()) ligne.lGDEVExerc else (this.dEVExerc ?: ""),
            lGDEVCodeM = ligne.lGDEVCodeM ?: this.devCodeM
        )
    } ?: emptyList()
}

/**
 * Convert LigneBonCommande + Article to LigneBonCommandeForSync
 * This includes all necessary article properties to prevent server errors like "ART_TVA of non-object"
 */
fun LigneBonCommande.toLigneBonCommandeForSync(article: Article?): LigneBonCommandeForSync {
    return LigneBonCommandeForSync(
        lGDEVNumBon = this.lGDEVNumBon,
        lGDEVCodeM = this.lGDEVCodeM,
        lGDEVExerc = this.lGDEVExerc,
        lGDEVCodeArt = this.lGDEVCodeArt,
        lGDEVQte = this.lGDEVQte,
        lGDEVUnite = this.lGDEVUnite,
        lGDEVPUHT = this.lGDEVPUHT,
        lGDEVTva = this.lGDEVTva,
        lGDEVNetht = this.lGDEVNetht,
        lGDEVRemise = this.lGDEVRemise,
        lGDEVStation = this.lGDEVStation,
        lGDEVUser = this.lGDEVUser,
        lGDEVNumOrdre = this.lGDEVNumOrdre,
        lGDEVTarif = this.lGDEVTarif,
        lGDEVQtePiece = this.lGDEVQtePiece,
        lGDEVExport = this.lGDEVExport,
        lGDEVDDm = this.lGDEVDDm,
        lGDEVMntTTC = this.lGDEVMntTTC,
        lGDEVMntHT = this.lGDEVMntHT,
        lGDEVPUTTC = this.lGDEVPUTTC,
        lGDEVTauxFodec = this.lGDEVTauxFodec,
        lGDEVTauxDc = this.lGDEVTauxDc,
        lGDEVMntBrutHT = this.lGDEVMntBrutHT,
        lGDEVMntFodec = this.lGDEVMntFodec,
        lGDEVMntDc = this.lGDEVMntDc,
        lGDEVMntTva = this.lGDEVMntTva,
        lGDEVQteGratuite = this.lGDEVQteGratuite,
        lGDevNumSerie = this.lGDevNumSerie,
        lGDEVCMarq = this.lGDEVCMarq,
        lgDEVNote = this.lgDEVNote,
        lgDevNDevIN = this.lgDevNDevIN,

        aRTCode = article?.aRTCode ?: this.lGDEVCodeArt,
        aRTTVA = article?.aRTTVA ?: run {
            this.lGDEVTva?.toDoubleOrNull() ?: 0.0
        },
        aRTDesignation = article?.aRTDesignation ?: "",
        aRTPrixUnitaireHT = article?.aRTPrixUnitaireHT ?: this.lGDEVPUHT ?: "0.0",
        aRTCodeBar = article?.aRTCodeBar ?: "",
        aRTQteStock = article?.aRTQteStock ?: "0",
        aRTUnite = article?.uNITEARTICLECodeUnite ?: this.lGDEVUnite,
        aRTFamille = article?.aRTFamille ?: "",
        aRTMarque = article?.aRTMarque ?: ""
    )
}
