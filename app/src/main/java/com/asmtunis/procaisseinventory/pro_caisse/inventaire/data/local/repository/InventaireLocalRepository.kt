package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.repository

import androidx.paging.PagingSource
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.LigneBonCommandeWithImageList
import kotlinx.coroutines.flow.Flow


interface InventaireLocalRepository {
    fun upsertAll(value: List<BonCommande>)
    fun upsert(value: BonCommande)

    fun getByNumSerie(code: String): Flow<Map<BonCommande, List<LigneBonCommande>>?>
    fun getByCodeM(codeM: String): Flow<Map<BonCommande, List<LigneBonCommande>>?>

    fun notSynced(typeInv : String, devEtat: String) : Flow<Map<BonCommande, List<LigneBonCommandeWithImageList>>>

    fun setSynced(devNum : String, devNumM: String)

    fun setToInserted(devNumM: String)
    fun setToFailed(devNumM: String)
    suspend fun getNotSyncedByType(devEtat: String): List<BonCommande>

    fun setLgSynced(devNum : String, devNumM: String)
    fun deleteAll()
    fun deleteByIdM(codeM: String)
    fun getAll(station: String, devEtat: String): Flow<Map<BonCommande, List<LigneBonCommande>>>


    fun getAllFiltred(station: String,
                      isAsc: Int,
                      sortBy: String,
                      devEtat: String,
                      nbrMonth: String,
                      year: String
    ): Flow<Map<BonCommande, List<LigneBonCommande>>>
    fun filterByNumSerie(station: String,
                         searchString: String,
                         sortBy: String,
                         isAsc: Int,
                         devEtat: String,
                         nbrMonth: String,
                         year: String
    ): Flow<Map<BonCommande, List<LigneBonCommande>>>
    fun filterByBonCommandeNum(
        station: String,
        searchString: String,
        sortBy: String,
        isAsc: Int,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): Flow<Map<BonCommande, List<LigneBonCommande>>>
    fun filterByClient(station: String,
                       searchString: String,
                       sortBy: String,
                       isAsc: Int,
                       devEtat: String,
                       nbrMonth: String,
                       year: String
    ): Flow<Map<BonCommande, List<LigneBonCommande>>>

    // Pagination methods for lazy loading
    fun getAllPatrimoinePaginated(
        station: String,
        isAsc: Int,
        sortBy: String,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): PagingSource<Int, BonCommande>

    fun filterByClientPaginated(
        station: String,
        searchString: String,
        sortBy: String,
        isAsc: Int,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): PagingSource<Int, BonCommande>

    fun filterByBonCommandeNumPaginated(
        station: String,
        searchString: String,
        sortBy: String,
        isAsc: Int,
        devEtat: String,
        nbrMonth: String,
        year: String
    ): PagingSource<Int, BonCommande>

    fun getLignesByDevNum(devNum: String): Flow<List<LigneBonCommande>>

}