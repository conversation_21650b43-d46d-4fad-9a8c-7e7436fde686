package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment

object Constant {

    // Patrimoine verification error codes (matching backend Enum.php)
    const val CODE_VERIFICATION_INVENTAIRE_EXIST = "10706"
    const val ERROR_ARTICLE_PAT_AFFECTE = "10704"
    const val ERROR_ARTICLE_OUT = "10705"
    const val ERROR_ARTICLE_IN = "10706"
    const val ERROR_INVENTAIRE_CODE_BARRE_BATIMENT = "10709"
    const val ERROR_NUM_SERIE_EGALE_CODE_BARRE_BATIMENT = "10720"
    const val SUCCESS_CODE = "10200"

    const val REGION = "Region"
    const val SOCIETE = "Societe "
    const val SITE_FINANCIER = "SITE FINANCIER"
    const val SITE_RECEPTION = "SITE RECEPTION"
    const val ZONE_CONSOMATION = "ZONE DE CONSOMMATION"
}