package com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.affectation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.SelectPatrimoineRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants
import com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column.ThreeColumnTableWithImage
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels

@Composable
fun AddAffectationScreen (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    clientId: String,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel : MainViewModel,

    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    proCaisseViewModels: ProCaisseViewModels
){
   val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
   val invPatViewModel = proCaisseViewModels.invPatViewModel


    val utilisateur = mainViewModel.utilisateur

    val  marqueList = mainViewModel.marqueList
    val  articleMapByBarCode = mainViewModel.articleMapByBarCode

    val selectedArticle =   selectPatrimoineVM.selectedPatrimoineList
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val codeM = mainViewModel.codeM


    val clientList = mainViewModel.clientList
    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode

    // Ensure marque and famille data is available for patrimoine operations
    LaunchedEffect(key1 = Unit) {
        // Load patrimoine data and check if essential data is available
        mainViewModel.loadPatrimoineData()

        // Log the current state for debugging
        android.util.Log.d("AddAffectationScreen", "Marque count: ${marqueList.size}, Famille count: ${mainViewModel.listFamille.size}")

        if (!mainViewModel.hasEssentialPatrimoineData()) {
            android.util.Log.w("AddAffectationScreen", "Essential patrimoine data missing - marques: ${marqueList.size}, familles: ${mainViewModel.listFamille.size}")
        }
    }

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM,//context.getString(R.string.invpat_number_field,"nbr todo"),
            )
        },
        floatingActionButton = {
            Column {

                FloatingActionButton(
                    onClick = {
                        navigate(SelectPatrimoineRoute)
                    }) {
                    Icon(
                        imageVector =  Icons.Default.Add,
                        contentDescription = stringResource(id = R.string.cd_achat_button)
                    )
                }
               Spacer(modifier = Modifier.height(12.dp))
                FloatingActionButton(
                    onClick = {
                            // Sauvegarde du numéro de série actuel avant de revenir à l'écran précédent
                            val numSerie = if (selectedArticle.isNotEmpty()) selectedArticle.first().numSerie else ""
                            selectPatrimoineVM.updateLastAssignedNumSerie(numSerie)

                            invPatViewModel.saveInvPat(
                                articleMapByBarCode = articleMapByBarCode,
                                codeM = codeM,
                                listSelectedPatrimoine = selectedArticle,
                                exercice = mainViewModel.getExerciceCode(),
                                client = clientByCode,
                                utilisateur = utilisateur,
                                typeInv = invPatViewModel.typeInvetaireState,
                                devEtat = Constants.PATRIMOINE,
                                onComplete = {
                                    // Retourner à l'écran précédent et reprendre le flux d'inventaire
                                    navigateUp()
                                }
                            )


                    }) {
                    Icon(
                        imageVector =  Icons.Default.Save,
                        contentDescription = stringResource(id = R.string.cd_achat_button)
                    )
                }
            }

        }
    ) { padding ->
        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            Text(text = clientByCode.cLINomPren)
            ThreeColumnTableWithImage(
                haveCamera = dataViewModel.getHaveCameraDevice(),
                marqueList = marqueList,
                articleMapByBarCode = articleMapByBarCode,
                canModify = true,
                selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList,
                onPress = {
                    //TODO Maybe show more detail : TVA / DISCOUNT / . . .
                }
            )
        }
    }
}
