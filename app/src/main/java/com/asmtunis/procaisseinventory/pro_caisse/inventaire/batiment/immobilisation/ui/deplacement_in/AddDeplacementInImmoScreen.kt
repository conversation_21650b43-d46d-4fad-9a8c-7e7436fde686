package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.deplacement_in

import android.util.Log
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountBalance
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.AddTask
import androidx.compose.material.icons.filled.Cancel
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.twotone.Home
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.MainImageTiketRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationDetailRoute
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.SetNumSerieView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.SOCIETE
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ColumnView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ItemDetailData
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.RowView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.updateInvPatQty
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.floatingActionButtonPosition
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import java.util.Locale

@Composable
fun AddDeplacementInImmoScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel: MainViewModel,
    proCaisseViewModels: ProCaisseViewModels,
    cameraViewModel: CameraViewModel,
    dataViewModel: DataViewModel,
    settingViewModel: SettingViewModel,
    barCodeViewModel: BarCodeViewModel,
    networkViewModel: NetworkViewModel,
) {
    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val imageList = mainViewModel.imageList
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val batimentViewModel = proCaisseViewModels.batimentViewModel
    val deplacementOutByUserViewModel = proCaisseViewModels.deplacementOutByUserViewModel
    val invPatViewModel = proCaisseViewModels.invPatViewModel
    val context = LocalContext.current
    val selectedZoneConsomation = batimentViewModel.selectedZoneConsomation
    val selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList
    val selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine
    val marqueList = mainViewModel.marqueList
    val invPatByNumSerie = selectPatrimoineVM.invPatByNumSerie

    val barCodeInfo = barCodeViewModel.barCodeInfo
    val codeM = mainViewModel.codeM
    val fiterValue = selectPatrimoineVM.fiterValue
    val utilisateur = mainViewModel.utilisateur
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val selectedListLg = deplacementOutByUserViewModel.selectedListLg
    val selectedDeplacementOutByUser = deplacementOutByUserViewModel.selectedDeplacementOutByUser

    val patrimoineVerificationState = selectPatrimoineVM.patrimoineVerificationState


    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val haveCamera = dataViewModel.getHaveCameraDevice()
    val allArticleAreInDepOut =
        selectedPatrimoineList.size == selectedListLg.size && verifyNumSeriesInSecondList(
            firstList = selectedPatrimoineList,
            secondList = selectedListLg
        )
    LaunchedEffect(key1 = cameraViewModel.listImgeUri.size) {
        if (selectedPatrimoine.numSerie.isEmpty()) return@LaunchedEffect

        if (selectedPatrimoineList.first {
                it.numSerie == selectedPatrimoine.numSerie
            }.imageList.any { it.imgUrl == cameraViewModel.currentImageUri.toString() }
        ) {
            return@LaunchedEffect
        }

        /*  selectPatrimoineVM.addItemToSelectedPatrimoineList(
            selectedPatrimoine.copy(imageList = cameraViewModel.listImgeUri.filter { it.devNum == selectedPatrimoine.numSerie }),
        )*/

        val combinedImageSet = mutableSetOf<ImagePieceJoint>()

        combinedImageSet.addAll(
            cameraViewModel.listImgeUri.filter {
                it.vcNumSerie == selectedPatrimoine.numSerie
            },
        )

        combinedImageSet.addAll(selectedPatrimoine.imageList)

        selectPatrimoineVM.addItemToSelectedPatrimoineList(
            selectedPatrimoine.copy(imageList = combinedImageSet.toList()),
        )
    }
    val density = LocalDensity.current

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { mainViewModel.onShowDismissScreenAlertDialogChange(true) },
                navIcon = Icons.TwoTone.Home,
                title = codeM,
                // context.getString(R.string.invpat_number_field,"nbrtodo"),
            )
        },
        floatingActionButtonPosition = floatingActionButtonPosition(windowSize = windowSize),
        floatingActionButton = {
            Column {
                FloatingActionButton(
                    onClick = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(SelectedPatrimoine())
                        selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                        selectPatrimoineVM.resetPatrimoineVerificationState()
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(id = R.string.cd_achat_button),
                    )
                }
                if (selectedPatrimoineList.isNotEmpty()) Spacer(modifier = Modifier.height(12.dp))
                AnimatedVisibility(
                    visible = selectedPatrimoineList.isNotEmpty(),
                    enter =
                    slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit =
                    fadeOut(
                        animationSpec =
                        keyframes {
                            this.durationMillis = 120
                        },
                    ),
                ) {
                    FloatingActionButton(
                        onClick = {
                            val invStationOrigineIsFromUtil =
                                dataViewModel.getInventaireStationOrigineIsFromUtilisateur()
                            val societe =
                                batimentViewModel.immobilisationTreeList.firstOrNull { it.tyEmpImNom == SOCIETE }


                            val station = mainViewModel.stationList.firstOrNull {
                                it.sTATDesg.lowercase(Locale.ROOT) == societe?.cLINomPren?.lowercase(
                                    Locale.ROOT
                                )
                            }

                            // Vérifier si c'est un Deplacement In créé depuis l'écran État

                            if (selectedDeplacementOutByUser.deplacementOutByUser != null) {
                                // Utiliser la méthode spécialisée pour validation automatique
                                invPatViewModel.saveDeplacementInFromEtatScreen(
                                    articleMapByBarCode = articleMapByBarCode,
                                    idStationOrigine = if (invStationOrigineIsFromUtil) utilisateur.Station else station?.sTATCode
                                        ?: "",
                                    codeM = codeM,
                                    listSelectedPatrimoine = selectedPatrimoineList,
                                    exercice = mainViewModel.exerciceList.firstOrNull()?.exerciceCode ?: "2024",
                                    utilisateur = utilisateur,
                                    typeInv = TypePatrimoine.ENTREE.typePat,
                                    devEtat = Constants.IMMOBILISATION,
                                    selectedZoneConsomation = selectedZoneConsomation,
                                    selectedDeplacementOutByUser = selectedDeplacementOutByUser,
                                    onComplete = { navigate(ZoneConsomationDetailRoute) }
                                )
                            } else {
                                // Utiliser la méthode standard pour les autres cas
                                invPatViewModel.saveInvPat(
                                    articleMapByBarCode = articleMapByBarCode,
                                    idStationOrigine = if (invStationOrigineIsFromUtil) utilisateur.Station else station?.sTATCode
                                        ?: "",
                                    codeM = codeM,
                                    listSelectedPatrimoine = selectedPatrimoineList,
                                    exercice = mainViewModel.exerciceList.firstOrNull()?.exerciceCode ?: "2024",
                                    utilisateur = utilisateur,
                                    typeInv = TypePatrimoine.ENTREE.typePat,
                                    devEtat = Constants.IMMOBILISATION,
                                    selectedZoneConsomation = selectedZoneConsomation,
                                    selectedDeplacementOutByUser = selectedDeplacementOutByUser,
                                    onComplete = { navigate(ZoneConsomationDetailRoute) }
                                )
                            }
                            //  navController.navigateUp()

                        },
                    ) {
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = stringResource(id = R.string.cd_achat_button),
                        )
                    }
                }
            }
        },
    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                navigate(ZoneConsomationDetailRoute)
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)

        )
        CustomAlertDialogue(
            title = context.getString(R.string.delete),
            msg = context.getString(R.string.confirm_remove_ligne_data),
            openDialog = invPatViewModel.showAlertDialog.first,
            setDialogueVisibility = {
                invPatViewModel.onShowAlertDialogChange(Pair(it, SelectedPatrimoine()))
            },
            customAction = {
                selectPatrimoineVM.deleteItemToSelectedPatrimoineList(invPatViewModel.showAlertDialog.second)
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)
        )
        if (selectPatrimoineVM.showSetNumeSerie) {
            SetNumSerieView(
                haveCamera = haveCamera,
                articleMapByBarCode = articleMapByBarCode,
                marqueList = marqueList,
                selectedPatrimoine = selectedPatrimoine,
                selectedPatrimoineList = selectedPatrimoineList,
                onNumSerieChange = { selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(numSerie = it)) },
                onDismiss = {
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                    selectPatrimoineVM.resetPatrimoineVerificationState()
                    selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                    selectPatrimoineVM.onBackUpSelectedPatrimoineChange(SelectedPatrimoine())
                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                },
                onConfirm = {
                    val controlInvPat =
                        ControleInventaire(
                            LG_DEV_NumSerie = selectedPatrimoine.numSerie,
                            DEV_CodeClient = selectedZoneConsomation.cLICode,
                            DEV_info3 = TypePatrimoine.ENTREE.typePat,
                        )

                    selectPatrimoineVM.patrimoineVerification(
                        baseConfig = mainViewModel.selectedBaseconfig,
                        controlPatrimoine = controlInvPat,
                    )
                },
                onAddInvPat = {
                    val codeArt =
                        if (invPatByNumSerie.isNotEmpty()) {
                            invPatByNumSerie.entries.first().value.first()!!.lGDEVCodeArt
                        } else {
                            "N/A"
                        }

                    updateInvPatQty(
                        imageList = imageList,
                        invPatByNumSerie = invPatByNumSerie,
                        articleCode = articleMapByBarCode[codeArt]?.aRTCode?: "",
                        numeSerie = selectedPatrimoine.numSerie,
                        patrimoineVerificationState = patrimoineVerificationState,
                        selectedPatrimoineList = selectedPatrimoineList,
                        addItemToSelectedPatrimoineList = {
                            selectPatrimoineVM.deleteSelectedPatrimoine(selectPatrimoineVM.backUpSelectedPatrimoine)// delete current line to insert a new one
                            selectPatrimoineVM.addItemToSelectedPatrimoineList(it)
                        },
                        marque =
                        if (invPatByNumSerie.isNotEmpty()) {
                            marqueList.firstOrNull {
                                it.mARCode == (invPatByNumSerie.entries.first().value.first()?.lGDEVCMarq
                                    ?: "")
                            } ?: Marque()
                        } else {
                            Marque()
                        },
                        note = selectedPatrimoine.note,
                    )
                },
                onBareCodeScan = {
                    openBareCodeScanner(
                        navigate = { navigate(it) },
                        onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) },
                    )
                },
                barCodeInfo = barCodeInfo,
                patrimoineVerificationState = patrimoineVerificationState,
                showDropDownMenuComposable = false,
                onNoteChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(note = it))
                },
            )
        }


        /*   ThreeColumnTable(
                showFilterLine = selectedPatrimoineList.size > 6 && selectPatrimoineVM.showFilterLine,
                onShowFilterLineChange = {
                    selectPatrimoineVM.onShowFilterLineChange(it)
                },
                fiterValue = fiterValue,

                onFilterValueChange = {
                    selectPatrimoineVM.onFilterValueChange(it)
                },
                canModify = true,
                listArticle = mainViewModel.articleList,
                selectedPatrimoineList = if(fiterValue.isNotEmpty()) selectedPatrimoineList.filter { it.numSerie.lowercase(
                    Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT)) ||it.article.aRTCode.lowercase(
                    Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT))  || it.article.aRTDesignation.lowercase(
                    Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT)) } else selectedPatrimoineList,
                onPress = {
                    selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                    selectPatrimoineVM.setSelectedPat(it)
                    selectPatrimoineVM.onShowSetNumeSerieChange(true)
                },
                onLongPress = {
                    invPatViewModel.onShowAlertDialogChange(Pair(true, it))
                },
                onSwipeToDelete = {
                  //  selectPatrimoineVM.deleteItemToSelectedPatrimoineList(it)
                },
                onPressTakeImage = {
                    selectPatrimoineVM.setSelectedPat(it)

                    cameraViewModel.onNumChange(value = it.numSerie)
                     navigate(MainImageTiketRoute)
                },
                onPressSeeImage = {
                    selectPatrimoineVM.setSelectedPat(it)
                    cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                },
            )

            if (cameraViewModel.openVerticalalImagePagerDialog && selectedPatrimoineList.map { it.imageList }.isNotEmpty()) {
                VerticalImagePager(
                    canModify = true,
                    onDismissRequest = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(false)
                    },
                    //  imageList =  cameraViewModel.listImgeUri.filter { it.devNum == selectedPatrimoine.numSerie },
                    //   imageList =  selectedPatrimoineList.flatMap { it.imageList.toList() }.toList().filter { it.devNum == selectedPatrimoine.numSerie },
                    imageList = selectedPatrimoine.imageList,
                    onDeleteClick = {
                        //    cameraViewModel.onImageDeleted(it)

                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                )
            }

        */

        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact -> {

                ColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.zone_Consomation),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = Icons.Default.AccountBalance,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = ItemDetailData(
                        modifier = Modifier,
                        title = stringResource(id = R.string.articles_dans_dep_out),
                        dataText = selectedPatrimoineList.size.toString() + "/" + selectedListLg.size.toString(),
                        icon = if (allArticleAreInDepOut) Icons.Default.AddTask else Icons.Default.Cancel,
                        tint = if (allArticleAreInDepOut)
                            LocalContentColor.current
                        else MaterialTheme.colorScheme.error
                    ),
                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = cameraViewModel.openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(
                            it
                        )
                    },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    }


                )

            }

            WindowWidthSizeClass.Expanded,
            WindowWidthSizeClass.Medium -> {
                RowView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    padding = padding,
                    selectedPatrimoine = selectedPatrimoine,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.zone_Consomation),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = Icons.Default.AccountBalance,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = ItemDetailData(
                        modifier = Modifier,
                        title = stringResource(id = R.string.articles_dans_dep_out),
                        dataText = selectedPatrimoineList.size.toString() + "/" + selectedListLg.size.toString(),
                        icon = if (allArticleAreInDepOut) Icons.Default.AddTask else Icons.Default.Cancel,
                        tint = if (allArticleAreInDepOut)
                            LocalContentColor.current
                        else MaterialTheme.colorScheme.error
                    ),
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = cameraViewModel.openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(it)
                    },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    }


                )

            }

            else -> {
                ColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.zone_Consomation),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = Icons.Default.AccountBalance,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = ItemDetailData(
                        modifier = Modifier,
                        title = stringResource(id = R.string.articles_dans_dep_out),
                        dataText = selectedPatrimoineList.size.toString() + "/" + selectedListLg.size.toString(),
                        icon = if (allArticleAreInDepOut) Icons.Default.AddTask else Icons.Default.Cancel,
                        tint = if (allArticleAreInDepOut)
                            LocalContentColor.current
                        else MaterialTheme.colorScheme.error
                    ),

                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = cameraViewModel.openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(it)
                    },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    }


                )
            }
        }


    }

}


fun verifyNumSeriesInSecondList(
    firstList: List<SelectedPatrimoine>,
    secondList: List<LigneBonCommande>
): Boolean {
    val numSeriesSet = secondList.map { it.lGDevNumSerie ?: "" }.toSet()
    return firstList.all { numSeriesSet.contains(it.numSerie) }
}
