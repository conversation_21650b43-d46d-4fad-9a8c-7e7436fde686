package com.asmtunis.procaisseinventory.pro_caisse.reglement.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementUpdate
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.domaine.ReglementWithChequeAndTicketResto
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

@HiltViewModel
class SyncReglementViewModel
    @Inject
    constructor(
        @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
        @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
        @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
        @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
        private val proCaisseRemote: ProCaisseRemote,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        private val listenNetwork: ListenNetwork,
        // app: Application
    ) : ViewModel() {

    var reglementLibreNotSync: List<ReglementWithChequeAndTicketResto> by mutableStateOf(emptyList())
        private set

    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)


    init {
            notSyncedReglementLibre()
        }



        private fun notSyncedReglementLibre() {
            viewModelScope.launch {
                // TODO HANDLE CASE WHEN CLIENT IS NOT SYNC §§

                val reglementLibreNotSyncFlow =  proCaisseLocalDb.reglementCaisse.getAllNotSynced().distinctUntilChanged()


                combine(networkFlow, reglementLibreNotSyncFlow, autoSyncFlow) { isConnected, reglementLibreNotSyncList, autoSync ->
                    connected = isConnected
                    autoSyncState = autoSync
                    reglementLibreNotSyncList?.ifEmpty { emptyList() }?: emptyList()
                }.collect {

                    if (it.isEmpty()) {
                        reglementLibreNotSync = emptyList()
                        return@collect
                    }
                    reglementLibreNotSync = it
                    if(connected && autoSyncState) syncReglementLibre()
                }

            }
        }

        var reglementCaisseUpdateState: RemoteResponseState<List<ReglementUpdate>> by mutableStateOf(RemoteResponseState())
            private set
    var notSyncReglementCaisseUpdateObj : String by mutableStateOf("")
        private set
        fun syncReglementLibre(selectedRegCaisse: ReglementCaisseWithTicketAndClient = ReglementCaisseWithTicketAndClient()) {

            val listToSync = if(selectedRegCaisse!=ReglementCaisseWithTicketAndClient())
               listOf(reglementLibreNotSync.first { it.reglementCaisse ==  selectedRegCaisse.reglementCaisse})
                else reglementLibreNotSync
            viewModelScope.launch(dispatcherIO) {
                val baseConfigObj =
                    GenericObject(
                        proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                        Json.encodeToJsonElement(listToSync),
                    )

                notSyncReglementCaisseUpdateObj = Json.encodeToString(baseConfigObj)
                proCaisseRemote.reglementCaisse.addBatchPayments(notSyncReglementCaisseUpdateObj)
                    .onEach { result ->
                        when (result) {
                            is DataResult.Success -> {
                                for (response in result.data!!) {
                                    // TODO HANDLE DIFFRENT CODE  if (response.code == "10200") {
                                    proCaisseLocalDb.reglementCaisse.updateRegCodeAndState(
                                        regCode = response.rEGC_Code!!,
                                        regCodeM = response.rEGC_Code_M!!,
                                    )

                                    proCaisseLocalDb.chequeCaisse.updateRegCodeAndState(
                                        regCode = response.rEGC_Code!!,
                                        regCodeM = response.rEGC_Code_M!!,
                                    )

                                    proCaisseLocalDb.ticketResto.updateRegCodeAndState(
                                        regCode = response.rEGC_Code!!,
                                        regCodeM = response.rEGC_Code_M!!,
                                    )

                                    updateClientMoney(
                                        codeClt = response.codeClient!!,
                                        soldClient = response.soldeClient!!,
                                        CLICredit = response.credit!!,
                                        CLIDebit = response.debit!!,
                                    )
                                    //  }
                                }

                                reglementCaisseUpdateState =
                                    RemoteResponseState(
                                        data = result.data,
                                        loading = false,
                                        error = null,
                                    )
                            }

                            is DataResult.Loading -> {
                                reglementCaisseUpdateState =
                                    RemoteResponseState(
                                        data = null,
                                        loading = true,
                                        error = null,
                                    )
                            }

                            is DataResult.Error -> {
                                reglementCaisseUpdateState =
                                    RemoteResponseState(
                                        data = null,
                                        loading = false,
                                        error = result.message,
                                        message = selectedRegCaisse.reglementCaisse?.rEGCCode
                                    )
                            }
                        }
                    }.flowOn(dispatcherIO).launchIn(this)
            }
        }

        private fun updateClientMoney(
            codeClt: String,
            soldClient: String,
            CLICredit: String,
            CLIDebit: String,
        ) {
            viewModelScope.launch(dispatcherIO) {
                proCaisseLocalDb.clients.updateMoneyClient(
                    codeClt = codeClt,
                    soldClient = soldClient,
                    cliCredit = CLICredit,
                    cliDebit = CLIDebit,
                )
            }
        }
    }
