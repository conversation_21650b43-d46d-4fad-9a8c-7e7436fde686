package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

//@Entity(tableName = ProCaisseConstants.VISITE_TABLE, primaryKeys = ["VIS_Code_M", "VIS_Exerc"])
@Entity(tableName = ProCaisseConstants.VISITE_TABLE)
@Serializable
data class VisitesDn @OptIn(ExperimentalSerializationApi::class
) constructor(

    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,
    @ColumnInfo(name = "VIS_Code_M")
    @SerialName("VIS_Code_M")
    var vIS_Code_M: String = "",

    @ColumnInfo(name = "VIS_Num")
    @SerialName("VIS_Num")
    var vIS_Num: String = "",

    @ColumnInfo(name = "VIS_Exerc")
    @SerialName("VIS_Exerc")
    var vIS_Exerc: String = "",

    @ColumnInfo(name = "VIS_Date")
    @SerialName("VIS_Date")
    var vIS_Date: String? = "",

    @ColumnInfo(name = "VIS_CodeClient")
    @SerialName("VIS_CodeClient")
    var vIS_CodeClient: String? = "",

    @ColumnInfo(name = "VIS_NomClient")
    @Transient
    var vIS_NomClient: String? = "",

    @ColumnInfo(name = "VIS_User")
    @SerialName("VIS_User")
    var vIS_User: String? = "",

    @ColumnInfo(name = "VIS_NomMagazin")
    @SerialName("VIS_NomMagazin")
    var vIS_NomMagazin: String? = "",

    @ColumnInfo(name = "VIS_Gouvernorat")
    @SerialName("VIS_Gouvernorat")
    var vIS_Gouvernorat: String? = "",

    @ColumnInfo(name = "VIS_Delegations")
    @SerialName("VIS_Delegations")
    var vIS_Delegations: String? = "",

    @ColumnInfo(name = "VIS_Adresse")
    @SerialName("VIS_Adresse")
    var vIS_Adresse: String? = "",

    @ColumnInfo(name = "VIS_NomGerant")
    @SerialName("VIS_NomGerant")
    var vIS_NomGerant: String = "",

    @ColumnInfo(name = "VIS_NumTele")
    @SerialName("VIS_NumTele")
    @EncodeDefault(EncodeDefault.Mode.ALWAYS)
    var vIS_NumTele: String? = "",

    @ColumnInfo(name = "VIS_TypePV")
    @SerialName("VIS_TypePV")
    var vIS_TypePV: String = "",

    @ColumnInfo(name = "VIS_TypeServ")
    @SerialName("VIS_TypeServ")
    var vIS_TypeServ: String = "",

    @ColumnInfo(name = "VIS_Superf")
    @SerialName("VIS_Superf")
    var vIS_Superf: String = "",

    @ColumnInfo(name = "VIS_DDM")
    @SerialName("VIS_DDM")
    var vIS_DDM: String? = "",

    @ColumnInfo(name = "VIS_Info1")
    @SerialName("VIS_Info1")
    @EncodeDefault(EncodeDefault.Mode.ALWAYS)
    var vIS_Info1: String? = "",

    @ColumnInfo(name = "VIS_Latitude")
    @SerialName("VIS_Latitude")
    var vIS_Latitude: String? = "",

    @ColumnInfo(name = "VIS_Longitude")
    @SerialName("VIS_Longitude")
    var vIS_Longitude: String? = "",

    @ColumnInfo(name = "VIS_Info2")
    @SerialName("VIS_Info2")
    @EncodeDefault(EncodeDefault.Mode.ALWAYS)
    var vIS_Info2: String? = "",

    @ColumnInfo(name = "VIS_Info3")
    @SerialName("VIS_Info3")
    @EncodeDefault(EncodeDefault.Mode.ALWAYS)
    var vIS_Info3: String? = ""
) : BaseModel()