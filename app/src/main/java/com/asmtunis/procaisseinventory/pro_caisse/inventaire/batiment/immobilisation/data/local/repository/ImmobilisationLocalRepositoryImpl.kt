package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao.ImmobilisationDAO
import kotlinx.coroutines.flow.Flow


class ImmobilisationLocalRepositoryImpl(private val immobilisationDAO: ImmobilisationDAO) :
    ImmobilisationLocalRepository {
    override fun getAll(): Flow<List<Immobilisation>?> = immobilisationDAO.getAll()
    override fun getNotSync(): Flow<List<Immobilisation>> = immobilisationDAO.getNotSync

    override fun setIsBatimentUser(cliCode: String) = immobilisationDAO.setIsBatimentUser(cliCode= cliCode)

    override fun getAllZoneConsomationByImoCB(imoCB: String): Flow<List<Immobilisation>?> =
        immobilisationDAO.getAllZoneConsomationByImoCB(imoCB = imoCB)

    override fun getParent(codeParent: String): Flow<Immobilisation?> =
        immobilisationDAO.getParent(codeParent = codeParent)


    override fun insert(item: Immobilisation) = immobilisationDAO.insert(item)

    override fun insertAll(items: List<Immobilisation>) = immobilisationDAO.insertAll(items)

    override fun deleteAll() = immobilisationDAO.deleteAll()
    override fun setZoneConsomationImmoCB(
        immoCB: String,
        cliCode: String,
        isSynced: Boolean,
        status: String
    ) = immobilisationDAO.setZoneConsomationImmoCB(
        immoCB= immoCB,
        cliCode = cliCode,
        isSynced= isSynced,
        status = status
    )

    override fun getAllFiltred(isAsc: Int,
                               sortBy: String,
                               byUser: Boolean,
                               tyEmpImNom: String
    ): Flow<List<Immobilisation>> =
        immobilisationDAO.getAllFiltred(
            isAsc = isAsc,
            sortBy = sortBy,
            byUser = byUser,
            tyEmpImNom = tyEmpImNom
        )

    override fun filterByCltImoCB(
        filterString: String,
        sortBy: String,
        isAsc: Int,
        byUser: Boolean,
        tyEmpImNom: String
    ): Flow<List<Immobilisation>> =
        immobilisationDAO.filterByCltImoCB(
            filterString = filterString,
            sortBy = sortBy,
            isAsc = isAsc,
            byUser = byUser,
            tyEmpImNom = tyEmpImNom
        )

    override fun filterByCLICode(
        filterString: String,
        sortBy: String,
        isAsc: Int,
        byUser: Boolean,
        tyEmpImNom: String
    ): Flow<List<Immobilisation>> =
        immobilisationDAO.filterByCLICode(
            filterString = filterString,
            sortBy = sortBy,
            isAsc = isAsc,
            byUser = byUser,
            tyEmpImNom = tyEmpImNom
        )

    override fun filterByName(
        filterString: String,
        sortBy: String,
        isAsc: Int,
        byUser: Boolean,
        tyEmpImNom: String
    ): Flow<List<Immobilisation>> =
        immobilisationDAO.filterByName(
            filterString = filterString,
            sortBy = sortBy,
            isAsc = isAsc,
            byUser = byUser,
            tyEmpImNom = tyEmpImNom
        )


}