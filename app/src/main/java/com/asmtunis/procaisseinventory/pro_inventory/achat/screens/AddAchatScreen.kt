package com.asmtunis.procaisseinventory.pro_inventory.achat.screens


import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.ADMIN
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.SelectArticlesScreensCalculRoute
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.ui.SetArticleDialogue
import com.asmtunis.procaisseinventory.pro_inventory.achat.AchatViewModel
import com.asmtunis.procaisseinventory.pro_inventory.text_validation.InventoryFormEvent
import com.asmtunis.procaisseinventory.pro_inventory.text_validation.InventoryTextValidationViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AddViewBottomAppBar
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableHeader
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils.infoText
import com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column.FiveColumnTable
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.date_time_picker.DatePickerView
import com.simapps.ui_kit.date_time_picker.TimePickerView
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.drop_down_menu.LargeDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField
import kotlinx.coroutines.launch
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddAchatScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    achatViewModel: AchatViewModel,
    barCodeViewModel: BarCodeViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    selectArtCalculVM: SelectArticleCalculViewModel,
    inventoryTextValidationViewModel: InventoryTextValidationViewModel = hiltViewModel(),
    navigationDrawerViewModel: NavigationDrawerViewModel,
) {

    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val scrollState = rememberScrollState()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    val showBottomSheet = achatViewModel.showBottomSheet
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)


    val fournisseurFilter = achatViewModel.fournisseurFilter

    val fournisseurList = mainViewModel.fournisseurList

    val state = inventoryTextValidationViewModel.stateAddNew
    val selectedBonEntree = achatViewModel.selectedBonEntree
    val ligneBonEntree = achatViewModel.selectedListLgBonEntree

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val isAutoScanMode = mainViewModel.isAutoScanMode
    val utilisateur = mainViewModel.utilisateur

    val selectedArticle = selectArtCalculVM.selectedArticle

    val selectedArtList = selectArtCalculVM.selectedArticleList

    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val tvaList = mainViewModel.tvaList

    val selectedArticleMobilityList = selectArtCalculVM.selectedArticleList

    val controlQuantity = selectArtCalculVM.controlQuantity
    val validationAddEvents = inventoryTextValidationViewModel.validationAddEvents

    val isAdmin = utilisateur.typeUser.lowercase(Locale.getDefault()).contains(ADMIN)


    val stationList = if (isAdmin)
        mainViewModel.stationList
    else mainViewModel.stationList.filter { it.sTATCode == utilisateur.Station }


    val barCodeInfo = barCodeViewModel.barCodeInfo

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }

    LaunchedEffect(key1 = Unit) {
        if (utilisateur.typeUser.lowercase() != ADMIN && state.station == Station()) {
            val station = stationList.first { it.sTATCode == utilisateur.Station }
            inventoryTextValidationViewModel.onAddNewEvent(InventoryFormEvent.stationChanged(station))
        }

        inventoryTextValidationViewModel.onAddNewEvent(
            InventoryFormEvent.selectedListArticleChanged(
                selectArtCalculVM.selectedArticleList
            )
        )

    }

    LaunchedEffect(key1 = selectedArticle) {
        inventoryTextValidationViewModel.onAddNewEvent(
            InventoryFormEvent.selectedArticleChanged(selectedArticle.article)
        )
    }

    LaunchedEffect(key1 = barCodeInfo.value) {
        if (barCodeInfo.value == "")// when screen rotate
            return@LaunchedEffect


        selectArtCalculVM.handleBareCodeResult(
            errorMessage = context.resources.getString(R.string.article_introvable, ""),
            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) },
            barCodeInfo = barCodeInfo,
            isAutoScanMode = isAutoScanMode,
            articleMapByBarCode = articleMapByBarCode,
            useSalePrice = false,
            tvaList = tvaList,
            showToast = { message, type ->
                showToast(
                    context = context,
                    toaster = toaster,
                    message = message,
                    type = type,
                )

            }
        )
    }

    LaunchedEffect(key1 = validationAddEvents) {

        achatViewModel.handleAddAchatEvents(
            toaster = toaster,
            codeM = mainViewModel.codeM,
            popBackStack = {
                mainViewModel.resetTimePicker()
                popBackStack()
            },
            validationAddEvents = validationAddEvents,
            context = context,
            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) },
            exerciceList = mainViewModel.exerciceList,
            stateAddNew = inventoryTextValidationViewModel.stateAddNew,
            controlQuantity = controlQuantity,
            selectedArticleList = selectedArticleMobilityList,
            selectedArticle = selectedArticle,
            isAutoScanMode = isAutoScanMode,
            utilisateur = mainViewModel.utilisateur,
            listActifTimber = mainViewModel.listActifTimber,
            selectedDateTime = mainViewModel.getSelectedDateTime(),
            listStationStockArticl = mainViewModel.stationStockArticlMapByBarCode,
            tvaList = mainViewModel.tvaList,
            setSelectedArticl = {
                selectArtCalculVM.setSelectedArticl(
                    article = it,
                    tvaList = tvaList
                )
            },
            addOneItemToSelectedArticleMobilityList = {
                selectArtCalculVM.addOneItemToSelectedArticleMobilityList(
                    it
                )
            },
            updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                mainViewModel.updateQtePerStation(
                    newQteStation = newQteStation,
                    newSartQteDeclare = newSartQteDeclare,
                    codeArticle = codeArticle,
                    codeStation = codeStation
                )
            },
            updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                mainViewModel.updateArtQteStock(
                    newQteAllStations = newQteAllStations,
                    newQteStation = newQteStation,
                    codeArticle = codeArticle
                )
            }
        )
    }


    val totPriceWithoutDicount = selectArtCalculVM.totPriceWithoutDicount

    val showBottomSheetAfterClickSave =
        !state.typePieceError?.asString().isNullOrEmpty() || !state.numPieceError?.asString()
            .isNullOrEmpty() || !state.fournisseurError?.asString()
            .isNullOrEmpty() || !state.stationError?.asString().isNullOrEmpty()
    LaunchedEffect(
        key1 = showBottomSheetAfterClickSave,
        key2 = selectedArticleMobilityList.isNotEmpty()
    ) {

        if (showBottomSheetAfterClickSave || selectedArticleMobilityList.isEmpty()) {
            achatViewModel.onShowBottomSheetChange(true)
        }
    }


    LaunchedEffect(key1 = selectedArticleMobilityList.size) {
        if (selectedArticleMobilityList.isEmpty()) {
            selectArtCalculVM.onTotalPriceAfterDiscountChange(value = "")
            return@LaunchedEffect
        }
        selectArtCalculVM.setTotalPrices()
        selectArtCalculVM.setTotalPriceWithoutDicount(value = selectedArtList.sumOf {
            stringToDouble(
                it.lTMtTTC
            )
        })
    }

    if (mainViewModel.showDatePicker) {
        DatePickerView(
            setDateVisibility = {
                mainViewModel.onShowDatePickerChange(it)
            },
            onSelectedDateChange = {
                mainViewModel.onSelectedDateChange(it)
                mainViewModel.onShowTimePickerChange(true)
            },
            confirmText = stringResource(R.string.confirm),
            cancelText = stringResource(R.string.cancel)
        )
    }

    if (mainViewModel.showTimePicker) {
        TimePickerView(
            setTimeVisibility = {
                mainViewModel.onShowTimePickerChange(it)
            },
            onSelectedTimeChange = {
                mainViewModel.onSelectedTimeChange(it)
            },
            confirmText = stringResource(R.string.confirm),
            cancelText = stringResource(R.string.cancel)
        )
    }



    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    mainViewModel.onShowDismissScreenAlertDialogChange(true)
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = achatViewModel.bonEntreeGeneratedNum,
            )
        },
        bottomBar = {


            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.background(MaterialTheme.colorScheme.background)
            ) {
                if (selectedArticleMobilityList.isNotEmpty())
                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.85f),
                        text = convertStringToPriceFormat(totPriceWithoutDicount.toString()),
                        errorValue = null,
                        label = context.getString(R.string.total),
                        onValueChange = {
                            //  onTotalPriceWithDiscountChange(it)
                        },
                        readOnly = true,
                        enabled = true,
                        showTrailingIcon = false,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next
                    )

                Spacer(modifier = Modifier.height(18.dp))
                AddViewBottomAppBar(
                    haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                    toaster = toaster,
                    showSaveBtn = selectedArticleMobilityList.isNotEmpty(),
                    onSaveClick = {
                        inventoryTextValidationViewModel.onAddNewEvent(
                            InventoryFormEvent.SubmitAchat
                        )
                    },
                    onClickAddArticle = {
                        selectArtCalculVM.onShowTvaChange(true)
                        selectArtCalculVM.setControlQte(false)
                        navigate(SelectArticlesScreensCalculRoute)
                    },
                    isAutoScanMode = isAutoScanMode,
                    setAutoScanMode = { mainViewModel.setAutoAddMode(!isAutoScanMode) },
                    openBareCodeScanner = {
                        openBareCodeScanner(
                            navigate = { navigate(it) },
                            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                        )
                    },
                    showBareCodeScannerBtn = barCodeViewModel.haveCameraDevice
                )
            }

        }

    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                popBackStack()
            },
            confirmText = stringResource(R.string.oui),
            cancelText = stringResource(R.string.non)

        )

        CustomAlertDialogue(
            title = context.getString(R.string.delete_confirmation_msg),
            msg = context.getString(R.string.confirm_remove_ligne_data),
            openDialog = selectArtCalculVM.showDeleteConfirmationDialog,
            setDialogueVisibility = {
                selectArtCalculVM.onShowDeleteConfirmationDialogChange(it)
            },
            customAction = {
                selectArtCalculVM.confirmDeleteArticle()
            },
            confirmText = stringResource(R.string.oui),
            cancelText = stringResource(R.string.non),
            negatifAction = {
                selectArtCalculVM.cancelDeleteArticle()
            }
        )

        if (showBottomSheet) {
            ModalBottomSheet(
                sheetState = sheetState,
                onDismissRequest = {
                    scope.launch {
                        sheetState.hide()
                    }

                    achatViewModel.onShowBottomSheetChange(false)
                },
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 12.dp, end = 12.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    GenericDropdownMenu(
                        modifier = Modifier.fillMaxWidth(0.45f),
                        designation = state.typePiece,
                        errorValue = state.typePieceError?.asString(),
                        label = stringResource(R.string.type_piece),
                        readOnly = true,
                        itemList = achatViewModel.typePieceList,
                        itemExpanded = achatViewModel.typePieceExpanded,
                        selectedItem = state.typePiece,
                        getItemDesignation = { it },
                        onClick = {
                            inventoryTextValidationViewModel.onAddNewEvent(
                                InventoryFormEvent.typePieceChanged(
                                    it
                                )
                            )
                            achatViewModel.onTypePieceExpandedChange(false)
                        },
                        onItemExpandedChange = {
                            achatViewModel.onTypePieceExpandedChange(it)
                        },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                        lottieAnimError = {
                            LottieAnim(lotti = R.raw.connection_error, size = it)
                        }
                    )




                    Spacer(modifier = Modifier.width(12.dp))



                    EditTextField(
                        modifier = Modifier.fillMaxWidth(),
                        showLeadingIcon = false,
                        text = state.numPiece,
                        errorValue = state.numPieceError?.asString(),
                        label = stringResource(R.string.num_piece),
                        onValueChange = {
                            inventoryTextValidationViewModel.onAddNewEvent(
                                InventoryFormEvent.numPieceChanged(it)
                            )
                        },
                        readOnly = false,
                        enabled = true,
                        leadingIcon = Icons.Default.Person,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )
                }
                Spacer(modifier = Modifier.height(12.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 12.dp, end = 12.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    LargeDropdownMenu(
                        modifier = Modifier.customWidth(0.45f),
                        designation = state.fournisseur.fRSNomf,
                        errorValue = state.fournisseurError?.asString(),
                        label = stringResource(R.string.fournisseur),
                        readOnly = true,
                        fiterValue = fournisseurFilter,
                        onFilterValueChange = { achatViewModel.onFournisseurFilterChange(it) },
                        showFilter = true,
                        itemExpanded = achatViewModel.fournisseurExpand,
                        selectedItem = state.fournisseur,
                        itemList =
                        if (fournisseurFilter.isNotEmpty()) fournisseurList.filter {
                            it.fRSNomf.lowercase(
                                Locale.ROOT
                            ).contains(fournisseurFilter.lowercase(Locale.ROOT))
                        } else fournisseurList,
                        getItemDesignation = { it.fRSNomf },
                        getItemTrailing = { it.fRSCodef },
                        onClick = {
                            inventoryTextValidationViewModel.onAddNewEvent(
                                InventoryFormEvent.fournisseurChanged(
                                    it
                                )
                            )
                            achatViewModel.onFournisseurExpandedChange(false)
                        },
                        onItemExpandedChange = {
                            achatViewModel.onFournisseurExpandedChange(it)
                        },
                    )

                    Spacer(modifier = Modifier.width(12.dp))
                    GenericDropdownMenu(
                        modifier = Modifier.fillMaxWidth(),
                        designation = state.station.sTATDesg,
                        errorValue = state.stationError?.asString(),
                        label = stringResource(R.string.station),
                        readOnly = true,
                        itemList = stationList,
                        itemExpanded = mainViewModel.stationExpand,
                        selectedItem = state.station,
                        getItemTrailing = { it.sTATCode },
                        getItemDesignation = { it.sTATDesg },
                        onClick = {
                            inventoryTextValidationViewModel.onAddNewEvent(
                                InventoryFormEvent.stationChanged(it)
                            )
                            mainViewModel.onStationExpandedChange(false)
                        },
                        onItemExpandedChange = {
                            mainViewModel.onStationExpandedChange(it)
                        },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                        lottieAnimError = {
                            LottieAnim(lotti = R.raw.connection_error, size = it)
                        }
                    )


                }

                Spacer(modifier = Modifier.height(24.dp))
            }
        }


        if (selectArtCalculVM.showSetArticle) {
            SetArticleDialogue(
                toaster = toaster,
                isProInventory = true,
                hasPromo = false,
                selectedArticle = selectedArticle,
                priceCategoryList = selectArtCalculVM.priceCategoryList,
                tvaExpanded = mainViewModel.tvaExpand,
                showTva = true,
                tvaList = mainViewModel.tvaList,
                selectedTva = selectedArticle.tva,
                unitArticleExpanded = mainViewModel.uniteArticleExpand,
                unitArticleList = mainViewModel.uniteArticleList.filter { it.uNITEARTICLECodeArt == selectedArticle.article.aRTCode },
                onSelectedUnitArticleChange = {
                    selectArtCalculVM.setSelectedArticll(selectedArticle.copy(uniteArticle = it))
                    selectArtCalculVM.updateSelectedArticleMobilityList()
                                              },
                onUnitArticleExpandedChange = { mainViewModel.onUniteArticleExpandedChange(it) },
                updateQty = {
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        //context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        quantiti = it,
                        typeOperation = "qty",
                        useSalePrice = false
                    )
                },
                onConfirm = {
                    if (stringToDouble(selectedArticle.quantity) <= 0.0 || stringToDouble(selectedArticle.lTMtBrutHT) <= 0.0) {

                        selectArtCalculVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article)

                        return@SetArticleDialogue
                    }
                    //  selectArtCalculVM.setTotalPrices()
                    selectArtCalculVM.setTotalPriceWithoutDicount(value = selectedArtList.sumOf { stringToDouble(it.lTMtTTC) })


                },
                onReset = {
                    //  selectArtCalculVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article)

                },
                onRemiseChange = {
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        remise = it,
                        typeOperation = "remise",
                        useSalePrice = false
                    )
                },
                setShowPriceCategoryChange = {
                    selectArtCalculVM.setShowPriceCategoryChange(it)
                },
                onShowSetArticleChange = {
                    selectArtCalculVM.onShowSetArticleChange(it)
                },
                setShowPriceCategorySingleArticleChange = {
                    selectArtCalculVM.setShowPriceCategorySingleArticleChange(it)
                },
                getSingleArticlePrice = {
                    selectArtCalculVM.getPrice(it.article)
                },
                onSelectedPriceCategorieChange = {
                    selectArtCalculVM.onSelectedPriceCategoryChange(it)
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        //   context = context,
                        operation = Globals.NO_OPERATION,
                        quantiti = selectedArticle.quantity,
                        useSalePrice = false
                    )
                },
                onPrixCaisseChange = {
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        // barCodeViewModel = barCodeViewModel,
                        typeOperation = "prixCaisse",
                        prixCaisse = it,
                        useSalePrice = false
                    )
                },
                onPrixTotalChange = {
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        typeOperation = "totalPrice",
                        prixtotal = it,
                        useSalePrice = false
                    )
                },

                onPrixHTChange = {
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        typeOperation = "prixAchatHT",
                        prixHT = it,
                        useSalePrice = false
                    )
                },
                showPriceCategorySingleArticle = selectArtCalculVM.showPriceCategorySingleArticle,
                onSelectedTvaChange = {
                    //TOdo add calcul
                    //  mainViewModel.onSelectedTvaChange(it)
                    //selectArtCalculVM.setSelectedArticlTva(it)
                    selectArtCalculVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        tva = it,
                        typeOperation = "tva",
                        useSalePrice = false
                    )
                },
                onTvaExpandedChange = {
                    mainViewModel.onTvaExpandedChange(it)
                },
                onPrixVenteChange = {
                    selectArtCalculVM.setSelectedArticlPrixVente(it)
                }
            )
        }


        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(padding)
        ) {

            TableHeader(
                onClickShowCalendar = {
                    mainViewModel.onShowDatePickerChange(true)
                },
                date = selectedBonEntree.bONENTDateFormatted,
                canModify = true,
                selectedDateTime = mainViewModel.getSelectedDateTime(),
                isExpanded = showBottomSheet,
                showExpanIcon = true,
                expanIconColor = if (showBottomSheetAfterClickSave) MaterialTheme.colorScheme.error else LocalContentColor.current,
                onExpand = {
                    achatViewModel.onShowBottomSheetChange(true)
                }
            )


            FiveColumnTable(
                rowTitls = context.resources.getStringArray(R.array.fourColumnTable_array).toList(),
                // client = bonCommandeVM.selectedBonCommandeWithClient.client?.cLINomPren?:bonCommandeVM.selectedBonCommandeWithClient.bonCommande?.dEVClientName?:mainViewModel.selectedClient.cLINomPren,//mainViewModel.selectedClient.cLINomPren,
                selectedListArticle = selectedArticleMobilityList,
                canModify = true,
                onPress = {
                    selectArtCalculVM.setSelectedArticl(article = it.article, tvaList = tvaList)

                },
                onLongPress = {


                },
                onSwipeToDelete = {
                    selectArtCalculVM.requestDeleteArticle(it.article)
                },
                onTap = {
                    selectArtCalculVM.onShowSetArticleChange(true)

                },
                firstColumn = { item ->
                    articleMapByBarCode[item.article.aRTCode]?.aRTDesignation
                        ?: context.resources.getString(
                            R.string.article_introvable, item.article.aRTCode
                        )
                },
                secondColumn = {
                    convertStringToPriceFormat(it.lTPuTTC)
                },
                thirdColumn = {
                    it.quantity
                },
                forthColumn = {
                    convertStringToPriceFormat(it.lTMtTTC)
                },
                infoText = {
                    infoText(selectedArticle = it, isAchat = true)
                }
            )
            if (state.listSelectedArticleError?.asString(context) != null && selectedArticleMobilityList.isEmpty())
                Text(
                    text = state.listSelectedArticleError.asString(context)!!,
                    color = MaterialTheme.colorScheme.error,
                    fontSize = MaterialTheme.typography.bodyLarge.fontSize,
                    // modifier = Modifier.align(Alignment.Start)
                )




            Spacer(modifier = Modifier.height(18.dp))


        }
    }
}





