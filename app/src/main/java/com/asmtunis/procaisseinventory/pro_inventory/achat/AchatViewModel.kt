package com.asmtunis.procaisseinventory.pro_inventory.achat

import android.content.Context
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.updateArticleQty
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.updateStationArticleQte
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.BL
import com.asmtunis.procaisseinventory.core.Globals.FACTURE
import com.asmtunis.procaisseinventory.core.Globals.THIS_MONTH
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.timbersValueSum
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.screens.filter.AchatFilterListState
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import com.asmtunis.procaisseinventory.pro_inventory.text_validation.InventoryFormState
import com.asmtunis.procaisseinventory.pro_inventory.text_validation.ValidationAddNewEvent
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
    class AchatViewModel @Inject constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val proInventoryRemote: ProInventoryRemote,
        private val proInventoryLocalDb: ProInventoryLocalDb,
        private val proCaisseLocalDb: ProCaisseLocalDb
    ) : ViewModel() {
    var showFilterLine by mutableStateOf(false)
        private set
    fun onShowFilterLineChange(value: Boolean) {
        showFilterLine = value
    }


    var fiterValue by mutableStateOf("")
        private set
    fun onFilterValueChange(value: String) {
        fiterValue = value
    }



    var fournisseurFilter: String by mutableStateOf("")
        private set
    fun onFournisseurFilterChange(value: String) {
        fournisseurFilter = value
    }

    var showBottomSheet: Boolean by mutableStateOf(false)
        private set
    fun onShowBottomSheetChange(value: Boolean) {
        showBottomSheet = value
    }


    var selectedMonthNumber: String by mutableStateOf(THIS_MONTH)
        private set
    fun onSelectedMonthsNbrChange(value: String) {
        selectedMonthNumber = value
    }



    val typePieceList = listOf(BL, FACTURE)

    var showSearchView: Boolean by mutableStateOf(false)
        private set
    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }

    var showCustomFilter: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }

    var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }


    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
        filterAchat(achatListstate)
    }







    private fun saveBonEntree(bonEntree: BonEntree) {
        viewModelScope.launch(dispatcher) {
            proInventoryLocalDb.bonEntree.upsert(bonEntree)
        }
    }

    fun saveListLigneBnEntree(bonEntree: List<LigneBonEntree>) {
        viewModelScope.launch(dispatcher) {
            proInventoryLocalDb.ligneBonEntree.upsertAll(bonEntree)
        }
    }

    private fun saveLigneBnEntree(bonEntree: LigneBonEntree) {
        viewModelScope.launch(dispatcher) {
            proInventoryLocalDb.ligneBonEntree.upsert(bonEntree)
        }
    }


    var bonEntreeGeneratedNum: String by mutableStateOf("")
        private set
    fun generateBonEntreeNum(prefix : String) {

        viewModelScope.launch {
            proInventoryLocalDb.bonEntree.getNewCode(prefix).collect {
                bonEntreeGeneratedNum =/*"Inv_M" +"_" +*/prefix +/*"_${stationUtilisateur}_${userID}_"  +*/ it
            }
        }
    }







    var typePieceExpanded: Boolean by mutableStateOf(false)
        private set
    fun onTypePieceExpandedChange(value: Boolean) {
        typePieceExpanded = value
    }


    var fournisseurExpand: Boolean by mutableStateOf(false)
        private set
    fun onFournisseurExpandedChange(value: Boolean) {
        fournisseurExpand = value
    }





    var selectedListLgBonEntree = mutableStateListOf<LigneBonEntree>()
        private set

 /*   var selectedLigneBonEntree: LigneBonEntree by mutableStateOf(LigneBonEntree())
        private set
    fun onSelectedLigneBonEntreeChange(value: LigneBonEntree) {
        selectedLigneBonEntree = value
    }*/

    var selectedBonEntree: BonEntree by mutableStateOf(BonEntree())
        private set
    fun onSelectedBonEntreeChange(value: Map<BonEntree, List<LigneBonEntree>>) {
            restBonEntree()

            value.forEach { (key, value) ->
                run {
                    selectedBonEntree = key
                    selectedListLgBonEntree.addAll(value)
                }
            }

    }


    fun deleteLigneBonEntree(value : LigneBonEntree){

      //  selectedListLgBonEntree.removeIf { it.lIGBonEntreeCodeArt == value.lIGBonEntreeCodeArt}
        val indexToDelete =   selectedListLgBonEntree.indexOfFirst { it.lIGBonEntreeCodeArt ==value.lIGBonEntreeCodeArt }
        val updatedList = selectedListLgBonEntree.filterIndexed { index, _ -> index != indexToDelete }
            .mapIndexed { index, ligneBonEntree -> ligneBonEntree.copy(ordre = index + 1) }

        selectedListLgBonEntree.clear()
        selectedListLgBonEntree.addAll(updatedList)
    }
    fun restBonEntree(){
        selectedListLgBonEntree.clear()
        selectedBonEntree = BonEntree()
    }


   /* fun getLigneBonEntree(article: Article) {
        selectedLigneBonEntree =
            if(selectedListLgBonEntree.any { it.lIGBonEntreeCodeArt == article.aRTCode })
                selectedListLgBonEntree.filter { it.lIGBonEntreeCodeArt == article.aRTCode }[0]
            else LigneBonEntree()
    }*/


    var achatListstate: AchatFilterListState by mutableStateOf(
        AchatFilterListState()
    )
        private set
    fun onEvent(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (achatListstate.listOrder::class == event.listOrder::class &&
                    achatListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                achatListstate = achatListstate.copy(
                    listOrder = event.listOrder
                )
                filterAchat(achatListstate)
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()


            is ListEvent.ListSearch -> {
                achatListstate = achatListstate.copy(
                    search = event.listSearch
                )

                filterAchat(achatListstate)
            }

            is ListEvent.FirstCustomFilter -> {
                achatListstate = achatListstate.copy(
                    filterByStationEntree = event.firstFilter
                )

                filterAchat(achatListstate)
            }

            is ListEvent.SecondCustomFilter -> {
                achatListstate = achatListstate.copy(
                    filterByFournisseur = event.secondFiter
                )

                filterAchat(achatListstate)
            }

            is ListEvent.ThirdCustomFilter -> TODO()

        }

    }
    var isLoadingFromLocalDb: Boolean by mutableStateOf(false)
        private set
    var getAchatJob: Job = Job()
    fun filterAchat(achatListState: AchatFilterListState) {
        val searchedText = searchTextState.text
        val searchValue = achatListState.search
        val filterByStationEntree = achatListState.filterByStationEntree
        val filterByFournisseur = achatListState.filterByFournisseur

        getAchatJob.cancel()
        isLoadingFromLocalDb = true
        if (searchedText.isEmpty()) {
            getAchatJob = when (achatListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (achatListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proInventoryLocalDb.bonEntree.getAllFiltred(
                                isAsc = 1,
                                sortBy = "BON_ENT_Num",
                                filterByStationEntree = filterByStationEntree,
                                filterByFournisseur= filterByFournisseur,
                                
                            ).collect {
                                setAchatList(bonEntree = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proInventoryLocalDb.bonEntree.getAllFiltred(
                                isAsc = 1,
                                sortBy = "BON_ENT_Date",
                                filterByStationEntree = filterByStationEntree,
                                filterByFournisseur= filterByFournisseur,
                                
                            ).collect {
                                setAchatList(bonEntree = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proInventoryLocalDb.bonEntree.getAllFiltred(
                                isAsc = 1,
                                sortBy = "BON_ENT_NumPiece",
                                filterByStationEntree = filterByStationEntree,
                                filterByFournisseur= filterByFournisseur,
                                
                            ).collect {
                                setAchatList(bonEntree = it)
                            }
                        }
                    }
                }

                is OrderType.Descending -> {
                    when (achatListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proInventoryLocalDb.bonEntree.getAllFiltred(
                                isAsc = 2,
                                sortBy = "BON_ENT_Num",
                                filterByStationEntree = filterByStationEntree,
                                filterByFournisseur= filterByFournisseur,
                                ).collect {
                                setAchatList(bonEntree = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proInventoryLocalDb.bonEntree.getAllFiltred(
                                isAsc = 2,
                                sortBy = "BON_ENT_Date",
                                filterByStationEntree = filterByStationEntree,
                                filterByFournisseur= filterByFournisseur,
                                
                            ).collect {
                                setAchatList(bonEntree = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proInventoryLocalDb.bonEntree.getAllFiltred(
                                isAsc = 2,
                                sortBy = "BON_ENT_NumPiece",
                                filterByStationEntree = filterByStationEntree,
                                filterByFournisseur= filterByFournisseur,
                                
                            ).collect {
                                setAchatList(bonEntree = it)
                            }
                        }
                    }
                }
            }
        } else {
            if (searchedText.isNotEmpty()) {
                if (searchValue is ListSearch.FirstSearch) {
                    getAchatJob = when (achatListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (achatListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNum(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Num",
                                        isAsc = 1,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNum(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Date",
                                        isAsc = 1,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNum(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_NumPiece",
                                        isAsc = 1,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (achatListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNum(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Num",
                                        isAsc = 2,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNum(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Date",
                                        isAsc = 2,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNum(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_NumPiece",
                                        isAsc = 2,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.SecondSearch) {
                    getAchatJob =  when (achatListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (achatListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNumPiece(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Num",
                                        isAsc = 1,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNumPiece(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Date",
                                        isAsc = 1,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNumPiece(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_NumPiece",
                                        isAsc = 1,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (achatListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNumPiece(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Num",
                                        isAsc = 2,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNumPiece(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Date",
                                        isAsc = 2,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntNumPiece(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_NumPiece",
                                        isAsc = 2,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.ThirdSearch) {
                    getAchatJob =  when (achatListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (achatListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntCodeFrs(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Num",
                                        isAsc = 1,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntCodeFrs(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Date",
                                        isAsc = 1,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntCodeFrs(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_NumPiece",
                                        isAsc = 1,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (achatListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntCodeFrs(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Num",
                                        isAsc = 2,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntCodeFrs(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_Date",
                                        isAsc = 2,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonEntree.filterByBonEntCodeFrs(
                                        searchString = searchedText,
                                        sortBy = "BON_ENT_NumPiece",
                                        isAsc = 2,
                                        filterByStationEntree = filterByStationEntree,
                                        filterByFournisseur= filterByFournisseur,
                                        
                                    ).collect {
                                        setAchatList(bonEntree = it)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }




    private fun setAchatList(bonEntree:  Map<BonEntree, List<LigneBonEntree>>){
        isLoadingFromLocalDb = false
        achatListstate = achatListstate.copy(
            lists =  emptyMap() // ,
            // listOrder = articlesListState.listOrder
        )

        achatListstate = achatListstate.copy(
            lists = bonEntree // ,
            // listOrder = articlesListState.listOrder
        )

      //  getAchatJob.cancel()
    }




    fun deleteBonEntree(
        bonEntree: BonEntree,
        listLgBonEntree: List<LigneBonEntree>,
        articleMapByBarCode: Map<String, Article>,
        allStationStockArticle: Map<String, StationStockArticle>,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {
        viewModelScope.launch(dispatcher) {
            proInventoryLocalDb.bonEntree.delete(bonEntree)
            proInventoryLocalDb.ligneBonEntree.deleteAllList(listLgBonEntree)

            for (lgBonEntree in listLgBonEntree) {
                val articl = articleMapByBarCode[lgBonEntree.lIGBonEntreeCodeArt] ?: return@launch

                val codStation = bonEntree.bONENTStationEntree?:""


              //  val stationStockArticle = allStationStockArticle.firstOrNull { it.sARTCodeSatation == codStation && (it.sARTCodeArt == articl.aRTCode || it.sARTCodeArt == articl.aRTCodeBar)}?: StationStockArticle()

                val stationStockArticle = allStationStockArticle[articl.aRTCode + codStation]?: StationStockArticle()


                val quatity = stringToDouble(lgBonEntree.lIGBonEntreeQte)



                updateStationArticleQte(
                    opeartion = Globals.MINUS,
                    codeStation = codStation,
                    quatity = quatity,
                    aRTCode = lgBonEntree.lIGBonEntreeCodeArt,
                    stationStockArticle = stationStockArticle,
                    updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation->
                        updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)
                    }
                )

                updateArticleQty(
                    operation = Globals.MINUS,
                    quantity = quatity,
                    article = articl,
                    updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                         updateArtQteStock(
                            newQteAllStations,
                            newQteStation,
                            codeArticle
                        )
                    }
                )

            }
        }
    }



    private fun saveBonAchat(
        exerciceList: List<Exercice>,
        selectedArticleList: List<SelectedArticle>,
        listStationStockArticl: Map<String, StationStockArticle>,
        input: InventoryFormState,
        utilisateur: Utilisateur,
        codeM: String,
        listActifTimber: List<Timbre>,
        selectedDateTime: String,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {
        val userId = utilisateur.codeUt
        val  typePiece = input.typePiece
        val bONENTMntTTC = if(typePiece == FACTURE)
            selectedArticleList.sumOf { stringToDouble(it.lTMtTTC) } + timbersValueSum(listActifTimber = listActifTimber)
        else  selectedArticleList.sumOf { stringToDouble(it.lTMtTTC) }


        val bonEntree = BonEntree(
            //  val id: Long = 0,
            bONENTNumM = codeM,
            bONENTNum = bonEntreeGeneratedNum,
            bONENTExer = exerciceList.firstOrNull()?.exerciceCode ?: "2024",
            bONENTDate = selectedDateTime,
            bONENTStationEntree = input.station.sTATCode,
            bONENTCodeFrs = input.fournisseur.fRSCodef,
            bONENTUser = userId,
            bONENTType = Globals.BE,
            bONENTSYNC = "0",
            bONENTNumPiece = input.numPiece,
            bONENTTypePiece = typePiece,
            timestamp = 0,
            bONENTENTTC = "false",
            bONENTETAT = "false",
            bONENTTauxRemise = (selectedArticleList.sumOf { stringToDouble(it.discount)} / selectedArticleList.size).toString(),// todo add if selectArtCalculVM.totalDiscount not empty,
            bONENTRemise = selectedArticleList.sumOf { stringToDouble(it.mntDiscount) }.toString(),
            bONENTMntTva = selectedArticleList.sumOf { stringToDouble(it.mntTva) }.toString(),
            bONENTMntHT = selectedArticleList.sumOf { stringToDouble(it.lTMtBrutHT) }.toString(),
            bONENTMntTTC  = bONENTMntTTC.toString(),
            bONENTMntNetHt  = selectedArticleList.sumOf { stringToDouble(it.lTMtNetHT) }.toString(),

            bONENTDDm = null,
            bONENTDevise = null,
            bONENTFact = null,
            bONENTFraisTansport = "0.0",
            bONENTMntDC = "0.0",
            bONENTMntFodec = "0.0",
            bONENTMont = null,
            bONENTReg = null,
            bONENTRegler = "false",
            bONENTStation = null,
            bONENTTauxEchange = "0.0",
            bONENTExport = "false",
            dDmM = null,
            docEtat = null,
            numBC = null,
            numBEMobile = null,
            ddm = null,
            export = "false",
            exportM = null
        )

        bonEntree.isSync = false
        bonEntree.status = ItemStatus.INSERTED.status

         saveBonEntree(bonEntree)

        saveListLgBonEntree(
            input = input,
            listStationStockArticl = listStationStockArticl,
            selectedArticleInventoryList = selectedArticleList,
            codeM = codeM,
            exerciceList = exerciceList,
            selectedDateTime = selectedDateTime,
            updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                updateArtQteStock(
                  newQteAllStations,
                    newQteStation,
                   codeArticle
                )
            },
            updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
               updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)
            }
        )
    }

    private fun saveListLgBonEntree(
        input: InventoryFormState,
        listStationStockArticl: Map<String, StationStockArticle>,
        selectedArticleInventoryList: List<SelectedArticle>,
        exerciceList: List<Exercice>,
        selectedDateTime: String,
        codeM: String,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {
        for (i in selectedArticleInventoryList.indices) {
            val articl = selectedArticleInventoryList[i].article

            val numOrder = i + 1
            val ligneBonEntree = LigneBonEntree(
                lIGBonEntreeNumBon = bonEntreeGeneratedNum,
                lIGBonEntreeCodeArt = articl.aRTCode,
                lIGBonEntreeExerc = exerciceList.firstOrNull()?.exerciceCode ?: "2024",
                lIGBonEntreeQte = selectedArticleInventoryList[i].quantity,
                lIGBonEntreeUnite = selectedArticleInventoryList[i].uniteArticle.uNITEARTICLECodeUnite,
                lIGBonEntreePUHT = selectedArticleInventoryList[i].lTPuHT,
                lIGBonEntreeTVA = selectedArticleInventoryList[i].tva.tVACode,
                ordre = numOrder,
              //  lIGBonEntreeNumOrdre = numOrder.toString(),
                lIGBonEntreeNumOrdre = "0.0",
                lIGBonEntreeDDm = selectedDateTime,
                lIGBonEntreeNumBonM = codeM,
                lIGBonEntreeRemise = selectedArticleInventoryList[i].discount.ifEmpty { "0.0" },
                lIGBonEntreeRemise2 = selectedArticleInventoryList[i].mntDiscount, // todo verify where to put mntDiscount
                lIGBonEntreeMntTva = selectedArticleInventoryList[i].mntTva,
                lIGBonEntreeMntTTC = selectedArticleInventoryList[i].lTMtTTC,
                lIGBonEntreeMntBrutHT = selectedArticleInventoryList[i].lTPuHT,
                lIGBonEntreePUTTC = selectedArticleInventoryList[i].lTPuTTC,
                lIGBonEntreeMntNetHt = selectedArticleInventoryList[i].lTMtNetHT,


                cMP = "0.0",
                cMPG = "0.0",
                coutCharge = "0.0",
                lIGBonEntreeFraisTansport = "0.0",
                lIGBonEntreeMntDc = "0.0",
                lIGBonEntreeMntFodec = "0.0",
                lIGBonEntreeQteGratuite = "0.0",
                lIGBonEntreeQtePiece = null,
                lIGBonEntreeQteRejete = "0.0",
                lIGBonEntreeQteVendu = "0.0",
                lIGBonEntreeTaux = "0.0",
                lIGBonEntreeTauxComp = "0.0",
                lIGBonEntreeTauxDc = "0.0",
                lIGBonEntreeTauxFodec = "0.0",
                lIGBonEntreeExport = "false",
                lIGMargeVente = "0.0",
                lIGPVENTEHT = "0.0",
                lIGPrixSite = "0.0",
                lIGPrixVentePub = "",
                lIGQteRetour = "0.0",
                lIGQteVendue = "0.0",
                lIGRetour = "false",
                lIGTauxEchange = "0.0",
                qteAStock = "0.0",
                qteDec = "0.0"
                )


            ligneBonEntree.isSync = false
            ligneBonEntree.status = ItemStatus.INSERTED.status

            saveLigneBnEntree(ligneBonEntree)
         //   val stationStockArticle = listStationStockArticl.firstOrNull { it.sARTCodeSatation == input.station.sTATCode && (it.sARTCodeArt == articl.aRTCode || it.sARTCodeArt == articl.aRTCodeBar)}?: StationStockArticle()
            val stationStockArticle = listStationStockArticl[articl.aRTCode + input.station.sTATCode]?: StationStockArticle()


            updateStationArticleQte(
                codeStation = input.station.sTATCode,
                quatity = stringToDouble(selectedArticleInventoryList[i].quantity),
                aRTCode = articl.aRTCode,
                stationStockArticle = stationStockArticle,
                updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                    updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)
                }
            )

            updateArticleQty(
                quantity = stringToDouble(selectedArticleInventoryList[i].quantity),
                article = articl,
                updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                    updateArtQteStock(
                       newQteAllStations,
                       newQteStation,
                       codeArticle
                    )
                }
            )
        }


    }





    fun handleAddAchatEvents(
        popBackStack: () -> Unit,
        validationAddEvents: ValidationAddNewEvent,
        context: Context,
        toaster: ToasterState,
        controlQuantity: Boolean,
        stateAddNew: InventoryFormState,
        selectedArticleList: List<SelectedArticle>,
        selectedArticle: SelectedArticle,
        utilisateur: Utilisateur,
        codeM: String,
        listActifTimber: List<Timbre>,
        selectedDateTime: String,
        isAutoScanMode: Boolean,
        exerciceList: List<Exercice>,
        listStationStockArticl: Map<String, StationStockArticle>,
        tvaList: List<Tva>,
        onBarCodeInfo: (BareCode) -> Unit,
        setSelectedArticl: (Article) -> Unit,
        addOneItemToSelectedArticleMobilityList: (SelectedArticle) -> Unit,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {

                when (validationAddEvents) {
                    is ValidationAddNewEvent.AddNew -> {
                        val input = validationAddEvents.addNew
                        showToast(
                            context = context,
                            toaster = toaster,
                            message = context.resources.getString(R.string.added_succesfully),
                            type =  ToastType.Success,
                        )
                        saveBonAchat(
                            exerciceList =  exerciceList,
                            selectedArticleList = selectedArticleList,
                            listStationStockArticl = listStationStockArticl,
                            input = input,
                            utilisateur = utilisateur,
                            listActifTimber = listActifTimber,
                            selectedDateTime = selectedDateTime,
                            codeM = codeM,
                            updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                                updateQtePerStation(
                                    newQteStation,
                                    newSartQteDeclare,
                                    codeArticle,
                                    codeStation
                                )
                            },
                            updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                                updateArtQteStock(
                                    newQteAllStations,
                                    newQteStation,
                                    codeArticle
                                )
                            }
                        )

                        popBackStack()
                    }

                    is ValidationAddNewEvent.AddNewNewLigne -> {
                        com.asmtunis.procaisseinventory.pro_inventory.utils.SelectedArticle.addNewLigneSelectedInventoryArtcle(
                            toaster = toaster,
                            context = context,
                            fromScan = false,
                            selectedArticle = selectedArticle,
                            setSelectedArticlInventory = {
                                setSelectedArticl(it.article)
                            },
                            addItemToSelectedArticleInventoryList = {
                                addOneItemToSelectedArticleMobilityList(it)
                            },
                            resetBarCode = {
                                onBarCodeInfo(BareCode())
                            },
                            isAutoScanMode = isAutoScanMode,
                            tva = if (stateAddNew.tva == Tva()) tvaList.first()
                            else stateAddNew.tva,

                            controlQuantity = controlQuantity
                        )

                    }

                }


    }



    fun setSelectedArticleList(
        listLigneBonEntree: List<LigneBonEntree>,
        tvaList: List<Tva>,
        articleMap: Map<String, Article>,
        setConsultationSelectedArticleMobilityList: (SelectedArticle) -> Unit
    ) {
        for (ligneBonEntree in listLigneBonEntree) {

            val tva = tvaList.firstOrNull { it.tVACode == ligneBonEntree.lIGBonEntreeTVA } ?: Tva(tVACode = ligneBonEntree.lIGBonEntreeTVA ?: "")
            val article = articleMap[ligneBonEntree.lIGBonEntreeCodeArt] ?: Article(aRTCode = ligneBonEntree.lIGBonEntreeCodeArt)

            val prixVente = ligneBonEntree.lIGBonEntreePUTTC
            val lTMtBrutHT = ligneBonEntree.lIGBonEntreePUHT?: "0.0"

            val mntTva = if(stringToDouble(ligneBonEntree.lIGBonEntreeMntTva) == 0.0) (stringToDouble(prixVente) - stringToDouble(lTMtBrutHT)).toString() else ligneBonEntree.lIGBonEntreeMntTva
            val seletedArt =
                SelectedArticle(
                    article = article,
                    tva = tva,
                    mntTva = mntTva,
                    quantity = ligneBonEntree.lIGBonEntreeQte!!,
                    prixCaisse = prixVente,
                    discount = ligneBonEntree.lIGBonEntreeRemise,
                    mntDiscount = ligneBonEntree.lIGBonEntreeRemise2 ?: "0.0",
                    lTMtTTC = ligneBonEntree.lIGBonEntreeMntTTC,
                    lTMtBrutHT = ligneBonEntree.lIGBonEntreePUHT?: "0.0",
                    prixVente = prixVente
                )

            setConsultationSelectedArticleMobilityList(seletedArt)

        }
    }
}
