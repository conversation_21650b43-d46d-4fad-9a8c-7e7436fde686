package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import com.simapps.ui_kit.utils.getCurrentDateInMillis
import com.simapps.ui_kit.utils.getCurrentTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
    class TicketRayonViewModel @Inject constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val proInventoryRemote: ProInventoryRemote,
        private val proInventoryLocalDb: ProInventoryLocalDb,
        private val proCaisseLocalDb: ProCaisseLocalDb
    ) : ViewModel() {

        // Initialize the state list before the init block
        private val _ticketRayonList = mutableStateListOf<TicketRayon>()
        val ticketRayonList: List<TicketRayon> = _ticketRayonList

        init {
            getTicketRayonList()
        }

        var showBottomSheet by mutableStateOf(false)
            private set
        fun bottomSheetVisibility(value: Boolean) {
            showBottomSheet = value
        }

        var selectedTicketRayon by mutableStateOf(TicketRayon())
            private set
        fun onSelectedTicketRayonChange(value: TicketRayon) {
            selectedTicketRayon = value
        }

        fun saveTicketRayon(ticketRayon: TicketRayon) {
            viewModelScope.launch(dispatcher) {
                proInventoryLocalDb.ticketRayon.upsert(ticketRayon)
            }
        }

        fun deleteTicketRayon(ticketRayon: TicketRayon) {
            viewModelScope.launch(dispatcher) {
                proInventoryLocalDb.ticketRayon.delete(ticketRayon)
            }
        }
        fun deleteAllTicketRayon() {
            viewModelScope.launch(dispatcher) {
                proInventoryLocalDb.ticketRayon.deleteAll()
            }
        }

        fun getTicketRayonList() {
            viewModelScope.launch {
                proInventoryLocalDb.ticketRayon.getAll().collect { newList ->
                    _ticketRayonList.clear()
                    _ticketRayonList.addAll(newList ?: emptyList())
                }
            }
        }




        fun addTicketRayon(article: Article) {
            val ticktRayon = TicketRayon(
                aRTCode = article.aRTCode,
                aRTDesignation = article.aRTDesignation,
                ddm = getCurrentTime(),
                artSync = "false",
                timestamp = getCurrentDateInMillis()
            )
            ticktRayon.isSync = false
            ticktRayon.status = ItemStatus.INSERTED.status
            saveTicketRayon(ticktRayon)

            bottomSheetVisibility(false)
        }
}