package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.enum_classes.EtatBonTransfert
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraisonWithArticle
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.ui.filter.BonTransfertListState
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.dokar.sonner.ToasterState
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class BonTransfertViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proInventoryRemote: ProInventoryRemote,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {

    var etatBnTransfertExpand by  mutableStateOf(false)
        private set
    fun onEtatBnTransfertExpandChange(value: Boolean) {
        etatBnTransfertExpand = value
    }



    var showFilterLine by mutableStateOf(false)
        private set
    fun onShowFilterLineChange(value: Boolean) {
        showFilterLine = value
    }


    var fiterValue by mutableStateOf("")
        private set
    fun onFilterValueChange(value: String) {
        fiterValue = value
    }

    var station by mutableStateOf(Station())
        private set

    fun onSelectedStationChange(value: Station) {
        station = value
    }

    var stationError: UiText? by mutableStateOf(null)
        private set

    fun onStationErrorChange(value: UiText?) {
        stationError = value
    }

    var etatBonTransfert by mutableStateOf(EtatBonTransfert.VALIDEE.value)
        private set

    fun onEtatBonTransfertChange(value: String) {
        etatBonTransfert = value
    }

    var stationDestination by mutableStateOf(Station())
        private set

    fun onStationDestinationChange(value: Station) {
        stationDestination = value
    }

    var stationDestinationError: UiText? by mutableStateOf(null)
        private set

    fun onStationDestinationErrorChange(value: UiText?) {
        stationDestinationError = value
    }


    var showSearchView: Boolean by mutableStateOf(false)
        private set

    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }

    var showCustomFilter: Boolean by mutableStateOf(false)
        private set

    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }

    var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
        private set

    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }


    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set

    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }


    var selectedLigneBonTransfert: LigneBonLivraison by mutableStateOf(LigneBonLivraison())
        private set

    fun onSelectedLigneBonTransfertChange(value: LigneBonLivraison) {
        selectedLigneBonTransfert = value
    }

    var selectedListLgBonTransfert = mutableStateListOf<LigneBonLivraison>()
        private set


    var selectedListLgBonTransfertWithArticle = mutableStateListOf<LigneBonLivraisonWithArticle>()
        private set
    var selectedBonTransfert: BonLivraison by mutableStateOf(BonLivraison())
        private set

    fun onSelectedBonTransfertChange(value: Map<BonLivraison, List<LigneBonLivraisonWithArticle>>) {
        restBonTransfert()

        value.forEach { (key, value) ->
            run {
                selectedBonTransfert = key
                selectedListLgBonTransfert.addAll(value.map { it.ligneTicket ?: LigneBonLivraison() })
                selectedListLgBonTransfertWithArticle.addAll(value)
            }
        }

    }


    fun deleteLigneBonTransfert(value: LigneBonLivraison) {
        // selectedListLgBonTransfert.remove(value)

        val indexToDelete =
            selectedListLgBonTransfert.indexOfFirst { it.lGBonTransCodeArt == value.lGBonTransCodeArt }
        val updatedList =
            selectedListLgBonTransfert.filterIndexed { index, _ -> index != indexToDelete }
                .mapIndexed { index, ligneBonTransfert ->
                    ligneBonTransfert.copy(
                        LG_BonTrans_NumOrdre = index + 1
                    )
                }

        selectedListLgBonTransfert.clear()
        selectedListLgBonTransfert.addAll(updatedList)
    }

    fun restBonTransfert() {
        station = Station()
        stationDestination = Station()
        selectedListLgBonTransfert.clear()
        selectedBonTransfert = BonLivraison()
    }

    fun getLigneBonTransfert(
        article: Article
    ) {
        selectedLigneBonTransfert =
            if (selectedListLgBonTransfert.any { it.lGBonTransCodeArt == article.aRTCode })
                selectedListLgBonTransfert.filter { it.lGBonTransCodeArt == article.aRTCode }[0]
            else LigneBonLivraison()
    }


    var bonTransfertListstate: BonTransfertListState by mutableStateOf(BonTransfertListState())
        private set











    fun saveBonTransfert(bonLivraison: BonLivraison) {
        viewModelScope.launch(dispatcher) {
            proInventoryLocalDb.bonLivraison.upsert(bonLivraison)
        }
    }

    fun saveListLigneBnTransfert(lgbonLivraison: List<LigneBonLivraison>) {
        viewModelScope.launch(dispatcher) {
            proInventoryLocalDb.ligneBonLivraison.upsertAll(lgbonLivraison)
        }
    }

    fun saveLigneBnTransfert(lgbonLivraison: LigneBonLivraison) {
        viewModelScope.launch(dispatcher) {
            proInventoryLocalDb.ligneBonLivraison.upsert(lgbonLivraison)
        }
    }

    fun onEvent(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (bonTransfertListstate.listOrder::class == event.listOrder::class &&
                    bonTransfertListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                bonTransfertListstate = bonTransfertListstate.copy(
                    listOrder = event.listOrder
                )
                filterBonTransfert(bonTransfertListstate)
            }

            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()

            is ListEvent.ListSearch -> {
                bonTransfertListstate = bonTransfertListstate.copy(
                    search = event.listSearch
                )

                filterBonTransfert(bonTransfertListstate)
            }

            is ListEvent.FirstCustomFilter -> {
                bonTransfertListstate = bonTransfertListstate.copy(
                    filterByStationSource = event.firstFilter
                )

                filterBonTransfert(bonTransfertListstate)
            }

            is ListEvent.SecondCustomFilter -> {
                bonTransfertListstate = bonTransfertListstate.copy(
                    filterByStationDestination = event.secondFiter
                )

                filterBonTransfert(bonTransfertListstate)
            }

            is ListEvent.ThirdCustomFilter -> {
                bonTransfertListstate = bonTransfertListstate.copy(
                    filterByEtatBnTransfert = event.thirdFilter
                )

                filterBonTransfert(bonTransfertListstate)
            }
        }

    }

    // var getBonTransfertJob: Job = Job()
    fun filterBonTransfert(bonTransfertListState: BonTransfertListState) {
        val searchedText = searchTextState.text
        val searchValue = bonTransfertListState.search
        val filterByStationSource = bonTransfertListState.filterByStationSource
        val filterByStationDestination = bonTransfertListState.filterByStationDestination
        val filterByEtatBnTransfert = bonTransfertListState.filterByEtatBnTransfert

        // getBonTransfertJob.cancel()

        if (searchedText.isEmpty()) {
            /*  getBonTransfertJob =*/ when (bonTransfertListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (bonTransfertListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proInventoryLocalDb.bonLivraison.getAllFiltred(
                                isAsc = 1,
                                sortBy = "BON_Trans_Num",
                                filterByStationSource = filterByStationSource,
                                filterByStationDestination = filterByStationDestination,
                                filterByEtatBnTransfert = filterByEtatBnTransfert,

                                ).collect {
                                setBonTransfertList(bonLivraison = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proInventoryLocalDb.bonLivraison.getAllFiltred(
                                isAsc = 1,
                                sortBy = "BON_Trans_Date",
                                filterByStationSource = filterByStationSource,
                                filterByStationDestination = filterByStationDestination,
                                filterByEtatBnTransfert = filterByEtatBnTransfert,

                                ).collect {
                                setBonTransfertList(bonLivraison = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proInventoryLocalDb.bonLivraison.getAllFiltred(
                                isAsc = 1,
                                sortBy = "BON_Trans_Mnt_TTC",
                                filterByStationSource = filterByStationSource,
                                filterByStationDestination = filterByStationDestination,
                                filterByEtatBnTransfert = filterByEtatBnTransfert,

                                ).collect {
                                setBonTransfertList(bonLivraison = it)
                            }
                        }
                    }
                }

                is OrderType.Descending -> {
                    when (bonTransfertListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proInventoryLocalDb.bonLivraison.getAllFiltred(
                                isAsc = 2,
                                sortBy = "BON_Trans_Num",
                                filterByStationSource = filterByStationSource,
                                filterByStationDestination = filterByStationDestination,
                                filterByEtatBnTransfert = filterByEtatBnTransfert,
                            ).collect {
                                setBonTransfertList(bonLivraison = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proInventoryLocalDb.bonLivraison.getAllFiltred(
                                isAsc = 2,
                                sortBy = "BON_Trans_Date",
                                filterByStationSource = filterByStationSource,
                                filterByStationDestination = filterByStationDestination,
                                filterByEtatBnTransfert = filterByEtatBnTransfert,

                                ).collect {
                                setBonTransfertList(bonLivraison = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proInventoryLocalDb.bonLivraison.getAllFiltred(
                                isAsc = 2,
                                sortBy = "BON_Trans_Mnt_TTC",
                                filterByStationSource = filterByStationSource,
                                filterByStationDestination = filterByStationDestination,
                                filterByEtatBnTransfert = filterByEtatBnTransfert,

                                ).collect {
                                setBonTransfertList(bonLivraison = it)
                            }
                        }
                    }
                }
            }
        } else {
            if (searchedText.isNotEmpty()) {
                if (searchValue is ListSearch.FirstSearch) {
                    /*    getBonTransfertJob = */when (bonTransfertListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (bonTransfertListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByBontransfertNum(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Num",
                                        isAsc = 1,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByBontransfertNum(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Date",
                                        isAsc = 1,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByBontransfertNum(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Mnt_TTC",
                                        isAsc = 1,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,
                                    ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (bonTransfertListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByBontransfertNum(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Num",
                                        isAsc = 2,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByBontransfertNum(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Date",
                                        isAsc = 2,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByBontransfertNum(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Mnt_TTC",
                                        isAsc = 2,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.SecondSearch) {
                    /*  getBonTransfertJob = */ when (bonTransfertListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (bonTransfertListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationDestination(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Num",
                                        isAsc = 1,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationDestination(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Date",
                                        isAsc = 1,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,
                                    ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationDestination(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Mnt_TTC",
                                        isAsc = 1,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,
                                    ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (bonTransfertListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationDestination(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Num",
                                        isAsc = 2,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,
                                    ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationDestination(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Date",
                                        isAsc = 2,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,
                                    ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationDestination(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Mnt_TTC",
                                        isAsc = 2,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.ThirdSearch) {
                    /*   getBonTransfertJob =*/  when (bonTransfertListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (bonTransfertListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationSource(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Num",
                                        isAsc = 1,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationSource(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Date",
                                        isAsc = 1,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationSource(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Mnt_TTC",
                                        isAsc = 1,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (bonTransfertListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationSource(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Num",
                                        isAsc = 2,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,
                                    ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationSource(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Date",
                                        isAsc = 2,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proInventoryLocalDb.bonLivraison.filterByCodeStationSource(
                                        searchString = searchedText,
                                        sortBy = "BON_Trans_Mnt_TTC",
                                        isAsc = 2,
                                        filterByStationSource = filterByStationSource,
                                        filterByStationDestination = filterByStationDestination,
                                        filterByEtatBnTransfert = filterByEtatBnTransfert,

                                        ).collect {
                                        setBonTransfertList(bonLivraison = it)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    private fun setBonTransfertList(bonLivraison: Map<BonLivraison, List<LigneBonLivraisonWithArticle>>) {

        bonTransfertListstate = bonTransfertListstate.copy(
            lists = emptyMap() // ,
            // listOrder = articlesListState.listOrder
        )

        bonTransfertListstate = bonTransfertListstate.copy(
            lists = bonLivraison // ,
            // listOrder = articlesListState.listOrder
        )

        //   getBonTransfertJob.cancel()
    }



    fun deleteBonTransfert(
        bonLivraison: BonLivraison,
        listLgBonLivraisonWithArticle: List<LigneBonLivraisonWithArticle>,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit,
        insertStationStockArticle: (stationStockArticl: StationStockArticle) -> Unit,
    ) {

        viewModelScope.launch(dispatcher) {
            proInventoryLocalDb.bonLivraison.delete(bonLivraison)
            proInventoryLocalDb.ligneBonLivraison.deleteList(listLgBonLivraisonWithArticle.map {
                it.ligneTicket ?: LigneBonLivraison()
            })

            val stationSource = bonLivraison.bONTransStatSource?: ""
            for (lgBonLivraisonArticle in listLgBonLivraisonWithArticle) {
                val lgBonLivraison = lgBonLivraisonArticle.ligneTicket!!
                // val article = /*lgBonLivraisonArticle.article?:*/ Article()
                val article = lgBonLivraisonArticle.article?: Article()

                val codeArt = lgBonLivraison.lGBonTransCodeArt
                val qteTransfert = lgBonLivraison.qteTransfert


                val stationStockArticle = lgBonLivraisonArticle.stationArticle?.firstOrNull { it.sARTCodeSatation == stationSource }?: StationStockArticle()
                val stationDestinationStockArticle = lgBonLivraisonArticle.stationArticle?.firstOrNull { it.sARTCodeSatation == bonLivraison.bONTransStatDest } ?: StationStockArticle()

                ArticleOpeartions.updateStationArticleQte(
                    opeartion = Globals.ADD,
                    codeStation = stationSource,
                    quatity = qteTransfert,
                    aRTCode = codeArt,
                    stationStockArticle = stationStockArticle,
                    updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                        updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)
                    }
                )



                ArticleOpeartions.updateStationDestinationArticleQte(
                    opeartion = Globals.MINUS,
                    sTATCode = bonLivraison.bONTransStatDest?: "0.0",
                    quatity = qteTransfert,
                    aRTCode = codeArt,
                    stationStockArticle = stationStockArticle,
                    stationDestinationStockArticle = stationDestinationStockArticle,
                    insertStationStockArticle = { stationStockArticl ->
                        insertStationStockArticle(stationStockArticl)
                    },
                    updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                        updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)
                    }

                )



                ArticleOpeartions.updateArticleQty(
                    quantity = qteTransfert,
                    article = article,
                    updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                        updateArtQteStock(
                            newQteAllStations,
                            newQteStation,
                            codeArticle
                        )
                    }
                )


            }
        }
    }


    fun saveBonTransfer(
        toaster: ToasterState,
        message: String,
        popBackStack: () -> Unit,
        selectedArticleInventoryList: List<SelectedArticle>,
        codeM: String,
        listStationStockArticl: Map<String, StationStockArticle>,
        exerciceList: List<Exercice>,
        selectedDateTime: String,
        utilisateur: Utilisateur,
        station: Station,
        stationDestination: Station,
        insertStationStockArticle: (stationStockArticl: StationStockArticle) -> Unit,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit,
    ) {
        viewModelScope.launch {


            val currentDate = getCurrentDateTime()
            if(selectedArticleInventoryList.isNotEmpty() && station != Station() && stationDestination != Station()) {


                val userId = utilisateur.codeUt


              /*  showToast(
                    context = context,
                    toaster = toaster,
                    message = message,
                    type =  ToastType.Success,
                )*/

                //   val input = event.addNew

                val bonTransfert = BonLivraison(
                    //     id = 0,
                    bONTransNum = codeM,
                    bONTransNumM = codeM,
                    bONTransExerc = exerciceList.firstOrNull()?.exerciceCode ?: "2024",
                    bONTransDate = selectedDateTime,
                    bONTransStatSource = station.sTATCode,
                    bONTransStatDest = stationDestination.sTATCode,
                    bONTransEtat = etatBonTransfert,
                    bONTransStat = null,
                    bONTransUser = userId,
                    bONTransExport = null,
                    bONTransDDm = selectedDateTime,
                    declaree = null,
                    exportation = null,
                    dDm = currentDate,
                    numManuelle = null,
                    observation = null,
                    bONTransEtatSaisie = null,
                    bONTransMntHT = null,
                    bONTransMntTTC = null,
                    bONTransTransporteur = null,
                    bONTransVehicule = Globals.BT,
                    bONTransObs = null,
                    bONENTSYNC =  "0",
                    timestamp = 0
                )
//TODO UPDATE ARTICLE QTE LOCALLY


                bonTransfert.isSync = false
                bonTransfert.status = ItemStatus.INSERTED.status

                saveBonTransfert(bonTransfert)
                //  bonTransfertViewModel.saveListLigneBnTransfert(bonTransfertViewModel.selectedListLgBonTransfert)

                saveListLgBonTransfert(
                    codeM = codeM,
                    currentDate = currentDate,
                    etatBonTransfert = etatBonTransfert,
                    exerciceList = exerciceList,
                    listStationStockArticl = listStationStockArticl,
                    station = station,
                    stationDestination = stationDestination,
                    selectedArticleInventoryList = selectedArticleInventoryList,
                    insertStationStockArticle = { stationStockArticl ->
                        insertStationStockArticle(stationStockArticl)
                    },
                    updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                        updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)
                    },
                    updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                        updateArtQteStock(
                            newQteAllStations,
                            newQteStation,
                            codeArticle
                        )
                    }
                )


                popBackStack()
            }



        }
    }







    private fun saveListLgBonTransfert(
        codeM: String,
        etatBonTransfert: String,
        currentDate: String,
        selectedArticleInventoryList: List<SelectedArticle>,
        exerciceList: List<Exercice>,
        listStationStockArticl: Map<String, StationStockArticle>,
        station: Station,
        stationDestination: Station,
        insertStationStockArticle: (stationStockArticl: StationStockArticle) -> Unit,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit,
    ){
        for (i in selectedArticleInventoryList.indices) {
            val articl = selectedArticleInventoryList[i].article
            val ligneBonLivraison = LigneBonLivraison(
                lGBonTransNumBon = codeM,
                lGBonTransCodeArt = articl.aRTCode,
                lGBonTransExerc = exerciceList.firstOrNull()?.exerciceCode ?: "2024",
                qteTransfert = stringToDouble(selectedArticleInventoryList[i].quantity),//TODO ASK WHATS THE DIFFRENCE BETWEN THOS AND qteDecTransferer
                qteDecTransferer = selectedArticleInventoryList[i].quantity,
                lGBonTransUnite = selectedArticleInventoryList[i].uniteArticle.uNITEARTICLECodeUnite.ifEmpty { articl.uNITEARTICLECodeUnite } ,
                lGBonTransPUHT = selectedArticleInventoryList[i].lTPuHT,
                lGBonTransSYNC = "0",
                lGBonTransStat = station.sTATCode,
                lGBonTransUser = null,
                lGBonTransExport = null,
                lGBonTransDDm = currentDate,
                lIGBonEntreeQtePiece = null,
                lGETAT = etatBonTransfert,
                export = null,
                dDm = currentDate,
                lGBonTransPACHATNHT = selectedArticleInventoryList[i].prixAchatHt,
                lGBonTransTVA = null,
                lGBonTransMNTHT = selectedArticleInventoryList[i].prixCaisse,
                lGBonTransMNTTC = articl.prixSolde,
                lGBonTransStatDest = stationDestination.sTATCode,
                LG_BonTrans_NumOrdre = i + 1
            )


            ligneBonLivraison.isSync = false
            ligneBonLivraison.status = ItemStatus.INSERTED.status

             saveLigneBnTransfert(ligneBonLivraison)


          //  runBlocking {
               // launch {

         //   val stationStockArticle = listStationStockArticl.filter { it.value.sARTCodeSatation == input.station.sTATCode}[articl.aRTCode]?: StationStockArticle()

            val stationStockArticle = listStationStockArticl[articl.aRTCode + station.sTATCode]?: StationStockArticle()//   .filter { it.value.sARTCodeSatation == station.sTATCode }[articl.aRTCode]?: StationStockArticle()
            val stationDestinationStockArticle = listStationStockArticl[articl.aRTCode + stationDestination.sTATCode]?: StationStockArticle()//.filter { it.value.sARTCodeSatation == stationDestination.sTATCode }[articl.aRTCode]?: StationStockArticle()
// val stationStockArticle = listStationStockArticl.firstOrNull { it.sARTCodeSatation == station.sTATCode && it.sARTCodeArt == articl.aRTCode }?: StationStockArticle()
//            val stationDestinationStockArticle = listStationStockArticl.firstOrNull { it.sARTCodeSatation == stationDestination.sTATCode && it.sARTCodeArt == articl.aRTCode }?: StationStockArticle()

            ArticleOpeartions.updateStationArticleQte (
                        opeartion = Globals.MINUS,
                        codeStation = station.sTATCode,
                        quatity = stringToDouble(selectedArticleInventoryList[i].quantity),
                        aRTCode = articl.aRTCode,
                        stationStockArticle = stationStockArticle,
                        updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                            updateQtePerStation(
                                newQteStation,
                                newSartQteDeclare,
                                codeArticle,
                                codeStation
                            )
                        }
                    )


                    ArticleOpeartions.updateStationDestinationArticleQte(
                        opeartion = Globals.ADD,
                        sTATCode = stationDestination.sTATCode,
                        quatity = stringToDouble(selectedArticleInventoryList[i].quantity),
                        aRTCode = articl.aRTCode,
                        stationDestinationStockArticle = stationDestinationStockArticle,
                        stationStockArticle = stationStockArticle,
                        insertStationStockArticle = { stationStockArticl ->
                            insertStationStockArticle(stationStockArticl)
                        },
                        updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                            updateQtePerStation(
                                newQteStation,
                                newSartQteDeclare,
                                codeArticle,
                                codeStation
                            )
                        }
                    )


                    ArticleOpeartions.updateArticleQty(
                        operation = Globals.MINUS,
                        quantity = stringToDouble(selectedArticleInventoryList[i].quantity),
                        article = articl,
                        updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                            updateArtQteStock(
                                newQteAllStations,
                                newQteStation,
                                codeArticle
                            )
                        }
                    )



        }

    }

}