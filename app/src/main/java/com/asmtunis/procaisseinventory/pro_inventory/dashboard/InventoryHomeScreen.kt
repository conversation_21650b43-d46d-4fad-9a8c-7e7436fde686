package com.asmtunis.procaisseinventory.pro_inventory.dashboard

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.DashboardNavigation
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel

@Composable
fun InventoryHomeScreen(
    navigate: (route: Any) -> Unit,
    navDrawerViewModel: NavigationDrawerViewModel,
    networkErrorsVM: NetworkErrorsViewModel,
    dataViewModel: DataViewModel,
    mainViewModel: MainViewModel,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    settingViewModel: SettingViewModel
) {
    val context = LocalContext.current
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val utilisateur = mainViewModel.utilisateur

    // Initialize any necessary data
    LaunchedEffect(key1 = utilisateur) {
        mainViewModel.onSelectedBaseconfigChange(baseConfig = selectedBaseconfig)
    }

    // Use the new DashboardNavigation with colorful cards
    DashboardNavigation(
        navigate = { navigate(it) },
        navDrawerViewmodel = navDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel,
        onLogoutClick = {
            // Navigate to logout/login screen
            navigate(com.asmtunis.procaisseinventory.core.navigation.LoginRoute)
        },
        syncCompletionViewModel = null
    )
}
