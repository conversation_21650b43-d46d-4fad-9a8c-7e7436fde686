package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens

import NavDrawer
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.DeleteSweep
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.BareCodeScannerIcon
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.utils.getCurrentTime
import kotlinx.coroutines.launch

@ExperimentalMaterial3Api
@Composable
fun TicketRayonScreen(
    navigate: (route: Any) -> Unit,
    navDrawerViewmodel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    ticketRayonViewModel: TicketRayonViewModel,
    mainViewModel: MainViewModel,
    barCodeViewModel: BarCodeViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    sunmiPrintManager: SunmiPrintManager,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    settingViewModel: SettingViewModel
) {
    val uiWindowState = settingViewModel.uiWindowState

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

val selectedTicketRayon = ticketRayonViewModel.selectedTicketRayon

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData


    val proInventoryAuthorizationList = getProInventoryDataViewModel.authorizationList

    val haveTicketRayonAuthorisation = proInventoryAuthorizationList.any { it.AutoCodeAu == AuthorizationValuesProInventory.TICKET_RAYON_DRAWER_ITEM }

    val listState = rememberLazyListState()
    val ticketRayonList = ticketRayonViewModel.ticketRayonList

    val barCodeInfo = barCodeViewModel.barCodeInfo
    val firstTimeConnected = printViewModel.firstTimeConnected

    val isVisible = floatingBtnIsVisible(
        listeSize = ticketRayonList.size,
        canScrollForward = listState.canScrollForward
    )

    LaunchedEffect(key1 = Unit) {
        if(printViewModel.proccedPrinting && printViewModel.deviceAddress.isNotEmpty()) {
            PrintFunctions.print(
                context = context,
                toaster = toaster,
                printParams = printParams,
                navigate = { navigate(it) },
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                sunmiPrintManager = sunmiPrintManager,
                toPrintBT = {
                    printViewModel.printTicketRayon(
                        context = context,
                        printParams = printParams,
                        article =  articleMapByBarCode[selectedTicketRayon.aRTCode] ?: Article()
                    )
                },
                toPrintWifi = {
                    // TODO: Add WiFi printing for Ticket Rayon
                },
                toPrintSunmi = {
                    // TODO: Add Sunmi printing for Ticket Rayon
                }
            )
        }
    }
    LaunchedEffect(key1 = barCodeInfo) {

        //TODO SEE WHY THIS BLOCK EXECUTE WHEN ROTATE SCREEN THEN REMOVE CHECH IF == ""
        if (barCodeInfo.value == "") return@LaunchedEffect

         val scannedArticle = articleMapByBarCode.values.firstOrNull { it.aRTCodeBar == barCodeInfo.value }


        if (scannedArticle == null) {

            showToast(
                context = context,
                toaster = toaster,
                message = context.resources.getString(
                    R.string.article_introvable,
                    barCodeInfo.value
                ),
                type =  ToastType.Error,
            )
            barCodeViewModel.onBarCodeInfo(barCode = BareCode())
            return@LaunchedEffect
        }

        if (ticketRayonList.any { it.aRTCode == scannedArticle.aRTCode }) {

            showToast(
                context = context,
                toaster = toaster,
                message = context.resources.getString(
                    R.string.existe_deja,
                    barCodeInfo.value
                ),
                type =  ToastType.Info,
            )
            barCodeViewModel.onBarCodeInfo(barCode = BareCode())
            return@LaunchedEffect
        }



        val ticketRayon = TicketRayon(
            aRTCode = scannedArticle.aRTCode,
            aRTDesignation = scannedArticle.aRTDesignation,
            ddm = getCurrentTime(),
            artSync = "false",
            timestamp = 0
        )
        ticketRayon.isSync = false
        ticketRayon.status = ItemStatus.INSERTED.status

        ticketRayonViewModel.saveTicketRayon(ticketRayon)
        barCodeViewModel.onBarCodeInfo(barCode = BareCode())

    }

    LaunchedEffect(key1 = firstTimeConnected) {
        if (!firstTimeConnected) return@LaunchedEffect

        // Direct printing without note functionality
        when {
            printParams.useSunmiPrinter -> {
                // TODO: Add Sunmi printing for Ticket Rayon
            }
            printParams.printViaWifi -> {
                // TODO: Add WiFi printing for Ticket Rayon
            }
            else -> {
                // Bluetooth printing
                printViewModel.printTicketRayon(
                    context = context,
                    printParams = printParams,
                    article = articleMapByBarCode[selectedTicketRayon.aRTCode] ?: Article()
                )
            }
        }

        ticketRayonViewModel.bottomSheetVisibility(false)
        printViewModel.onFirstTimeConnectedChange(firstConnect = false)
    }

    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navDrawerViewmodel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncProcaisseViewModels = syncProcaisseViewModels,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            topBar = {
                AppBar(
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = {
                        // Navigate to shortcuts screen instead of opening drawer
                        navigate(com.asmtunis.procaisseinventory.core.navigation.HomePageRoute)
                    },
                    title = stringResource(id = navDrawerViewmodel.proInventorySelectedMenu.title),
                    actions = {
                        if (ticketRayonList.isEmpty()) return@AppBar

                        Icon(
                                imageVector = Icons.TwoTone.DeleteSweep,
                                contentDescription = stringResource(id = R.string.delete),
                                modifier = Modifier.clickable {
                                    mainViewModel.onShowAlertDialogChange(true)

                                },
                                tint = MaterialTheme.colorScheme.error
                            )
                    }
                )
            },
            floatingActionButton = {
                val density = LocalDensity.current
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp) ,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                AnimatedVisibility(
                    visible = isVisible,
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {
                    FloatingActionButton(onClick = {
                    }) {
                        BareCodeScannerIcon(
                            haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                            toaster = toaster,
                            onClick = {
                                openBareCodeScanner(
                                    navigate = { navigate(it) },
                                    onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                                )
                            }
                        )
                    }
                }

                    SnapScrollingButton(
                        isScrollInProgress = listState.isScrollInProgress,
                        isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15 && isVisible,
                        density = density,
                        animateScrollToItem = {
                            listState.animateScrollToItem(index = it)
                        }
                    )
            }
            }
        ) { padding ->

            if (printViewModel.openPrintInfoDialogue)
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )

            if (ticketRayonViewModel.showBottomSheet) {
                CustomModalBottomSheet(
                    title = selectedTicketRayon.aRTDesignation,
                    status = selectedTicketRayon.status,
                    remoteResponseState = syncInventoryViewModel.responseAddTicketRayonState,
                    showDeleteIcon = true,
                    onDismissRequest = {
                        ticketRayonViewModel.bottomSheetVisibility(false)
                    },
                    onDeleteRequest = {
                        ticketRayonViewModel.deleteTicketRayon(selectedTicketRayon)
                        ticketRayonViewModel.bottomSheetVisibility(false)
                    },
                    onPrintRequest = {
                        PrintFunctions.print(
                            context = context,
                            toaster = toaster,
                            printParams = printParams,
                            navigate = { navigate(it) },
                            printViewModel = printViewModel,
                            bluetoothVM = bluetoothVM,
                            sunmiPrintManager = sunmiPrintManager,
                            toPrintBT = {
                                printViewModel.printTicketRayon(
                                    context = context,
                                    printParams = printParams,
                                    article =  articleMapByBarCode[selectedTicketRayon.aRTCode] ?: Article()
                                )
                            },
                            toPrintWifi = {
                                // TODO: Add WiFi printing for Ticket Rayon
                            },
                            toPrintSunmi = {
                                // TODO: Add Sunmi printing for Ticket Rayon
                            }
                        )


                        ticketRayonViewModel.bottomSheetVisibility(false)

                    },
                    onSyncRequest = {
                        syncInventoryViewModel.syncTicketRayon(selectedTickRayon = selectedTicketRayon)
                    }
                    )


            }

            CustomAlertDialogue(
                title = context.getString(R.string.delete),
                msg = context.getString(R.string.confirm_remove_all_list_data),
                openDialog = mainViewModel.showAlertDialog,
                setDialogueVisibility = {
                    mainViewModel.onShowAlertDialogChange(it)
                },
                customAction = {
                    ticketRayonViewModel.deleteAllTicketRayon()
                },
                confirmText = stringResource(id = R.string.oui),
                cancelText = stringResource(id = R.string.non),
            )
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    //.customWidth(LocalConfiguration.current, 0.85f)
                    .fillMaxSize()
                    .padding(padding),
            ) {

                if (ticketRayonList.isNotEmpty())
                    TicketRayonList(
                        ticketRayonViewModel = ticketRayonViewModel,
                        articleMapByBarCode = articleMapByBarCode,
                        ticketRayonList = ticketRayonList,
                        haveTicketRayonAuthorisation = haveTicketRayonAuthorisation,
                        listState = listState
                    )
                else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)

            }

        }
    }
}










