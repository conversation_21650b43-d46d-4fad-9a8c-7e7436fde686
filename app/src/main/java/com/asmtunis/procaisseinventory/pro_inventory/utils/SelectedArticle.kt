package com.asmtunis.procaisseinventory.pro_inventory.utils

import android.content.Context
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState

object SelectedArticle {

    fun addNewLigneSelectedInventoryArtcle(
        context: Context,
        toaster: ToasterState,
        fromScan: Boolean = true,
        isAutoScanMode : Boolean,
        station : Station = Station(),
        stationStockArticl: Map<String, StationStockArticle> = emptyMap(),
        selectedArticle: SelectedArticle,
        setSelectedArticlInventory : (SelectedArticle) -> Unit,
        addItemToSelectedArticleInventoryList : (SelectedArticle) -> Unit,
        resetBarCode : (BareCode) -> Unit,
        tva: Tva,
        controlQuantity : Boolean
    ) {
    val article = selectedArticle.article
    val stationStockArticleList = selectedArticle.stationStockArticleList
    val aRTQteStock = stringToDouble(article.aRTQteStock)


        if (isAutoScanMode && fromScan) {
            setSelectedArticlInventory(
                selectedArticle.copy(
                    prixAchatHt = selectedArticle.prixAchatHt.ifEmpty { article.aRTPrixUnitaireHT },
                    tva = tva,
                    quantity = if (selectedArticle.quantity.isNotEmpty()) (selectedArticle.quantity + 1)
                    else ArticleOpeartions.getArticleQt(
                        controlQuantity = controlQuantity,
                        quantityInStock = aRTQteStock
                    ).toString(),
                    qteStationFromDB = stationStockArticl[article.aRTCode+ station.sTATCode]?.sARTQteStation?: ""
                )
            )
        }
        else {
            setSelectedArticlInventory(
                selectedArticle.copy(
                    tva = tva,
                    prixAchatHt = selectedArticle.prixAchatHt.ifEmpty { stringToDouble(article.aRTPrixUnitaireHT).toString() },//prixState.toDouble(),
                   // quantity = selectedArticle.quantity.ifEmpty { "1.0" }
                    qteStationFromDB = stationStockArticl[article.aRTCode+ station.sTATCode]?.sARTQteStation?: ""
                )
            )
        }





        val stationStock = stringToDouble(selectedArticle.qteStationFromDB.ifEmpty { "0" })
        if (stringToDouble(selectedArticle.quantity) > stationStock && controlQuantity) {
            showToast(
                context = context,
                toaster = toaster,
                message = context.resources.getString(R.string.error_qty) + "\n "+ context.resources.getString(
                    R.string.stock_qte_is, "${selectedArticle.qteStationFromDB.ifEmpty { "0" }} ${selectedArticle.article.uNITEARTICLECodeUnite}"),
                type =  ToastType.Error,
            )
            return
        }

        addItemToSelectedArticleInventoryList(selectedArticle)

        showToast(
            context = context,
            toaster = toaster,
            message = context.resources.getString(R.string.cd_add) + "\n "+ article.aRTDesignation,
            type =  ToastType.Error,
        )
        resetBarCode(BareCode())


    }
}