package com.asmtunis.procaisseinventory.pro_inventory.text_validation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateArticleQuantity
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateArticleQuantityByStation
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateDoubleNotZero
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateEmail
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateList
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePassword
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePhoneNumber
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateStringNotEmpty
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import com.asmtunis.procaisseinventory.pro_inventory.utils.ActionInventory
import kotlinx.coroutines.launch


class InventoryTextValidationViewModel(
    private val validateIsNotEmptyString: ValidateStringNotEmpty = ValidateStringNotEmpty(),
    private val validateList: ValidateList = ValidateList(),
    private val validateEmail: ValidateEmail = ValidateEmail(),
    private val validatePassword: ValidatePassword = ValidatePassword(),
    private val validatePhoneNumber: ValidatePhoneNumber = ValidatePhoneNumber(),
    private val validateDoubleNotZero: ValidateDoubleNotZero = ValidateDoubleNotZero(),
    private val validateArticleQuantity: ValidateArticleQuantity = ValidateArticleQuantity(),
    private val validateArticleQuantityByStation: ValidateArticleQuantityByStation = ValidateArticleQuantityByStation()
) : ViewModel() {
 

    var stateAddNew by mutableStateOf(InventoryFormState())
        private set

   // private val validationAddNewEventChannel = Channel<ValidationAddNewEvent>()
    //val validationAddEvents = validationAddNewEventChannel.receiveAsFlow()
    var validationAddEvents: ValidationAddNewEvent by mutableStateOf(ValidationAddNewEvent())
        private set


    fun resetVariable(){

        validationAddEvents = ValidationAddNewEvent()
        stateAddNew = stateAddNew.copy(
             typePiece = "",
         typePieceError= null,

         numPiece = "",
         numPieceError = null,


         fournisseur =  Fournisseur(),
         fournisseurError = null,

         station = Station(),
         stationError = null,

            stationDestination = Station(),
            stationDestinationError = null,

         listSelectedArticleInventory= emptyList(),
         listSelectedArticleError = null,



             quantite = "",
         quantiteError = null,

         prixAchatHT = "",
         prixAchatHTError = null,

         tva = Tva(),
         tvaError = null,



        )

    }

    fun resetAddNewLigneVariable(){
        stateAddNew = stateAddNew.copy(

            quantite = "",
            quantiteError = null,

            prixAchatHT = "",
            prixAchatHTError = null,

            tva = Tva(),
            tvaError = null,
        )

    }

    fun onAddNewEvent(event: InventoryFormEvent) {
        when (event) {



            InventoryFormEvent.SubmitAchat -> submitAddData(ActionInventory.ACHAT.type)
            InventoryFormEvent.SubmitBonTransfert -> submitAddData(ActionInventory.BON_TRANSFERT.type)
            InventoryFormEvent.SubmitInventaire -> submitAddData(ActionInventory.INVENTAIRE.type)
            InventoryFormEvent.SubmitAddNewLigneBnTransfert -> submitAddData(ActionInventory.ADD_NEW_LIGNE_BN_TRANSFERT.type)
            InventoryFormEvent.SubmitAddNewLigneAchat ->   submitAddData(ActionInventory.ADD_NEW_LIGNE_ACHAT.type)
            InventoryFormEvent.SubmitAddNewLigneInventaire -> submitAddData(ActionInventory.ADD_NEW_LIGNE_INVENTAIRE.type)

            is InventoryFormEvent.fournisseurChanged ->
                stateAddNew = stateAddNew.copy(fournisseur = event.fournisseur)
            is InventoryFormEvent.selectedListArticleChanged ->
                stateAddNew = stateAddNew.copy(listSelectedArticleInventory = event.listLigneBonEntree)
            is InventoryFormEvent.numPieceChanged ->
                stateAddNew = stateAddNew.copy(numPiece = event.numPiece)
            is InventoryFormEvent.stationChanged ->
                stateAddNew = stateAddNew.copy(station = event.station)
            is InventoryFormEvent.typePieceChanged ->
                stateAddNew = stateAddNew.copy(typePiece = event.typePiece)

            is InventoryFormEvent.stationDestinationChanged ->
                stateAddNew = stateAddNew.copy(stationDestination = event.stationDestination)


            is InventoryFormEvent.prixAchatHTChanged ->
                stateAddNew = stateAddNew.copy(prixAchatHT = event.prixAchatHT)
            is InventoryFormEvent.quantiteChanged ->
                stateAddNew = stateAddNew.copy(quantite = event.quantite)
            is InventoryFormEvent.tvaChanged ->
                stateAddNew = stateAddNew.copy(tva = event.tva)


            is InventoryFormEvent.selectedArticleChanged ->   stateAddNew = stateAddNew.copy(selectedArticle = event.article)
        }
    }

    private fun submitAddData(from : String) {
        val fournisseurResult = validateIsNotEmptyString.execute(stateAddNew.fournisseur.fRSCodef)

        val listSelectedArticleResult = validateList.execute(stateAddNew.listSelectedArticleInventory)
         val numPieceResult = validateIsNotEmptyString.execute(stateAddNew.numPiece)
        val stationResult = validateIsNotEmptyString.execute(stateAddNew.station.sTATCode)
        val stationDestinationResult = validateDoubleNotZero.execute(StringUtils.stringToDouble(stateAddNew.stationDestination.sTATCode))
        val typePieceResult = validateIsNotEmptyString.execute(stateAddNew.typePiece)

        val quantityStockResult = validateArticleQuantity.execute(stateAddNew.quantite, stateAddNew.selectedArticle)
        val quantityNotEmptyResult = validateIsNotEmptyString.execute(stateAddNew.quantite)
        val prixAchatHTResult = validateIsNotEmptyString.execute(stateAddNew.prixAchatHT)
        val tvaResult = validateIsNotEmptyString.execute(stateAddNew.tva.tVACode)

        val hasError = when (from) {
            ActionInventory.ACHAT.type -> listOf(
                fournisseurResult,
                listSelectedArticleResult,
                numPieceResult,
                stationResult,
                typePieceResult
            ).any { !it.successful }

            ActionInventory.INVENTAIRE.type -> {
                listOf(
                    listSelectedArticleResult,
                    stationResult
                ).any { !it.successful }
            }

            ActionInventory.BON_TRANSFERT.type ->
                listOf(
                    listSelectedArticleResult,
                    stationResult,
                    stationDestinationResult
                ).any { !it.successful }

            ActionInventory.ADD_NEW_LIGNE_ACHAT.type ->
                listOf(
                    quantityNotEmptyResult,
                prixAchatHTResult,
                tvaResult
            ).any { !it.successful }

            ActionInventory.ADD_NEW_LIGNE_BN_TRANSFERT.type ->
                listOf(
                    quantityStockResult,
                    prixAchatHTResult
                ).any { !it.successful }

            ActionInventory.ADD_NEW_LIGNE_INVENTAIRE.type ->
                listOf(
                    quantityNotEmptyResult,
                    prixAchatHTResult
                ).any { !it.successful }
            else -> {
                TODO()
            }
        }

        if (hasError) {
            when (from) {
                ActionInventory.ADD_NEW_LIGNE_ACHAT.type -> stateAddNew = stateAddNew.copy(
                    quantiteError = quantityNotEmptyResult.errorMessage,
                    prixAchatHTError = prixAchatHTResult.errorMessage,
                    tvaError = tvaResult.errorMessage,
                )
                ActionInventory.ADD_NEW_LIGNE_BN_TRANSFERT.type -> stateAddNew = stateAddNew.copy(
                    quantiteError = quantityStockResult.errorMessage,
                    prixAchatHTError = prixAchatHTResult.errorMessage
                )
                ActionInventory.ADD_NEW_LIGNE_INVENTAIRE.type -> stateAddNew = stateAddNew.copy(
                    quantiteError = quantityNotEmptyResult.errorMessage,
                    prixAchatHTError = prixAchatHTResult.errorMessage
                )
                else -> stateAddNew = stateAddNew.copy(

                    typePieceError= typePieceResult.errorMessage,

                    numPieceError = numPieceResult.errorMessage,


                    fournisseurError = fournisseurResult.errorMessage,

                    stationError = stationResult.errorMessage,

                    stationDestinationError = stationDestinationResult.errorMessage,

                    listSelectedArticleError = listSelectedArticleResult.errorMessage,


                    )
            }
            return
        }
        viewModelScope.launch {

            validationAddEvents = if(from == ActionInventory.ADD_NEW_LIGNE_ACHAT.type ||
                from == ActionInventory.ADD_NEW_LIGNE_BN_TRANSFERT.type||
                from == ActionInventory.ADD_NEW_LIGNE_INVENTAIRE.type)
                ValidationAddNewEvent.AddNewNewLigne(stateAddNew)
            else
                ValidationAddNewEvent.AddNew(stateAddNew)
        }
    }









}


